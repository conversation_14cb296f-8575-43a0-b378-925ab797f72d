FROM node:lts-alpine AS BUILD_IMAGE

WORKDIR /app

# Install dependencies
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile

COPY . .

## Build and cleanup
RUN npm run build

# remove development dependencies
RUN yarn install --production

FROM node:lts-alpine

# Working directory
WORKDIR /app

ENV NODE_ENV=production

# copy from build image
COPY --from=BUILD_IMAGE /app/dist ./dist
COPY --from=BUILD_IMAGE /app/public ./public
COPY --from=BUILD_IMAGE /app/node_modules ./node_modules
COPY package.json yarn.lock ./

# Execute moleculer-runner
ENTRYPOINT [ "node",  "./node_modules/moleculer/bin/moleculer-runner.js" ]
CMD [ "--config", "./dist/moleculer.config.js", "./dist/services/**/*.service.js" ]
