diff --git a/node_modules/fastest-validator/lib/rules/number.js b/node_modules/fastest-validator/lib/rules/number.js
index 5ac898c..de2b276 100644
--- a/node_modules/fastest-validator/lib/rules/number.js
+++ b/node_modules/fastest-validator/lib/rules/number.js
@@ -60,6 +60,16 @@ module.exports = function({ schema, messages }, path, context) {
 		`);
 	}
 
+  // Check enum value
+  if (schema.enum != null) {
+    const enumNum = JSON.stringify(schema.enum);
+    src.push(`
+			if (${enumNum}.indexOf(value) === -1) {
+				${this.makeError({ type: "numberEnum", expected: "\"" + schema.enum.join(", ") + "\"", actual: "origValue", messages })}
+			}
+		`);
+  }
+
 	// Check integer
 	if (schema.integer === true) {
 		src.push(`
