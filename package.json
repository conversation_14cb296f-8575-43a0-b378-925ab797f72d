{"name": "socketconnector", "version": "0.2.0", "description": "My Moleculer-based microservices project", "scripts": {"build": "tsc --build tsconfig.json", "dev": "ts-node ./node_modules/moleculer/bin/moleculer-runner.js --hot --repl --config src/moleculer.config.ts src/services/**/*.service.ts", "dev:nats": "TRANSPORTER=nats://localhost:4222 yarn dev", "dev:only": "ts-node ./node_modules/moleculer/bin/moleculer-runner.js --hot --repl --config src/moleculer.config.ts src/services/**/config.service.ts src/services/**/$SINGLE.service.ts", "start": "moleculer-runner --config ./dist/moleculer.config.js ./dist/services/**/*.service.js", "cli": "moleculer connect NATS", "ci": "jest --watch", "test": "jest --coverage", "lint": "eslint --ext .js,.ts .", "dc:up": "docker-compose up --build -d", "dc:logs": "docker-compose logs -f", "dc:down": "docker-compose down", "dev:watch": "ts-node src/dev.watch.ts --watch", "gen:broker:types": "ts-node src/gen.broker.types.ts", "postinstall:fixinstall": "ts-patch install #Only for fix gen:broker:types"}, "keywords": ["microservices", "moleculer"], "author": "", "devDependencies": {"@shelf/jest-mongodb": "^3.0.2", "@types/bcrypt": "^5.0.0", "@types/compression": "^1.7.2", "@types/jest": "^27.5.2", "@types/jsonwebtoken": "^8.5.8", "@types/lodash": "^4.14.182", "@types/mkdirp": "^1.0.2", "@types/mongoose": "5.11.97", "@types/node": "^13.13.52", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^2.26.0", "@typescript-eslint/parser": "^2.26.0", "eslint": "^6.8.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-prefer-arrow": "^1.2.2", "jest": "^29.5.0", "jest-cli": "^29.5.0", "moleculer-repl": "^0.7.3", "prettier": "^2.8.8", "ts-jest": "^27.1.5", "ts-node": "^10.9.1", "ts-patch": "^2.0.1", "ts-transformer-enumerate": "^0.5.2", "typescript": "^4.9.5"}, "dependencies": {"@typegoose/typegoose": "^8.3.0", "bcrypt": "^5.1.0", "cbor-x": "^1.5.3", "compression": "^1.7.4", "fastest-validator-decorators": "^1.3.0", "helmet": "^5.1.1", "ioredis": "^4.28.5", "jaeger-client": "^3.19.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mkdirp": "^3.0.1", "moleculer": "^0.14.34", "moleculer-db": "^0.8.26", "moleculer-db-adapter-mongoose": "^0.9.6", "moleculer-decorators": "^1.3.0", "moleculer-ts": "^2.0.6", "moment": "^2.30.1", "mongoose": "6.5.5", "mpath": "0.9.0", "nats": "^2.15.1", "socket.io": "^4.8.1", "socket.io-request": "^0.8.0", "uuid": "^10.0.0", "zca-js": "^2.0.0-beta.24"}, "engines": {"node": ">= 12.x.x"}}