'use strict';

import { ServiceBroker as TypedServiceBroker } from '../../../src/types/moleculer';
import TestService from '../../../src/services/user/user.service';
import * as Moleculer from 'moleculer';
import { ServiceBroker } from 'moleculer';
import { API_CODE, CommonErrors } from '../../../src/commons/error.helper';
import { STATUSES } from '../../../src/entities/base.entity';
import { User } from '../../../src/models/user';
import { getModelForClass } from '@typegoose/typegoose';
import { IUser, PERMISSIONS, USER_ROLE } from '../../../src/entities/user.entity';
import { CommonConfig } from '../../../src/commons/common.config';
import ValidationError = Moleculer.Errors.ValidationError;
import BadRequestError = CommonErrors.BadRequestError;

function getFakeConfigService() {
  return {
    name: 'config',
    actions: {
      actionGet: (): any => null,
    },
  };
}

function getFakeAPIService() {
  return {
    name: 'api',
    actions: {},
  };
}

const userOk = '<EMAIL>';
const userPending = '<EMAIL>';
const userBlock = '<EMAIL>';
const plainPass = 'Sample@123';
let res;

console.log('Mongo URL:', CommonConfig.DB_URI);

const TestModel = getModelForClass(User);

describe('Test init admin account', function () {
  let res;
  // @ts-ignore
  const broker: TypedServiceBroker = new ServiceBroker({ logger: false });

  // Dependencies service
  broker.createService(getFakeConfigService());
  broker.createService(getFakeAPIService());

  const service = new TestService(broker);

  service.config['users.signup.enabled'] = true;

  let token: string, user: IUser;

  beforeAll(async () => {});
  afterAll(async () => await broker.stop());
  it('should silent crash', async () => {
    // @ts-ignore
    jest.spyOn(service.adapter, 'insert').mockImplementation((...args) => {
      return Promise.reject('Mock error!');
    });

    await broker.start();

    expect(service.adapter.insert).toBeCalledTimes(1);
  });
});

describe("Test 'user' service", () => {
  describe('Test actions: actionRegister', () => {
    let res;
    // @ts-ignore
    const broker: TypedServiceBroker = new ServiceBroker({ logger: false });

    // Dependencies service
    broker.createService(getFakeConfigService());
    broker.createService(getFakeAPIService());

    const service = new TestService(broker);

    service.config['users.signup.enabled'] = true;

    const validEmail = '<EMAIL>';

    beforeAll(async () => {
      await broker.start();
    });
    afterAll(async () => await broker.stop());

    it('should register error missing require all fields', async () => {
      res = await broker
        .call('user.actionRegister', {})
        .then(() => {
          expect('Should not here!').toEqual(true);
        })
        .catch((error: ValidationError) => {
          expect(error).toBeInstanceOf(ValidationError);
          expect(error.code).toEqual(API_CODE.UNPROCESSABLE);
          const errorFields: string[] = error.data.map((value: any) => value.field);

          // 'password' will be check on pre save
          const expectErrorList = ['username'];
          expect(errorFields).toEqual(expect.arrayContaining(expectErrorList));
          expect(errorFields.length).toEqual(expectErrorList.length);
        });
    });

    it('should register username: invalid', async () => {
      res = await broker
        .call('user.actionRegister', {
          username: 'invalid',
        })
        .then(() => {
          expect('Should not here!').toEqual(true);
        })
        .catch((error: ValidationError) => {
          const errorFields: string[] = error?.data.map((value: any) => value.field);
          expect(errorFields).toEqual(expect.arrayContaining(['username']));
        });
    });

    it('should register username: invalid 2', async () => {
      res = await broker
        .call('user.actionRegister', {
          username: 'invalid@gmail',
        })
        .then(() => {
          expect('Should not here!').toEqual(true);
        })
        .catch((error: ValidationError) => {
          const errorFields: string[] = error?.data.map((value: any) => value.field);
          expect(errorFields).toEqual(expect.arrayContaining(['username']));
        });
    });

    it('should register username: invalid 3', async () => {
      res = await broker
        .call('user.actionRegister', {
          username: '@gmail.com',
        })
        .then(() => {
          expect('Should not here!').toEqual(true);
        })
        .catch((error: ValidationError) => {
          const errorFields: string[] = error?.data.map((value: any) => value.field);
          expect(errorFields).toEqual(expect.arrayContaining(['username']));
        });
    });

    it('should register username: invalid 4', async () => {
      res = await broker
        .call('user.actionRegister', {
          username: 'gmail.com',
        })
        .then(() => {
          expect('Should not here!').toEqual(true);
        })
        .catch((error: ValidationError) => {
          const errorFields: string[] = error?.data.map((value: any) => value.field);
          expect(errorFields).toEqual(expect.arrayContaining(['username']));
        });
    });
    it('should register username: valid', async () => {
      res = await broker
        .call('user.actionRegister', {
          username: validEmail,
        })
        .then(() => {
          expect('Should not here!').toEqual(true);
        })
        .catch((error: ValidationError) => {
          const errorFields: string[] = error?.data?.map((value: any) => value.field);
          expect(errorFields).not.toEqual(expect.arrayContaining(['username']));
        });
    });

    it('should register name: invalid', async () => {
      res = await broker
        .call('user.actionRegister', {
          name: '',
        })
        .then(() => {
          expect('Should not here!').toEqual(true);
        })
        .catch((error: ValidationError) => {
          const errorFields: string[] = error?.data.map((value: any) => value.field);
          expect(errorFields).toEqual(expect.arrayContaining(['name']));
        });
    });

    it('should register name: invalid object', async () => {
      res = await broker
        .call('user.actionRegister', {
          // @ts-ignore
          name: {
            test: 'any',
          },
        })
        .then(() => {
          expect('Should not here!').toEqual(true);
        })
        .catch((error: ValidationError) => {
          const errorFields: string[] = error?.data.map((value: any) => value.field);
          expect(errorFields).toEqual(expect.arrayContaining(['name']));
        });
    });

    it('should register name: valid', async () => {
      res = await broker
        .call('user.actionRegister', {
          name: 'anything',
        })
        .then(() => {
          expect('Should not here!').toEqual(true);
        })
        .catch((error: ValidationError) => {
          const errorFields: string[] = error?.data.map((value: any) => value.field);
          expect(errorFields).not.toEqual(expect.arrayContaining(['name']));
        });
    });

    it('should register password: invalid', async () => {
      res = await broker
        .call('user.actionRegister', {
          username: validEmail,
          password: 'anything',
        })
        .then(() => {
          expect('Should not here!').toEqual(true);
        })
        .catch((error: ValidationError) => {
          const errorFields: string[] = error?.data?.map((value: any) => value.field);
          expect(errorFields).toEqual(expect.arrayContaining(['password']));
        });
    });

    it('should register password: invalid 2', async () => {
      res = await broker
        .call('user.actionRegister', {
          username: validEmail,
          password: 'Anything',
        })
        .then(() => {
          expect('Should not here!').toEqual(true);
        })
        .catch((error: ValidationError) => {
          const errorFields: string[] = error?.data.map((value: any) => value.field);
          expect(errorFields).toEqual(expect.arrayContaining(['password']));
        });
    });

    it('should register password: invalid 3', async () => {
      res = await broker
        .call('user.actionRegister', {
          username: validEmail,
          password: 'Anything@',
        })
        .then(() => {
          expect('Should not here!').toEqual(true);
        })
        .catch((error: ValidationError) => {
          const errorFields: string[] = error?.data.map((value: any) => value.field);
          expect(errorFields).toEqual(expect.arrayContaining(['password']));
        });
    });

    it('should register password: invalid 4, length < 8', async () => {
      res = await broker
        .call('user.actionRegister', {
          username: validEmail,
          password: 'Ang@123',
        })
        .then(() => {
          expect('Should not here!').toEqual(true);
        })
        .catch((error: ValidationError) => {
          const errorFields: string[] = error?.data.map((value: any) => value.field);
          expect(errorFields).toEqual(expect.arrayContaining(['password']));
        });
    });

    it('should register password: valid', async () => {
      res = await broker
        .call('user.actionRegister', {
          password: 'Anyt@123',
        })
        .then(() => {
          expect('Should not here!').toEqual(true);
        })
        .catch((error: ValidationError) => {
          const errorFields: string[] = error?.data.map((value: any) => value.field);
          expect(errorFields).not.toEqual(expect.arrayContaining(['password']));
        });
    });

    it('should register new user: block by setting', async () => {
      // Delete old one
      await service.adapter.removeMany({
        username: validEmail,
      });

      await broker.broadcast('config.changed', [{ key: 'users.signup.enabled', value: false }]);

      res = await broker
        .call('user.actionRegister', {
          username: validEmail,
          name: 'OK',
          password: 'Anything@123',
        })
        .then(() => {
          expect('Should not here!').toEqual(true);
        })
        .catch((error) => {
          expect(error).toBeInstanceOf(BadRequestError);
          expect(error.message).toEqual('Sign up process is disabled! Please contact admin to sign up!');
        });
    });

    it('should register new user: block by setting updated', async () => {
      // Delete old one
      await service.adapter.removeMany({
        username: validEmail,
      });

      await broker.broadcast('config.changed', [{ key: 'users.signup.enabled', value: true }]);
      await broker.broadcast('config.changed', [{ key: 'users.signup.enabled', value: false }]);

      res = await broker
        .call('user.actionRegister', {
          username: validEmail,
          name: 'OK',
          password: 'Anything@123',
        })
        .then(() => {
          expect('Should not here!').toEqual(true);
        })
        .catch((error) => {
          expect(error).toBeInstanceOf(BadRequestError);
          expect(error.message).toEqual('Sign up process is disabled! Please contact admin to sign up!');
        });
    });

    it('should register new user: add new and check broadcast', async () => {
      jest.spyOn(broker, 'broadcast');
      // Delete old one
      await service.adapter.removeMany({
        username: validEmail,
      });

      const total = await service.adapter.count();

      service.config['users.signup.enabled'] = true;

      res = await broker.call('user.actionRegister', {
        username: validEmail,
        name: 'OK',
        password: 'Anything@123',
      });

      expect(await service.adapter.count()).toEqual(total + 1);
      expect(res).toEqual('Bạn đã đăng ký thành công vui lòng chờ quản trị viên xác nhận!');
      expect(broker.broadcast).toBeCalledTimes(1);
      expect(broker.broadcast).toBeCalledWith('user.created', expect.anything());
    });

    it('should register new user: add new and check default value', async () => {
      // Delete old one
      await service.adapter.removeMany({
        username: validEmail,
      });

      await broker.broadcast('config.changed', [{ key: 'users.signup.enabled', value: true }]);

      expect(service.config['users.signup.enabled']).toEqual(true);

      res = await broker.call('user.actionRegister', {
        username: validEmail,
        name: 'OK',
        password: 'Anything@123',
      });

      const newOne = await TestModel.findOne({ username: validEmail }).lean();

      expect(newOne).not.toBeNull();
      expect(newOne.username).toEqual(validEmail);

      // Check default value
      expect(newOne.name).toEqual('OK');
      expect(newOne.status).toEqual(STATUSES.PENDING);
      expect(newOne.permissions).toEqual([]);
      expect(newOne.role).toEqual(USER_ROLE.USER);
    });

    it('should register new user: add new and force default value', async () => {
      // Delete old one
      await service.adapter.removeMany({
        username: validEmail,
      });

      const total = await service.adapter.count();

      await broker.broadcast('config.changed', [{ key: 'users.signup.enabled', value: true }]);

      res = await broker.call('user.actionRegister', {
        username: validEmail,
        name: 'OK',
        password: 'Anything@123',
        permissions: PERMISSIONS,
        status: STATUSES.ACTIVE,
        role: USER_ROLE.ADMIN,
      });

      const newOne = await TestModel.findOne({ username: validEmail }).lean();

      expect(newOne).not.toBeNull();
      expect(newOne.username).toEqual(validEmail);

      // Check default value
      expect(newOne.name).toEqual('OK');
      expect(newOne.status).toEqual(STATUSES.PENDING);
      expect(newOne.permissions).toEqual([]);
      expect(newOne.role).toEqual(USER_ROLE.USER);
    });
  });
  describe('Test actions: actionLogin, actionLogout', () => {
    let res;
    // @ts-ignore
    const broker: TypedServiceBroker = new ServiceBroker({ logger: false });

    // Dependencies service
    broker.createService(getFakeConfigService());

    const service = new TestService(broker);

    beforeAll(async () => {
      await broker.start();

      // Create fake user
      await service.adapter.insert({
        username: userOk,
        password: plainPass,
        name: 'test',
        status: STATUSES.ACTIVE,
      });
      await service.adapter.insert({
        username: userPending,
        password: plainPass,
        name: 'test',
        status: STATUSES.PENDING,
      });
      await service.adapter.insert({
        username: userBlock,
        password: plainPass,
        name: 'test',
        status: STATUSES.DELETED,
      });
    });
    afterAll(async () => await broker.stop());

    describe("Test 'user.actionLogin'", () => {
      it('should login success', async () => {
        res = await broker.call('user.actionLogin', {
          username: userOk,
          password: plainPass,
        });

        expect(res).not.toBeNull();
        expect(res.token).toEqual(expect.any(String));

        // expect(service.adapter.updateById).toBeCalledTimes(1);
        // expect(service.adapter.updateById).toBeCalledWith('123', { $inc: { quantity: 10 } });
        //
        // expect(service.transformDocuments).toBeCalledTimes(1);
        // expect(service.transformDocuments).toBeCalledWith(expect.any(Context), { id: '123', value: 10 }, record);
        //
        // expect(service.entityChanged).toBeCalledTimes(1);
        // expect(service.entityChanged).toBeCalledWith(
        //   'updated',
        //   { _id: '123', name: 'Awesome thing', price: 999, quantity: 25 },
        //   expect.any(Context),
        // );
      });

      it('should login failed: invalid username', async () => {
        expect.assertions(2);
        res = await broker
          .call('user.actionLogin', {
            username: 'invalid user',
            password: 'wrong pass',
          })
          .catch((error: any) => {
            expect(error).toBeInstanceOf(ValidationError);
            expect(error?.data?.map((value: any) => value.field)).toEqual(expect.arrayContaining(['username']));
          });
      });

      it('should login failed: valid username, not existed', async () => {
        expect.assertions(2);
        res = await broker
          .call('user.actionLogin', {
            username: '<EMAIL>',
            password: 'wrong pass',
          })
          .catch((error: any) => {
            expect(error).toBeInstanceOf(CommonErrors.NotFoundError);
            expect(error?.message).toEqual('User/password is invalid!');
          });
      });

      it('should login failed: valid username, wrong pass', async () => {
        expect.assertions(2);
        res = await broker
          .call('user.actionLogin', {
            username: userOk,
            password: 'wrong pass',
          })
          .catch((error: any) => {
            expect(error).toBeInstanceOf(CommonErrors.NotFoundError);
            expect(error?.message).toEqual('User/password is invalid!');
          });
      });

      it('should login failed with message for new user', async () => {
        expect.assertions(2);
        res = await broker
          .call('user.actionLogin', {
            username: userPending,
            password: 'wrong pass',
          })
          .catch((error: any) => {
            expect(error).toBeInstanceOf(CommonErrors.NotFoundError);
            expect(error?.message).toEqual('User/password is invalid!');
          });
      });

      it('should login failed with message for block', async () => {
        expect.assertions(2);
        res = await broker
          .call('user.actionLogin', {
            username: userBlock,
            password: 'wrong pass',
          })
          .catch((error: any) => {
            expect(error).toBeInstanceOf(CommonErrors.NotFoundError);
            expect(error?.message).toEqual('User/password is invalid!');
          });
      });

      it('should prevent new user login with error message', async () => {
        expect.assertions(2);

        res = await broker
          .call('user.actionLogin', {
            username: userPending,
            password: plainPass,
          })
          .catch((error) => {
            expect(error).toBeInstanceOf(CommonErrors.NotFoundError);
            expect(error?.message).toEqual('Please activate user before login!');
          });
      });

      it('should prevent blocked login with error message', async () => {
        expect.assertions(2);

        res = await broker
          .call('user.actionLogin', {
            username: userBlock,
            password: plainPass,
          })
          .catch((error) => {
            expect(error).toBeInstanceOf(CommonErrors.NotFoundError);
            expect(error?.message).toEqual('This user unable to login! Please contact to admin!');
          });
      });
    });

    describe("Test 'user.actionLogout'", () => {
      it('should logout failed with guest', async () => {
        res = await broker
          .call('user.actionLogout', undefined, {
            meta: {},
          })
          .catch((error: any) => {
            expect(error).toEqual('Should not be call in here!');
          });
        expect(res).toEqual('Có lỗi trong quá trình đăng xuất!');
      });
      it('should logout success', async () => {
        const { token } = await broker.call('user.actionLogin', {
          username: userOk,
          password: plainPass,
        });
        const user = await service.adapter.findOne({ username: userOk });

        res = await broker.call('user.actionLogout', undefined, {
          meta: { user, token },
        });
        expect(res).toEqual('Bạn đã đăng xuất thành công!');
      });
      it('should logout error with invalid token', async () => {
        res = await broker
          .call('user.actionLogout', undefined, {
            meta: {
              token: 'invalid token',
            },
          })
          .catch((error: any) => {
            expect(error).toEqual('Should not be call in here!');
          });
        expect(res).toEqual('Có lỗi trong quá trình đăng xuất!');
      });
    });

    // describe("Test 'products.decreaseQuantity'", () => {
    //   it('should call the adapter updateById method & transform result', async () => {
    //     service.adapter.updateById.mockClear();
    //     service.transformDocuments.mockClear();
    //     service.entityChanged.mockClear();
    //
    //     const res = await broker.call('products.decreaseQuantity', {
    //       id: '123',
    //       value: 10,
    //     });
    //     expect(res).toEqual({
    //       _id: '123',
    //       name: 'Awesome thing',
    //       price: 999,
    //       quantity: 25,
    //     });
    //
    //     expect(service.adapter.updateById).toBeCalledTimes(1);
    //     expect(service.adapter.updateById).toBeCalledWith('123', { $inc: { quantity: -10 } });
    //
    //     expect(service.transformDocuments).toBeCalledTimes(1);
    //     expect(service.transformDocuments).toBeCalledWith(expect.any(Context), { id: '123', value: 10 }, record);
    //
    //     expect(service.entityChanged).toBeCalledTimes(1);
    //     expect(service.entityChanged).toBeCalledWith(
    //       'updated',
    //       { _id: '123', name: 'Awesome thing', price: 999, quantity: 25 },
    //       expect.any(Context),
    //     );
    //   });
    //
    //   it('should throw error if params is not valid', async () => {
    //     service.adapter.updateById.mockClear();
    //     service.transformDocuments.mockClear();
    //     service.entityChanged.mockClear();
    //
    //     expect.assertions(2);
    //     try {
    //       await broker.call('products.decreaseQuantity', {
    //         id: '123',
    //         value: -5,
    //       });
    //     } catch (err) {
    //       expect(err).toBeInstanceOf(Errors.ValidationError);
    //       expect(err.data).toEqual([
    //         {
    //           action: 'products.decreaseQuantity',
    //           actual: -5,
    //           field: 'value',
    //           message: "The 'value' field must be a positive number.",
    //           nodeID: broker.nodeID,
    //           type: 'numberPositive',
    //         },
    //       ]);
    //     }
    //   });
    // });
  });

  describe('Test broadcast event', () => {
    // @ts-ignore
    const broker: TypedServiceBroker = new ServiceBroker({ logger: false, cacher: true });

    // Dependencies service
    broker.createService(getFakeConfigService());

    const service = new TestService(broker);

    beforeEach(() => jest.clearAllMocks());
    beforeAll(async () => {
      await broker.start();

      // Create fake user
      await service.adapter
        .insert({
          username: userOk,
          password: plainPass,
          name: 'test',
          status: STATUSES.ACTIVE,
        })
        .catch(() => null);
    });
    afterAll(async () => await broker.stop());

    it('should broadcast event user.login', async () => {
      jest.spyOn(broker, 'broadcast');

      const { token } = await broker.call('user.actionLogin', {
        username: userOk,
        password: plainPass,
      });

      expect(broker.broadcast).toBeCalledWith('user.login', {
        user: expect.any(Object),
        token,
      });
      // expect(broker.cacher.set).toBeCalledWith(
      //   `${service.name}.actionVerifyToken:${token}`,
      //   expect.anything(),
      //   expect.anything(),
      // );
    });

    it('should broadcast event user.created after register', async () => {
      jest.spyOn(broker, 'broadcast');

      res = await broker.call('user.actionRegister', {
        username: '<EMAIL>',
        name: 'test',
        password: 'Test@123',
      });

      expect(broker.broadcast).toBeCalledTimes(1);
      expect(broker.broadcast).toBeCalledWith('user.created', expect.any(Object));
    });

    it('should broadcast event user.updated after change', async () => {
      jest.spyOn(broker, 'broadcast');

      const { token } = await broker.call('user.actionLogin', {
        username: userOk,
        password: plainPass,
      });
      const user = await service.adapter.findOne({ username: userOk });

      // Mock save function
      service.adapter.findById = jest.fn(async (args) => {
        if (user) {
          user.save = jest.fn();
        }
        return user;
      });

      jest.clearAllMocks();

      res = await broker.call(
        'user.actionMeUpdate',
        {
          name: 'Name changed',
        },
        {
          meta: {
            user,
            token,
          },
        },
      );

      expect(broker.broadcast).toBeCalledTimes(1);
      expect(broker.broadcast).toBeCalledWith(
        'user.updated',
        expect.objectContaining({ name: 'Name changed', username: userOk }),
      );
      expect(broker.broadcast).toBeCalledWith(
        'user.updated',
        expect.not.objectContaining({ password: expect.anything() }),
      );
    });

    it('should broadcast event user.logout', async () => {
      jest.spyOn(broker, 'broadcast');

      const { token } = await broker.call('user.actionLogin', {
        username: userOk,
        password: plainPass,
      });
      const user = await service.adapter.findOne({ username: userOk });

      jest.clearAllMocks();

      res = await broker.call('user.actionLogout', undefined, {
        meta: {
          user,
          token,
        },
      });

      expect(broker.broadcast).toBeCalledTimes(1);
      expect(broker.broadcast).toBeCalledWith('user.logout', {
        user: expect.any(Object),
        token: expect.any(String),
      });
    });
  });
  describe('Test handle broadcast event', () => {
    // @ts-ignore
    const broker: TypedServiceBroker = new ServiceBroker({ logger: false, cacher: true });

    // Dependencies service
    broker.createService(getFakeConfigService());

    const service = new TestService(broker);

    afterEach(() => {
      jest.clearAllMocks();
    });

    beforeAll(async () => {
      await broker.start();

      // Create fake user
      await service.adapter
        .insert({
          username: userOk,
          password: plainPass,
          name: 'test',
          status: STATUSES.ACTIVE,
        })
        .catch(() => {
          // Skip if existed
        });
    });
    afterAll(async () => await broker.stop());

    const token = expect.any(String);
    it('Test event user.login: empty', () => {
      jest.spyOn(broker.cacher, 'set');

      broker.broadcast('user.login', { user: null, token });

      expect(broker.cacher.set).toBeCalledTimes(0);
    });
    it('Test event user.login: cache user.actionMe', async () => {
      jest.spyOn(broker.cacher, 'set');

      const user = await service.adapter.findOne({ username: userOk });

      broker.broadcast('user.login', { user, token });

      expect(broker.cacher.set).toBeCalledTimes(1);
      expect(broker.cacher.set).toBeCalledWith(`${service.name}.actionMe:${user._id}`, user);
    });
    it('Test event user.logout: empty', () => {
      jest.spyOn(broker.cacher, 'clean');

      broker.broadcast('user.logout', { user: null, token });

      expect(broker.cacher.clean).toBeCalledTimes(0);
    });
    it('Test event user.logout: clean user.actionVerifyToken', async () => {
      jest.spyOn(broker.cacher, 'clean');

      const user = await service.adapter.findOne({ username: userOk });

      broker.broadcast('user.logout', { user, token });

      expect(broker.cacher.clean).toBeCalledTimes(1);
      expect(broker.cacher.clean).toBeCalledWith(expect.arrayContaining([`user.actionVerifyToken:${token}`]));
    });

    it('Test event user.created', async () => {
      jest.spyOn(service, 'cleanCacheOnChangeUser');

      const user = expect.any(User);

      await broker.broadcast('user.created', user);

      expect(service.cleanCacheOnChangeUser).toBeCalledTimes(1);
      expect(service.cleanCacheOnChangeUser).toBeCalledWith(user);
    });

    it('Test event user.updated', async () => {
      jest.spyOn(service, 'cleanCacheOnChangeUser');

      const user = expect.any(User);

      await broker.broadcast('user.updated', user);

      expect(service.cleanCacheOnChangeUser).toBeCalledTimes(1);
      expect(service.cleanCacheOnChangeUser).toBeCalledWith(user);
    });

    it('Test event user.removed', async () => {
      jest.spyOn(service, 'cleanCacheOnChangeUser');

      const user = expect.any(User);

      await broker.broadcast('user.removed', user);

      expect(service.cleanCacheOnChangeUser).toBeCalledTimes(1);
      expect(service.cleanCacheOnChangeUser).toBeCalledWith(user, true);
    });
  });
  describe('Test cacher', () => {
    // @ts-ignore
    const broker: TypedServiceBroker = new ServiceBroker({ logger: false, cacher: true });

    // Dependencies service
    broker.createService(getFakeConfigService());

    const service = new TestService(broker);

    beforeAll(async () => {
      await broker.start();

      // Create fake user
      await service.adapter
        .insert({
          username: userOk,
          password: plainPass,
          name: 'test',
          status: STATUSES.ACTIVE,
        })
        .catch(() => null);
    });
    afterAll(async () => await broker.stop());

    it('should cache user.actionVerifyToken:${token} after user.actionVerifyToken', async () => {
      const { token } = await broker.call('user.actionLogin', {
        username: userOk,
        password: plainPass,
      });

      jest.spyOn(broker.cacher, 'set');

      const res = await broker.call('user.actionVerifyToken', { token });

      expect(broker.cacher.set).toBeCalledWith(`user.actionVerifyToken:${token}`, res, expect.anything());
    });
  });

  describe('Test actions: actionList', () => {
    let res;
    // @ts-ignore
    const broker: TypedServiceBroker = new ServiceBroker({ logger: false });

    // Dependencies service
    broker.createService(getFakeConfigService());

    const service = new TestService(broker);

    service.config['users.signup.enabled'] = true;

    beforeAll(async () => {
      await broker.start();
    });
    afterAll(async () => await broker.stop());

    it('should return list: default', async () => {
      const total = await TestModel.countDocuments();
      res = await broker.call('user.actionList', {});

      expect(res).toEqual({
        rows: expect.arrayContaining([
          {
            _id: expect.any(String),
            username: expect.any(String),
            name: expect.any(String),
            permissions: expect.arrayContaining(PERMISSIONS),
            role: expect.any(Number),
            status: expect.any(Number),
          },
        ]),
        total: total,
        page: 1,
        pageSize: 10,
        totalPages: 1,
      });
    });

    it('should return list: page 0', async () => {
      expect.assertions(1);
      res = await broker
        .call('user.actionList', {
          page: 0,
        })
        .catch((error) => {
          expect(error).toBeInstanceOf(ValidationError);
        });
    });
    it('should return list: page 1', async () => {
      const total = await TestModel.countDocuments();
      res = await broker.call('user.actionList', {
        page: 1,
      });

      expect(res).toEqual({
        rows: expect.arrayContaining([expect.any(Object)]),
        total: total,
        page: 1,
        pageSize: 10,
        totalPages: 1,
      });
    });

    it('should return list: page 2', async () => {
      const total = await TestModel.countDocuments();
      res = await broker.call('user.actionList', {
        page: 2,
      });

      expect(res).toEqual({
        rows: [],
        total: total,
        page: 2,
        pageSize: 10,
        totalPages: 1,
      });
    });
  });

  describe('Test actions: actionMe, actionMeUpdate', () => {
    let res;
    // @ts-ignore
    const broker: TypedServiceBroker = new ServiceBroker({ logger: false });

    // Dependencies service
    broker.createService(getFakeConfigService());

    const service = new TestService(broker);

    service.config['users.signup.enabled'] = true;

    let token: string, user: IUser;

    beforeAll(async () => {
      await broker.start();

      // Create fake user
      await service.adapter
        .insert({
          username: userOk,
          password: plainPass,
          name: 'test',
          status: STATUSES.ACTIVE,
        })
        .catch(() => null);

      const data = await broker.call('user.actionLogin', {
        username: userOk,
        password: plainPass,
      });
      token = data.token;
      user = await TestModel.findOne({
        username: userOk,
      }).lean();
    });
    afterAll(async () => await broker.stop());

    it('should call actionMe: empty', async () => {
      res = await broker.call('user.actionMe', undefined, {});
      expect(res).toEqual(null);
    });

    it('should call actionMe: get info', async () => {
      res = await broker.call('user.actionMe', undefined, {
        meta: {
          user,
          token,
        },
      });

      expect(res).toEqual({
        _id: expect.any(String),
        username: expect.any(String),
        permissions: expect.any(Array),
        name: expect.any(String),
        role: expect.any(Number),
        status: expect.any(Number),
      });
      res.permissions.forEach((permission) => {
        expect(PERMISSIONS.includes(permission)).toEqual(true);
      });
    });

    it('should call actionMeUpdate: name', async () => {
      const nameChanged = 'name ' + Math.random();
      res = await broker.call(
        'user.actionMeUpdate',
        {
          name: nameChanged,
        },
        { meta: { user, token } },
      );
      expect(res.name).toEqual(nameChanged);
    });

    it('should call actionMeUpdate: username not allow', async () => {
      expect.assertions(2);
      const usernameChanged = 'username ' + Math.random();
      res = await broker
        .call(
          'user.actionMeUpdate',
          {
            username: usernameChanged,
          },
          { meta: { user, token } },
        )
        .catch(async (error) => {
          expect(error).toBeInstanceOf(ValidationError);
          res = await broker.call('user.actionMe', undefined, { meta: { user, token } });
          expect(res?.username).toEqual(userOk);
        });
    });

    it('should call actionMeUpdate: permissions not allow', async () => {
      expect.assertions(2);
      res = await broker
        .call(
          'user.actionMeUpdate',
          {
            permissions: PERMISSIONS,
          },
          { meta: { user, token } },
        )
        .catch(async (error) => {
          expect(error).toBeInstanceOf(ValidationError);
          res = await broker.call('user.actionMe', undefined, { meta: { user, token } });
          expect(res?.permissions).toEqual(user.permissions);
        });
    });

    it('should call actionMeUpdate: change password invalid', async () => {
      expect.assertions(2);
      res = await broker
        .call(
          'user.actionMeUpdate',
          {
            password: 'invalidpass',
          },
          { meta: { user, token } },
        )
        .catch(async (error) => {
          expect(error).toBeInstanceOf(ValidationError);
          res = await broker.call('user.actionLogin', {
            username: userOk,
            password: plainPass,
          });
          expect(res.token).toEqual(expect.any(String));
        });
    });
    it('should call actionMeUpdate: change new valid password', async () => {
      const newPassword = 'NewPass@1234';
      res = await broker.call(
        'user.actionMeUpdate',
        {
          password: newPassword,
        },
        { meta: { user, token } },
      );
      res = await broker.call('user.actionLogin', {
        username: userOk,
        password: newPassword,
      });
      expect(res.token).toEqual(expect.any(String));
    });
  });

  describe('Test actions: actionVerifyToken', function () {
    let res;
    // @ts-ignore
    const broker: TypedServiceBroker = new ServiceBroker({ logger: false });

    // Dependencies service
    broker.createService(getFakeConfigService());

    const service = new TestService(broker);

    service.config['users.signup.enabled'] = true;

    let token: string, user: IUser;

    beforeAll(async () => {
      await broker.start();

      // Create fake user
      await TestModel.deleteMany({
        username: userOk,
      });
      await service.adapter
        .insert({
          username: userOk,
          password: plainPass,
          name: 'test',
          status: STATUSES.ACTIVE,
        })
        .catch(() => null);

      const data = await broker.call('user.actionLogin', {
        username: userOk,
        password: plainPass,
      });
      token = data.token;
      user = await TestModel.findOne({
        username: userOk,
      }).lean();
    });
    afterAll(async () => await broker.stop());

    it('should blank info for invalid token', async () => {
      res = await broker.call('user.actionVerifyToken', { token: 'invalid' });
      expect(res).toEqual(null);
    });

    it('should return info and enough fields', async () => {
      res = await broker.call('user.actionVerifyToken', { token });
      expect(res).toEqual({
        _id: expect.any(String),
        username: expect.any(String),
        permissions: expect.any(Array),
        name: expect.any(String),
        role: expect.any(Number),
        status: expect.any(Number),
      });
    });
  });

  // describe('Test methods', () => {
  //   const broker = new ServiceBroker({ logger: false });
  //   const service = broker.createService(TestService);
  //
  //   // jest.spyOn(service.adapter, 'insertMany');
  //   // jest.spyOn(service, 'seedDB');
  //
  //   beforeAll(() => broker.start());
  //   afterAll(() => broker.stop());
  //
  //   describe("Test 'seedDB'", () => {
  //     it('should be called after service started & DB connected', async () => {
  //       expect(service.seedDB).toBeCalledTimes(1);
  //       expect(service.seedDB).toBeCalledWith();
  //     });
  //
  //     it('should insert 3 documents', async () => {
  //       expect(service.adapter.insertMany).toBeCalledTimes(1);
  //       expect(service.adapter.insertMany).toBeCalledWith([
  //         { name: 'Samsung Galaxy S10 Plus', quantity: 10, price: 704 },
  //         { name: 'iPhone 11 Pro', quantity: 25, price: 999 },
  //         { name: 'Huawei P30 Pro', quantity: 15, price: 679 },
  //       ]);
  //     });
  //   });
  // });
  //
  // describe('Test hooks', () => {
  //   const broker = new ServiceBroker({ logger: false });
  //   const createActionFn = jest.fn();
  //   // @ts-ignore
  //   broker.createService(TestService);
  //
  //   beforeAll(() => broker.start());
  //   afterAll(() => broker.stop());
  //
  //   // describe("Test before 'create' hook", () => {
  //   //   it('should add quantity with zero', async () => {
  //   //     await broker.call('products.create', {
  //   //       id: '111',
  //   //       name: 'Test product',
  //   //       price: 100,
  //   //     });
  //   //
  //   //     expect(createActionFn).toBeCalledTimes(1);
  //   //     expect(createActionFn.mock.calls[0][0].params).toEqual({
  //   //       id: '111',
  //   //       name: 'Test product',
  //   //       price: 100,
  //   //       quantity: 0,
  //   //     });
  //   //   });
  //   // });
  // });
});
