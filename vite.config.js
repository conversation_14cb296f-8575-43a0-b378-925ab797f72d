import rollupReplace from "@rollup/plugin-replace";
import react from "@vitejs/plugin-react";
import reactRefresh from "@vitejs/plugin-react-refresh";
import fs from "fs";
import path from "path";
import { defineConfig } from "vite";
// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },

  build: {
    rollupOptions: {
      external: [
        /^node:.*/
      ]
    }
  },

  // optimizeDeps: {
  //   include: ["vfile"]
  // },

  plugins: [
    rollupReplace({
      preventAssignment: true,
      values: {
        __DEV__: JSON.stringify(mode === 'development'),
        "process.env.NODE_ENV": JSON.stringify(mode),
      },
    }),
    react(),
    reactRefresh(),
    {
      name: 'fallback-middleware', configureServer(server) {
        return () => {
          server.middlewares.use((req, res, next) => {
            // Check format originalUrl is /editor/2c0d3f99-a593-4d1a-960e-b5abd31b5888 using regex for redirect to editor root html
            const match = req.originalUrl.match(/\/editor\/[0-9a-f-]{36}/);
            if (match) {
              const fallbackPath = path.join(__dirname, 'public', 'editor', 'index.html');
              res.writeHead(200, {'Content-Type': 'text/html'});
              fs.createReadStream(fallbackPath).pipe(res);
            } else {
              next();
            }
          });
        };
      },
    },
  ],
}));
