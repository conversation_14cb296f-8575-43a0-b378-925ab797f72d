<html>
<head>
  <title id="pageTitle">Bizino Team Demo Chat</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">

  <link rel="stylesheet" href="styles.css">
</head>

<body>
<section class="msger">
  <header class="msger-header">
    <div class="msger-header-title" id="headerTitle">
      SimpleChat
    </div>
    <div class="msger-header-options">
      <button id="clearChatBtn">🗑️</button>
    </div>
  </header>

  <main class="msg-chat">
    <!-- For chat history -->
  </main>

  <!-- Placeholder for suggestions -->
  <div class="suggestions-container"></div>

  <!-- Popup for displaying thinking data -->
  <div id="thinkingPopup" class="thinking-popup">
    <div class="thinking-popup-content">
      <span class="close-popup">&times;</span>
      <div id="thinkingDataContainer" class="thinking-data-container">
        <pre id="thinkingData"></pre>
      </div>
    </div>
  </div>

  <form class="msg-form">
    <input type="text" class="msger-input" placeholder="Enter your message..." disabled>
    <button type="submit" class="msger-send-btn" disabled>Send</button>
    <button class="msger-speech-btn" disabled>🎤</button>
  </form>
</section>
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/dompurify/dist/purify.min.js"></script>
<script src="script.js"></script>
</body>
</html>
