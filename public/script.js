const API_URL = "";

const msgerForm = get('.msg-form');
const msgerInput = get('.msger-input');
const msgerChat = get('.msg-chat');

let page = 1;
let isLoading = false;
let hasMoreHistory = true;

msgerChat.addEventListener('scroll', async () => {
  if (msgerChat.scrollTop === 0 && !isLoading && hasMoreHistory) {
    isLoading = true;
    try {
      const response = await fetch(`${API_URL}/api/botChat/moreHistory`, {
        method: 'POST', headers: {
          'Content-Type': 'application/json',
        }, body: JSON.stringify({
          bot_uuid: BOT_UUID, thread_id, channel_id: 'web', environment: 'dev', page: page,
        }),
      });


      const data = await response.json();
      const firstItem = msgerChat.firstElementChild;
      data.histories.forEach(history => {
        const { message, timestamp, sender } = history;
        const side = sender === 'assistant' ? 'left' : 'right';
        const name = sender === 'assistant' ? BOT_NAME : PERSON_NAME;
        const img = sender === 'assistant' ? BOT_IMG : PERSON_IMG;
        appendMessage(name, img, side, message, timestamp, true);
      });
      firstItem.scrollIntoView({});

      // Increment page number on successful request only if histories has data
      if (data.histories && data.histories.length > 0) {
        page += 1;
      } else {
        hasMoreHistory = false;
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      isLoading = false;
    }
  }
});
const speechBtn = get('.msger-speech-btn');
const clearChatBtn = get('#clearChatBtn');
const msgerSendBtn = get('.msger-send-btn');

// Icons made by Freepik from www.flaticon.com
const BOT_IMG = 'https://image.flaticon.com/icons/svg/327/327779.svg';
const PERSON_IMG = 'https://image.flaticon.com/icons/svg/145/145867.svg';
let BOT_NAME = 'BOT';
const PERSON_NAME = 'Me';
let BOT_UUID = 'a5dbaf21-36e1-41e4-b553-cb0c68d1f830';

// Extract BOT_UUID from URL hash
const hash = window.location.hash;
if (hash.startsWith('#/ai/')) {
  const parts = hash.split('/');
  if (parts.length >= 3) {
    BOT_UUID = parts[2];
  }
}

// Generate a thread_id and save it to localStorage
let thread_id = localStorage.getItem('thread_id');
if (!thread_id) {
  thread_id = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  localStorage.setItem('thread_id', thread_id);
}

// Make an initial HTTP request to startChat
(async () => {
  try {
    const response = await fetch(`${API_URL}/api/botChat/startChat`, {
      method: 'POST', headers: {
        'Content-Type': 'application/json',
      }, body: JSON.stringify({
        bot_uuid: BOT_UUID, thread_id: thread_id, channel_id: 'web', environment: 'dev',
      }),
    });

    if (!response.ok) {
      throw new Error('Network response was not ok');
    }

    const data = await response.json();
    BOT_NAME = data.setting.name;
    document.getElementById('pageTitle').innerText = BOT_NAME;
    document.getElementById('headerTitle').innerText = BOT_NAME;

    // Enable the chat input, send button, and speech button
    msgerInput.disabled = false;
    msgerSendBtn.disabled = false;
    speechBtn.disabled = false;

    // Check for greeting and append to chat history, but only if histories is not present
    if (data.setting.greeting && (!data.histories || data.histories.length === 0)) {
      appendMessage(BOT_NAME, BOT_IMG, 'left', data.setting.greeting, Date.now());
    }

    // Check for suggestions and display them
    if (data.suggests && data.suggests.length > 0) {
      displaySuggestions(data.suggests);
    }

    // Append chat history from response
    if (data.histories && data.histories.length > 0) {
      data.histories.reverse().forEach(history => {
        const { message, timestamp, sender } = history;
        const side = sender === 'assistant' ? 'left' : 'right';
        const name = sender === 'assistant' ? BOT_NAME : PERSON_NAME;
        const img = sender === 'assistant' ? BOT_IMG : PERSON_IMG;
        appendMessage(name, img, side, message, timestamp);
      });
    }
  } catch (error) {
    console.error('Error:', error);
  }
})();

function displaySuggestions(suggestions) {
  const suggestionsContainer = document.querySelector('.suggestions-container');
  suggestionsContainer.innerHTML = ''; // Clear previous suggestions

  suggestions.forEach(suggestion => {
    const suggestionButton = document.createElement('button');
    suggestionButton.className = 'suggestion-btn';
    suggestionButton.textContent = suggestion;
    suggestionButton.addEventListener('click', () => {
      msgerInput.value = suggestion;
      msgerForm.dispatchEvent(new Event('submit'));
    });
    suggestionsContainer.appendChild(suggestionButton);
  });
}

msgerForm.addEventListener('submit', async event => {
  event.preventDefault();

  const msgText = msgerInput.value;
  if (!msgText) return;

  // Clear the suggestions container before appending a new message
  const suggestionsContainer = document.querySelector('.suggestions-container');
  suggestionsContainer.innerHTML = '';

  appendMessage(PERSON_NAME, PERSON_IMG, 'right', msgText, Date.now());
  msgerInput.value = '';

  try {
    const response = await fetch(`${API_URL}/api/botChat/chat`, {
      method: 'POST', headers: {
        'Content-Type': 'application/json',
      }, body: JSON.stringify({
        message: msgText, bot_uuid: BOT_UUID, thread_id, channel_id: 'web', environment: 'dev',
      }),
    });


    const data = await response.json();
    let thinking = null;
    if (response.ok) {
      const botMsgText = data.message;
      appendMessage(BOT_NAME, BOT_IMG, 'left', botMsgText, Date.now(), data.thinking, false);
    } else {
      let thinking = null;
      if (data.data && Array.isArray(data.data.thinking)) {
        thinking = data.data.thinking;
      }
      const errorMsg = data.message ? `Error: ${data.message}` : 'Sorry, I encountered an error processing your message.';
      appendMessage(BOT_NAME, BOT_IMG, 'left', errorMsg, Date.now(), thinking, false);
    }
  } catch (error) {
    console.error('Error:', error);
    appendMessage(BOT_NAME, BOT_IMG, 'left', 'Sorry, I encountered an error processing your message.', Date.now());
  }
});

let isListening = false;
const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();

speechBtn.addEventListener('click', () => {
  if (isListening) {
    isListening = false;
    speechBtn.textContent = '🎤';
    recognition.stop();
    return;
  }

  isListening = true;
  speechBtn.textContent = '⏹️';

  recognition.lang = 'vi-VN';
  recognition.interimResults = false;
  recognition.maxAlternatives = 1;

  // Enable continuous listening to avoid multiple permission prompts
  recognition.continuous = true;

  recognition.onresult = (event) => {
    const speechResult = event.results[event.results.length - 1][0].transcript;
    msgerInput.value = speechResult;
    msgerForm.dispatchEvent(new Event('submit'));
  };

  recognition.onerror = (event) => {
    console.error('Speech recognition error:', event.error);
    isListening = false;
    speechBtn.textContent = '🎤';
  };

  recognition.start();
});

clearChatBtn.addEventListener('click', () => {
  if (confirm('Are you sure you want to clear the chat history and start a new chat?')) {
    thread_id = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    localStorage.setItem('thread_id', thread_id);
    location.reload();
  }
});

function generateThinkingTable(thinking) {
  let tableHTML = '<table class="thinking-table"><thead><tr><th>ID</th><th>Type</th><th>Message</th></tr></thead><tbody>';
  thinking.forEach(record => {
    let sanitizedMsg;
    if (typeof record.data === 'object' && record.data !== null) {
      const subTableHTML = generateSubTable(record.data);
      sanitizedMsg = DOMPurify.sanitize(subTableHTML);
    } else {
      sanitizedMsg = DOMPurify.sanitize(record.data || '');
    }
    tableHTML += `<tr><td>${record.id}</td><td>${record.type}</td><td>${record.name}<br/>${sanitizedMsg}</td></tr>`;
  });
  tableHTML += '</tbody></table>';
  return tableHTML;
}

function generateSubTable(data) {
  let subTableHTML = '<table class="sub-thinking-table"><thead><tr><tbody>';

  Object.entries(data).forEach(([key, value]) => {
    subTableHTML += '<tr>';
    subTableHTML += `<td>${key}</td>`;
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      subTableHTML += `<td>${DOMPurify.sanitize(JSON.stringify(value))}</td>`;
    } else {
      const sanitizedValue = DOMPurify.sanitize((value || '').toString().replace(/\n/g, '<br>'));
      subTableHTML += `<td>${sanitizedValue}</td>`;
    }
    subTableHTML += '</tr>';
  });

  subTableHTML += '</tbody></table>';
  return subTableHTML;
}

function appendMessage(name, img, side, markdownText, timestamp, thinking = null, isHistory = false) {
  // Convert Markdown to HTML
  const rawHtmlContent = marked.parse(markdownText);

  // Sanitize the HTML to prevent XSS attacks
  const cleanHtmlContent = DOMPurify.sanitize(rawHtmlContent);
  //   Simple solution for small apps
  const debugIcon = thinking ? `<span class="debug-icon" title="Show thinking data">🔍</span>` : '';
  const msgHTML = `
    <div class="msg ${side}-msg">
      <div class="msg-img" style="background-image: url(${img})"></div>

      <div class="msg-bubble">
        <div class="msg-info">
          <div class="msg-info-name">${debugIcon}${name}</div>
          <div class="msg-info-time">${formatDate(new Date(timestamp))}</div>
        </div>

        <div class="msg-text content">
            ${cleanHtmlContent}
        </div>
      </div>
    </div>
  `;

  if (isHistory) {
    msgerChat.insertAdjacentHTML('afterbegin', msgHTML);
  } else {
    msgerChat.insertAdjacentHTML('beforeend', msgHTML);
    msgerChat.scrollTop += 500;
  }

  if (thinking) {
    const debugIconElement = msgerChat.lastElementChild.querySelector('.debug-icon');
    debugIconElement.addEventListener('click', () => {
      const popup = document.getElementById('thinkingPopup');
      const thinkingDataElement = document.getElementById('thinkingData');
      thinkingDataElement.innerHTML = generateThinkingTable(thinking);
      popup.style.display = 'flex';

      const closePopup = document.querySelector('.close-popup');
      closePopup.onclick = () => {
        popup.style.display = 'none';
      };

      window.onclick = (event) => {
        if (event.target === popup) {
          popup.style.display = 'none';
        }
      };
    });
  }
}


// Utils
function get(selector, root = document) {
  return root.querySelector(selector);
}

function formatDate(date) {
  const d = '0' + date.getDate();
  const M = '0' + (date.getMonth() + 1);
  const h = '0' + date.getHours();
  const m = '0' + date.getMinutes();

  return `${d.slice(-2)}/${M.slice(-2)} ${h.slice(-2)}:${m.slice(-2)}`;
}

function random(min, max) {
  return Math.floor(Math.random() * (max - min) + min);
}
