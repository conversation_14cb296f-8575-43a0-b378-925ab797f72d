import { ObjectId, ObjectIdNull } from '../types';
import { BotThinking } from '../types/flow.types';
import { BotThreadSession } from '../types/flow.node.types';

export enum ChatMode {
  NORMAL = 0,
  DISABLE_CHAT = 1,
}

export type IChatHistory = {
  _id?: ObjectIdNull;
  bot_uuid: string;
  thread_id: string;
  channel_id: string;
  environment: string;
  session: BotThreadSession['session'];
  histories: ChatRecord[];
  isFinished: boolean;
  total: number;
  recentMessage?: string; // New field for the most recent message
  chatMode?: number; // 0: auto reply, 1: disable auto chat
};

export type ChatRecord = {
  _id: ObjectId;
  timestamp: number;
  message: string;
  sender: string;
  thinking?: BotThinking[];
  [name: `_${string}`]: any;
};
