import { ObjectId, ObjectIdNull } from '../types';
import { BotThinking, BotThreadSession } from '../types/flow.types';

export type IChatHistory = {
  _id?: ObjectIdNull;
  bot_uuid: string;
  thread_id: string;
  channel_id: string;
  environment: string;
  session: BotThreadSession['session'];
  histories: ChatRecord[];
  isFinished: boolean;
  total: number;
  recentMessage?: string; // New field for the most recent message
};

export type ChatRecord = {
  _id: ObjectId;
  timestamp: number;
  message: string;
  sender: string;
  thinking?: BotThinking[];
  [name: `_${string}`]: any;
};
