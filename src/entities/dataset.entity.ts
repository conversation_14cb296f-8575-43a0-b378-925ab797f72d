import { ObjectIdNull } from '../types';

export const DATASET_STATUS = {
  ACTIVE: 1,
  DELETED: -1,
} as const;
export const DATASET_TYPE = {
  KNOWLEDGE: 1,
  QNA: 2,
  VECTOR_STORE: 3,
} as const;

export type IDataset = {
  _id?: string;
  name?: string;
  team_id: ObjectIdNull;
  type: number;
  model_name: string;
  description: string;
  status: number;
  vectorConfig?: { fields: string[]; vectorFields: string[]; parseFields?: { [name: string]: 'json' | 'number' | 'boolean' }; indexField?: string };
};
