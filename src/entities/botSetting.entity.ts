import { BotConfig } from '../types/flow.types';

export type LiveChatConfig = any;

export const SNAPSHOT_TYPES = {
  ORIGINAL: 0, // Original bot setting
  SNAPSHOT: 1, // snapshot
  PRODUCTION: 2, // Production snapshot
} as const;

export const TEMPLATE_TYPES = {
  DEFAULT: 0, // Default bot setting
  PUBLIC_TEMPLATE: 1, // Public template
} as const;

export type IBotSetting = {
  _id?: string;
  name: string;
  desc: string;
  team_id: string;
  status: number;
  type: string;
  config: BotConfig;
  copyright: string;
  greeting: string;
  template_type: number;
  question_1: string;
  question_2: string;
  question_3: string;
  max_history: number;
  max_history_day: number;
  snapshot_type?: number;
  snapshot_name?: string;
  snapshot_id?: string;
  logo_url?: string;
  live_config?: LiveChatConfig;
};

export type IBotSnapshot = {
  _id?: string;
  snapshot_id: string;
  snapshot_type: number;
  snapshot_name: string;
  updated: Date;
};
