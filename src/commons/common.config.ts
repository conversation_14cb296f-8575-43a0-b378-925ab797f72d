import os from 'os';

function isTrue(text?: string | number) {
  return [1, true, '1', 'true', 'yes'].includes(text || '');
}

function isFalse(text?: string | number) {
  return [0, false, '0', 'false', 'no'].includes(text || '');
}

function getValue(text?: string, defaultValud?: string | boolean) {
  const vtrue = isTrue(text);
  const vfalse = isFalse(text);
  const val = text || defaultValud;
  if (vtrue) {
    return true;
  } else if (vfalse) {
    return false;
  }
  return val;
}

const HOST_NAME = os.hostname().toLowerCase();

function getDbInfo(where: string, what: string, defaultValue: string) {
  const value = process.env[`DB_${where}_${what}`];
  const generic = process.env[`DB_GENERIC_${what}`];
  return value || generic || defaultValue;
}

export const CommonConfig = {
  NODE_ENV: process.env.NODE_ENV || 'development', // or production
  IS_TEST: (process.env.NODE_ENV || 'development') === 'test',
  HOST: process.env.HOST || '0.0.0.0',
  PORT: +(process.env.PORT || 80),
  REQUEST_TIMEOUT: +(process.env.REQUEST_TIMEOUT || 10000),
  NAMESPACE: process.env.NAMESPACE || undefined,
  NODEID: `${process.env.NODEID ? process.env.NODEID + '-' : ''}${HOST_NAME}-${process.env.NODE_ENV || 'development'}`,
  CACHER: getValue(process.env.CACHER, undefined),
  RATE_LIMIT: +(process.env.RATE_LIMIT || 10),
  RATE_LIMIT_WINDOW: +(process.env.RATE_LIMIT_WINDOW || 10000),
  JWT_SECRET: process.env.JWT_SECRET || 'dummy-secret',
  DB_URI: getDBUri(),
  MILVUS_URI: process.env.MILVUS_URI || 'localhost:19530', // Not using now
  WEAVIATE_URL: process.env.WEAVIATE_URL || 'localhost:8080',
  WEAVIATE_SCHEMA: process.env.WEAVIATE_SCHEMA || 'http',
};

function getDBUri(): string {
  let dbUri = process.env.MONGO_URI || 'mongodb://localhost/moleculer_mongo';
  const NODE_ENV = process.env.NODE_ENV || 'development';
  if (NODE_ENV != 'production') {
    dbUri = dbUri + '_' + NODE_ENV;
  }
  return dbUri;
}

function getUriDbCollection(collection: string): string {
  const dbUri = process.env.MONGO_URI || 'mongodb://localhost/moleculer-mongo';
  return dbUri + '/' + collection;
}
