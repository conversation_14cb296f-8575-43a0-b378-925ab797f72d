import { Errors } from 'moleculer';

/**
 * List api code. See more: https://en.wikipedia.org/wiki/List_of_HTTP_status_codes
 */
export const API_CODE = {
  SERVER_ERROR: 500,
  UNPROCESSABLE: 422,
  TOO_MANY_REQUEST: 429,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SESSION_EXPIRED: 440,
};

export module CommonErrors {
  import ValidationError = Errors.ValidationError;

  export class BadRequestError extends Errors.MoleculerError {
    constructor(message: string, data: any = null, type: string = 'BAD_REQUEST') {
      super(message, API_CODE.BAD_REQUEST, type, data);
    }
  }

  export class NotFoundError extends Errors.MoleculerError {
    constructor(message: string = 'Item not found!', data: any = null, type: string = 'NOT_FOUND') {
      super(message, API_CODE.NOT_FOUND, type, data);
    }
  }

  export class CustomValidationError extends ValidationError {
    constructor(data: { type: string; message: string; field: string; actual: any }[]) {
      super('Parameters validation error!', 'VALIDATION_ERROR', data);
    }
  }

  export class UnAuthorizationError extends Errors.MoleculerError {
    constructor(
      type: string = 'UNAUTHORIZED',
      message: string = 'Your request is unauthorized! Please login first!',
      data: any = null,
    ) {
      super(message, API_CODE.UNAUTHORIZED, type, data);
    }
  }

  export class TooManyRequestError extends Errors.MoleculerError {
    constructor(
      message: string = 'Your request is limited! Please try later!',
      data: any = null,
      type: string = 'TOO_MANY_REQUEST',
    ) {
      super(message, API_CODE.TOO_MANY_REQUEST, type, data);
    }
  }

  export class ForbiddenError extends Errors.MoleculerError {
    constructor(
      message: string = 'You are not allowed to perform this action!',
      data: any = null,
      type: string = 'FORBIDDEN',
    ) {
      super(message, API_CODE.FORBIDDEN, type, data);
    }
  }

  export class UnprocessableError extends Errors.MoleculerError {
    constructor(
      data: { field: string; message: string; type?: string }[] = null,
      message: string = null,
      type: string = 'UNPROCESSABLE',
    ) {
      if (!message && data && data.length > 0) {
        message = data[0].message;
      }
      super(message, API_CODE.UNPROCESSABLE, type, data);
    }
  }

  export class ServerError extends Errors.MoleculerError {
    constructor(
      message: string = 'Server error! Please contact to admin!',
      data: any = null,
      type: string = 'SERVER_ERROR',
    ) {
      super(message, API_CODE.SERVER_ERROR, type, data);
    }
  }

  export class NotImplementYetError extends Errors.MoleculerError {
    constructor(
      message: string = 'This feature not implement yet!',
      data: any = null,
      type: string = 'NOT_IMPLEMENT_YET',
    ) {
      super(message, API_CODE.SERVER_ERROR, type, data);
    }
  }
}
