import { plugin, pre, prop } from '@typegoose/typegoose';
import { Field, getSchema, Number, Schema, String as IsString } from 'fastest-validator-decorators';
import { BaseModel, basePreSave } from './base.model';
import * as mongoose from 'mongoose';
import mongooseLeanId from '../utils/mongoPlugins/mongoLeanId';
import paginate from '../utils/mongoPlugins/paginate';
import { DATASET_STATUS, DATASET_TYPE, IDataset } from '../entities/dataset.entity';
import { v4 as uuidv4 } from 'uuid';

@Schema()
@pre<Dataset>('save', function (): Promise<void> {
  return basePreSave(this);
})
@plugin(paginate, { sort: '-created' })
@plugin(mongooseLeanId)
export class Dataset extends BaseModel implements IDataset {
  @prop({ default: uuidv4, unique: true, required: true })
  @IsString()
  public _id: string = null;

  @prop({ default: '' })
  @IsString()
  team_id: string;

  @prop({ default: '' })
  @IsString({ min: 4 })
  public name: string;

  @prop({ default: '' })
  @IsString({ empty: true })
  public description: string;

  @prop({ default: 'base768' })
  @IsString()
  public model_name: string;

  @prop({ default: DATASET_TYPE.KNOWLEDGE, enum: Object.values(DATASET_TYPE) })
  @Number({ enum: Object.values(DATASET_TYPE) })
  public type: number = DATASET_TYPE.KNOWLEDGE;

  @prop({ default: DATASET_STATUS.ACTIVE })
  @Number({ enum: Object.values(DATASET_STATUS) })
  public status: number = DATASET_STATUS.ACTIVE;

  @prop({ type: Object, default: null })
  @Field({})
  public vectorConfig: IDataset['vectorConfig'] | null = null;

  @prop({ historyIgnore: true })
  protected created: Date;

  @prop({ historyIgnore: true })
  protected updated: Date;
}

export const DatasetSchema = getSchema(Dataset);
export type DatasetType = Dataset & mongoose.Document;
