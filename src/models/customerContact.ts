import { plugin, pre, prop } from '@typegoose/typegoose';
import { Array, getSchema, Number, ObjectId, Schema, String as IsString } from 'fastest-validator-decorators';
import { ObjectIdNull } from '../types';
import { BaseModel, basePreSave } from './base.model';
import * as mongoose from 'mongoose';
import mongooseLeanId from '../utils/mongoPlugins/mongoLeanId';
import paginate from '../utils/mongoPlugins/paginate';
import { CUSTOMER_CONTACT_STATUS, ICustomerContact, ThreadInfo } from '../entities/customerContact.entity';
import { STATUSES } from '../entities/base.entity';

@Schema()
@pre<CustomerContact>('save', function (): Promise<void> {
  return basePreSave(this);
})
@plugin(paginate, { sort: '-created' })
@plugin(mongooseLeanId)
export class CustomerContact extends BaseModel implements ICustomerContact {
  @ObjectId({ optional: true })
  public _id: ObjectIdNull = null;

  @prop({ required: true, index: true })
  @IsString()
  public bot_uuid: string;

  @prop({ required: true, index: true })
  @IsString()
  public name: string;

  @prop({ default: '' })
  @IsString({ empty: true })
  public phone: string;

  @prop({ default: '' })
  @IsString({ empty: true })
  public address: string;

  @prop({ default: '' })
  @IsString({ empty: true })
  public note: string;

  @prop({ default: [], type: () => [Object], index: true })
  @Array({ items: 'object' })
  public threads: ThreadInfo[] = [];

  @prop({ default: STATUSES.ACTIVE })
  @Number({ enum: Object.values(CUSTOMER_CONTACT_STATUS) })
  public status: number = CUSTOMER_CONTACT_STATUS.ACTIVE;

  @prop({ historyIgnore: true })
  public created: Date;

  @prop({ historyIgnore: true })
  public updated: Date;
}

export const CustomerContactSchema = getSchema(CustomerContact);
export type CustomerContactType = CustomerContact & mongoose.Document;
