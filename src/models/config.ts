import { modelOptions, prop, Severity } from '@typegoose/typegoose';
import { getSchema, ObjectId, Schema, String } from 'fastest-validator-decorators';
import { BaseModel } from './base.model';
import { IConfigEntity } from '../entities/config.entity';
import * as mongoose from 'mongoose';
import { ConfigKeyType } from '../types/model.types';

@Schema()
@modelOptions({ options: { allowMixed: Severity.ALLOW } })
export class Config extends BaseModel implements IConfigEntity {
  @ObjectId()
  public _id: string;

  @prop({ unique: true, index: true })
  @String()
  public key: ConfigKeyType;

  @prop({ type: mongoose.Schema.Types.Mixed })
  public value: any;

  @prop({ default: true })
  public isDefault: boolean = true;

  @prop({ historyIgnore: true })
  protected created: Date;

  @prop({ historyIgnore: true })
  protected updated: Date;
}

export const ConfigSchema = getSchema(Config);
export type ConfigType = Config & mongoose.Document;
