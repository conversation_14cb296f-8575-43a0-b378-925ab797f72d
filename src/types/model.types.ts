export type ConfigListType = {
  'site.name': string;
  'site.url': string; /// process.env.SITE_URL || 'http://localhost:3000',

  'config.firebase_admin': string; // Connect with firebase admin. Ex: auth, db, analytics

  'mail.enabled': boolean; // true,
  'mail.from': string; // '<EMAIL>',

  'users.signup.enabled': boolean; // false, // Allow user sign up or not
  'users.default_team_package_id': string; // Used for add default package into team when register
  'users.defaultRole': number; // USER_ROLE.USER, // Default role when sign up
  'users.jwt.expiresIn': number; // 24 * 30, // In hours

  'telegram.bot_token': string; //  '',
  'telegram.all_user_ids': number[]; // [0],
  'telegram.admin_user_ids': number[]; // [0],
};

export type ConfigKeyType = keyof ConfigListType;

export type TeleGramMessageResult = {
  success: number;
  failed: number;
};
