import * as MoleculerTs from 'moleculer-ts';
import * as Moleculer from './moleculer';
import * as Services from './services.types';

export interface ServiceBroker {
  call<T extends ServiceActionNames>(
    actionName: T,
    params: GetCallParams[T],
    opts?: Moleculer.CallingOptions,
  ): Promise<GetCallReturn[T]>;

  emit<T extends ServiceEventNames>(eventName: T, payload: GetEmitParams[T], groups?: ServiceNamesEmitGroup): void;

  broadcast: ServiceBroker['emit'];
  broadcastLocal: ServiceBroker['emit'];
}

export type GetCallParams = {
  'botChat.actionChat': Services.BotChatServiceTypes.Actions[0]['in'];
  'botChat.actionDirectChat': Services.BotChatServiceTypes.Actions[1]['in'];
  'botChat.actionStartChat': Services.BotChatServiceTypes.Actions[2]['in'];
  'botChat.actionMoreHistory': Services.BotChatServiceTypes.Actions[3]['in'];
  'botChat.actionHook': Services.BotChatServiceTypes.Actions[4]['in'];
  'botSetting.actionList': Services.BotSettingServiceTypes.Actions[0]['in'];
  'botSetting.actionCreate': Services.BotSettingServiceTypes.Actions[1]['in'];
  'botSetting.actionUpdate': Services.BotSettingServiceTypes.Actions[2]['in'];
  'botSetting.actionGet': Services.BotSettingServiceTypes.Actions[3]['in'];
  'botSetting.actionLocalGet': Services.BotSettingServiceTypes.Actions[4]['in'];
  'botSetting.actionDelete': Services.BotSettingServiceTypes.Actions[5]['in'];
  'botSetting.getAllName': Services.BotSettingServiceTypes.Actions[6]['in'];
  'botSetting.createSnapshot': Services.BotSettingServiceTypes.Actions[7]['in'];
  'botSetting.listSnapshots': Services.BotSettingServiceTypes.Actions[8]['in'];
  'botSetting.deleteSnapshot': Services.BotSettingServiceTypes.Actions[9]['in'];
  'botSetting.restoreFromSnapshot': Services.BotSettingServiceTypes.Actions[10]['in'];
  'botSetting.publish': Services.BotSettingServiceTypes.Actions[11]['in'];
  'botSetting.updateLogo': Services.BotSettingServiceTypes.Actions[12]['in'];
  'botSetting.getLiveConfig': Services.BotSettingServiceTypes.Actions[13]['in'];
  'botSetting.convertToTemplate': Services.BotSettingServiceTypes.Actions[14]['in'];
  'botSetting.cloneFromTemplate': Services.BotSettingServiceTypes.Actions[15]['in'];
  'botSetting.getListTemplate': Services.BotSettingServiceTypes.Actions[16]['in'];
  'botSetting.find': Services.BotSettingServiceTypes.Actions[17]['in'];
  'botSetting.count': Services.BotSettingServiceTypes.Actions[18]['in'];
  'botSetting.insert': Services.BotSettingServiceTypes.Actions[19]['in'];
  'botSetting.get': Services.BotSettingServiceTypes.Actions[20]['in'];
  'botSetting.update': Services.BotSettingServiceTypes.Actions[21]['in'];
  'botSetting.remove': Services.BotSettingServiceTypes.Actions[22]['in'];
  'botSetting.list': Services.BotSettingServiceTypes.Actions[23]['in'];
  'botSetting.create': Services.BotSettingServiceTypes.Actions[24]['in'];
  'chatHistory.actionList': Services.ChatHistoryServiceTypes.Actions[0]['in'];
  'chatHistory.actionCreate': Services.ChatHistoryServiceTypes.Actions[1]['in'];
  'chatHistory.actionUpdate': Services.ChatHistoryServiceTypes.Actions[2]['in'];
  'chatHistory.actionGet': Services.ChatHistoryServiceTypes.Actions[3]['in'];
  'chatHistory.actionDelete': Services.ChatHistoryServiceTypes.Actions[4]['in'];
  'chatHistory.getPaginatedHistory': Services.ChatHistoryServiceTypes.Actions[5]['in'];
  'chatHistory.actionGetSessionThread': Services.ChatHistoryServiceTypes.Actions[6]['in'];
  'chatHistory.changeChatMode': Services.ChatHistoryServiceTypes.Actions[7]['in'];
  'chatHistory.localChangeChatMode': Services.ChatHistoryServiceTypes.Actions[8]['in'];
  'chatHistory.actionSaveSessionHistoryRecord': Services.ChatHistoryServiceTypes.Actions[9]['in'];
  'chatHistory.actionGetThreadIdByBotUUID': Services.ChatHistoryServiceTypes.Actions[10]['in'];
  'chatHistory.actionGetBotChatHistoryByThreadId': Services.ChatHistoryServiceTypes.Actions[11]['in'];
  'chatHistory.find': Services.ChatHistoryServiceTypes.Actions[12]['in'];
  'chatHistory.count': Services.ChatHistoryServiceTypes.Actions[13]['in'];
  'chatHistory.insert': Services.ChatHistoryServiceTypes.Actions[14]['in'];
  'chatHistory.get': Services.ChatHistoryServiceTypes.Actions[15]['in'];
  'chatHistory.update': Services.ChatHistoryServiceTypes.Actions[16]['in'];
  'chatHistory.remove': Services.ChatHistoryServiceTypes.Actions[17]['in'];
  'chatHistory.list': Services.ChatHistoryServiceTypes.Actions[18]['in'];
  'chatHistory.create': Services.ChatHistoryServiceTypes.Actions[19]['in'];
  'config.actionGet': Services.ConfigServiceTypes.Actions[0]['in'];
  'config.actionSet': Services.ConfigServiceTypes.Actions[1]['in'];
  'config.actionAll': Services.ConfigServiceTypes.Actions[2]['in'];
  'config.actionMigrate': Services.ConfigServiceTypes.Actions[3]['in'];
  'coreAi.actionProcessFlow': Services.CoreAiServiceTypes.Actions[0]['in'];
  'coreAi.actionGetTopic': Services.CoreAiServiceTypes.Actions[1]['in'];
  'coreAi.actionGetOpenAiEmbedding': Services.CoreAiServiceTypes.Actions[2]['in'];
  'coreAi.actionGetOpenAiChat': Services.CoreAiServiceTypes.Actions[3]['in'];
  'coreAi.respondWithOllama': Services.CoreAiServiceTypes.Actions[4]['in'];
  'customerContact.actionCreate': Services.CustomerContactServiceTypes.Actions[0]['in'];
  'customerContact.actionUpdate': Services.CustomerContactServiceTypes.Actions[1]['in'];
  'customerContact.actionGet': Services.CustomerContactServiceTypes.Actions[2]['in'];
  'customerContact.actionDelete': Services.CustomerContactServiceTypes.Actions[3]['in'];
  'customerContact.actionQuickSearch': Services.CustomerContactServiceTypes.Actions[4]['in'];
  'customerContact.actionLinkChatHistory': Services.CustomerContactServiceTypes.Actions[5]['in'];
  'customerContact.actionUnlinkChatHistory': Services.CustomerContactServiceTypes.Actions[6]['in'];
  'customerContact.actionGetByThreadId': Services.CustomerContactServiceTypes.Actions[7]['in'];
  'customerContact.actionQuickCreateFromChatHistory': Services.CustomerContactServiceTypes.Actions[8]['in'];
  'customerContact.actionList': Services.CustomerContactServiceTypes.Actions[9]['in'];
  'customerContact.find': Services.CustomerContactServiceTypes.Actions[10]['in'];
  'customerContact.count': Services.CustomerContactServiceTypes.Actions[11]['in'];
  'customerContact.insert': Services.CustomerContactServiceTypes.Actions[12]['in'];
  'customerContact.get': Services.CustomerContactServiceTypes.Actions[13]['in'];
  'customerContact.update': Services.CustomerContactServiceTypes.Actions[14]['in'];
  'customerContact.remove': Services.CustomerContactServiceTypes.Actions[15]['in'];
  'customerContact.list': Services.CustomerContactServiceTypes.Actions[16]['in'];
  'customerContact.create': Services.CustomerContactServiceTypes.Actions[17]['in'];
  'dataset.actionList': Services.DatasetServiceTypes.Actions[0]['in'];
  'dataset.actionCreate': Services.DatasetServiceTypes.Actions[1]['in'];
  'dataset.actionUpdate': Services.DatasetServiceTypes.Actions[2]['in'];
  'dataset.actionGet': Services.DatasetServiceTypes.Actions[3]['in'];
  'dataset.actionDelete': Services.DatasetServiceTypes.Actions[4]['in'];
  'dataset.cleanKnowledge': Services.DatasetServiceTypes.Actions[5]['in'];
  'dataset.getKnowledge': Services.DatasetServiceTypes.Actions[6]['in'];
  'dataset.retrieveKnowledge': Services.DatasetServiceTypes.Actions[7]['in'];
  'dataset.actionSearchKnowledge': Services.DatasetServiceTypes.Actions[8]['in'];
  'dataset.deleteKnowledge': Services.DatasetServiceTypes.Actions[9]['in'];
  'dataset.createKnowledge': Services.DatasetServiceTypes.Actions[10]['in'];
  'dataset.actionImportKnowledgeUrl': Services.DatasetServiceTypes.Actions[11]['in'];
  'dataset.updateKnowledge': Services.DatasetServiceTypes.Actions[12]['in'];
  'dataset.getAllTeamDatasetName': Services.DatasetServiceTypes.Actions[13]['in'];
  'dataset.find': Services.DatasetServiceTypes.Actions[14]['in'];
  'dataset.count': Services.DatasetServiceTypes.Actions[15]['in'];
  'dataset.insert': Services.DatasetServiceTypes.Actions[16]['in'];
  'dataset.get': Services.DatasetServiceTypes.Actions[17]['in'];
  'dataset.update': Services.DatasetServiceTypes.Actions[18]['in'];
  'dataset.remove': Services.DatasetServiceTypes.Actions[19]['in'];
  'dataset.list': Services.DatasetServiceTypes.Actions[20]['in'];
  'dataset.create': Services.DatasetServiceTypes.Actions[21]['in'];
  'devTest.actionStressTestRecord': Services.DevTestServiceTypes.Actions[0]['in'];
  'devTest.actionStressTestSearch': Services.DevTestServiceTypes.Actions[1]['in'];
  'devTest.actionCurrentTest': Services.DevTestServiceTypes.Actions[2]['in'];
  'devTest.actionVMTest': Services.DevTestServiceTypes.Actions[3]['in'];
  'devTest.whatMyIP': Services.DevTestServiceTypes.Actions[4]['in'];
  'devTest.actionCleanCache': Services.DevTestServiceTypes.Actions[5]['in'];
  'model768.getEmbedding': Services.Model768ServiceTypes.Actions[0]['in'];
  'package.actionCreate': Services.PackageServiceTypes.Actions[0]['in'];
  'package.actionUpdate': Services.PackageServiceTypes.Actions[1]['in'];
  'package.actionDelete': Services.PackageServiceTypes.Actions[2]['in'];
  'package.actionGet': Services.PackageServiceTypes.Actions[3]['in'];
  'package.actionList': Services.PackageServiceTypes.Actions[4]['in'];
  'package.actionGetAllName': Services.PackageServiceTypes.Actions[5]['in'];
  'package.find': Services.PackageServiceTypes.Actions[6]['in'];
  'package.count': Services.PackageServiceTypes.Actions[7]['in'];
  'package.insert': Services.PackageServiceTypes.Actions[8]['in'];
  'package.get': Services.PackageServiceTypes.Actions[9]['in'];
  'package.update': Services.PackageServiceTypes.Actions[10]['in'];
  'package.remove': Services.PackageServiceTypes.Actions[11]['in'];
  'package.list': Services.PackageServiceTypes.Actions[12]['in'];
  'package.create': Services.PackageServiceTypes.Actions[13]['in'];
  'socketConnector.connect': Services.SocketConnectorServiceTypes.Actions[0]['in'];
  'socketConnector.sendClientTask': Services.SocketConnectorServiceTypes.Actions[1]['in'];
  'socketConnector.getActiveConnections': Services.SocketConnectorServiceTypes.Actions[2]['in'];
  'socketConnector.sendFacebookMessage': Services.SocketConnectorServiceTypes.Actions[3]['in'];
  'socketConnector.sendFacebookFeedReply': Services.SocketConnectorServiceTypes.Actions[4]['in'];
  'teamPackage.actionAssign': Services.TeamPackageServiceTypes.Actions[0]['in'];
  'teamPackage.actionExtend': Services.TeamPackageServiceTypes.Actions[1]['in'];
  'teamPackage.actionUpdateUsage': Services.TeamPackageServiceTypes.Actions[2]['in'];
  'teamPackage.actionGetCurrent': Services.TeamPackageServiceTypes.Actions[3]['in'];
  'teamPackage.actionList': Services.TeamPackageServiceTypes.Actions[4]['in'];
  'teamPackage.actionCheckCreditLimits': Services.TeamPackageServiceTypes.Actions[5]['in'];
  'teamPackage.actionAddCreditUsage': Services.TeamPackageServiceTypes.Actions[6]['in'];
  'teamPackage.actionUpdateTeamPackagesByPackageId': Services.TeamPackageServiceTypes.Actions[7]['in'];
  'teamPackage.find': Services.TeamPackageServiceTypes.Actions[8]['in'];
  'teamPackage.count': Services.TeamPackageServiceTypes.Actions[9]['in'];
  'teamPackage.insert': Services.TeamPackageServiceTypes.Actions[10]['in'];
  'teamPackage.get': Services.TeamPackageServiceTypes.Actions[11]['in'];
  'teamPackage.update': Services.TeamPackageServiceTypes.Actions[12]['in'];
  'teamPackage.remove': Services.TeamPackageServiceTypes.Actions[13]['in'];
  'teamPackage.list': Services.TeamPackageServiceTypes.Actions[14]['in'];
  'teamPackage.create': Services.TeamPackageServiceTypes.Actions[15]['in'];
  'telegram.actionMessageAll': Services.TelegramServiceTypes.Actions[0]['in'];
  'telegram.actionMessageAdmin': Services.TelegramServiceTypes.Actions[1]['in'];
  'telegram.actionMessageUser': Services.TelegramServiceTypes.Actions[2]['in'];
  'telegram.actionSystemMessage': Services.TelegramServiceTypes.Actions[3]['in'];
  'tokenUsage.getMe': Services.TokenUsageServiceTypes.Actions[0]['in'];
  'tokenUsage.getByTeam': Services.TokenUsageServiceTypes.Actions[1]['in'];
  'tokenUsage.addTokenUsage': Services.TokenUsageServiceTypes.Actions[2]['in'];
  'tokenUsage.find': Services.TokenUsageServiceTypes.Actions[3]['in'];
  'tokenUsage.count': Services.TokenUsageServiceTypes.Actions[4]['in'];
  'tokenUsage.insert': Services.TokenUsageServiceTypes.Actions[5]['in'];
  'tokenUsage.get': Services.TokenUsageServiceTypes.Actions[6]['in'];
  'tokenUsage.update': Services.TokenUsageServiceTypes.Actions[7]['in'];
  'tokenUsage.remove': Services.TokenUsageServiceTypes.Actions[8]['in'];
  'tokenUsage.list': Services.TokenUsageServiceTypes.Actions[9]['in'];
  'tokenUsage.create': Services.TokenUsageServiceTypes.Actions[10]['in'];
  'tokenUsageDaily.addTokenUsage': Services.TokenUsageDailyServiceTypes.Actions[0]['in'];
  'tokenUsageDaily.reportUsage': Services.TokenUsageDailyServiceTypes.Actions[1]['in'];
  'tokenUsageDaily.find': Services.TokenUsageDailyServiceTypes.Actions[2]['in'];
  'tokenUsageDaily.count': Services.TokenUsageDailyServiceTypes.Actions[3]['in'];
  'tokenUsageDaily.insert': Services.TokenUsageDailyServiceTypes.Actions[4]['in'];
  'tokenUsageDaily.get': Services.TokenUsageDailyServiceTypes.Actions[5]['in'];
  'tokenUsageDaily.update': Services.TokenUsageDailyServiceTypes.Actions[6]['in'];
  'tokenUsageDaily.remove': Services.TokenUsageDailyServiceTypes.Actions[7]['in'];
  'tokenUsageDaily.list': Services.TokenUsageDailyServiceTypes.Actions[8]['in'];
  'tokenUsageDaily.create': Services.TokenUsageDailyServiceTypes.Actions[9]['in'];
  'user.actionRegister': Services.UserServiceTypes.Actions[0]['in'];
  'user.actionLogin': Services.UserServiceTypes.Actions[1]['in'];
  'user.actionFirebaseLogin': Services.UserServiceTypes.Actions[2]['in'];
  'user.actionLogout': Services.UserServiceTypes.Actions[3]['in'];
  'user.updateUserStatus': Services.UserServiceTypes.Actions[4]['in'];
  'user.actionMe': Services.UserServiceTypes.Actions[5]['in'];
  'user.actionMeUpdate': Services.UserServiceTypes.Actions[6]['in'];
  'user.actionVerifyToken': Services.UserServiceTypes.Actions[7]['in'];
  'user.actionList': Services.UserServiceTypes.Actions[8]['in'];
  'user.find': Services.UserServiceTypes.Actions[9]['in'];
  'user.count': Services.UserServiceTypes.Actions[10]['in'];
  'user.insert': Services.UserServiceTypes.Actions[11]['in'];
  'user.get': Services.UserServiceTypes.Actions[12]['in'];
  'user.update': Services.UserServiceTypes.Actions[13]['in'];
  'user.remove': Services.UserServiceTypes.Actions[14]['in'];
  'user.list': Services.UserServiceTypes.Actions[15]['in'];
  'user.create': Services.UserServiceTypes.Actions[16]['in'];
  'vectorStore.processCsv': Services.VectorStoreServiceTypes.Actions[0]['in'];
  'vectorStore.createCollection': Services.VectorStoreServiceTypes.Actions[1]['in'];
  'vectorStore.cleanCollection': Services.VectorStoreServiceTypes.Actions[2]['in'];
  'vectorStore.getData': Services.VectorStoreServiceTypes.Actions[3]['in'];
  'vectorStore.countCollection': Services.VectorStoreServiceTypes.Actions[4]['in'];
  'vectorStore.delete': Services.VectorStoreServiceTypes.Actions[5]['in'];
  'vectorStore.search': Services.VectorStoreServiceTypes.Actions[6]['in'];
  'vectorStore.create': Services.VectorStoreServiceTypes.Actions[7]['in'];
  'vectorStore.update': Services.VectorStoreServiceTypes.Actions[8]['in'];
  'workGate.actionList': Services.WorkGateServiceTypes.Actions[0]['in'];
  'workGate.actionCount': Services.WorkGateServiceTypes.Actions[1]['in'];
  'workGate.actionCreate': Services.WorkGateServiceTypes.Actions[2]['in'];
  'workGate.actionUpdateGateConfig': Services.WorkGateServiceTypes.Actions[3]['in'];
  'workGate.actionLocalGet': Services.WorkGateServiceTypes.Actions[4]['in'];
  'workGate.actionUpdate': Services.WorkGateServiceTypes.Actions[5]['in'];
  'workGate.actionGet': Services.WorkGateServiceTypes.Actions[6]['in'];
  'workGate.actionDelete': Services.WorkGateServiceTypes.Actions[7]['in'];
  'workGate.getConfig': Services.WorkGateServiceTypes.Actions[8]['in'];
  'workGate.getListFacebookPosts': Services.WorkGateServiceTypes.Actions[9]['in'];
  'workGate.actionImportFacebookPages': Services.WorkGateServiceTypes.Actions[10]['in'];
  'workGate.actionPostWebhook': Services.WorkGateServiceTypes.Actions[11]['in'];
  'workGate.actionGetWebhook': Services.WorkGateServiceTypes.Actions[12]['in'];
  'workGate.getAllZaloQr': Services.WorkGateServiceTypes.Actions[13]['in'];
  'workGate.find': Services.WorkGateServiceTypes.Actions[14]['in'];
  'workGate.count': Services.WorkGateServiceTypes.Actions[15]['in'];
  'workGate.insert': Services.WorkGateServiceTypes.Actions[16]['in'];
  'workGate.get': Services.WorkGateServiceTypes.Actions[17]['in'];
  'workGate.update': Services.WorkGateServiceTypes.Actions[18]['in'];
  'workGate.remove': Services.WorkGateServiceTypes.Actions[19]['in'];
  'workGate.list': Services.WorkGateServiceTypes.Actions[20]['in'];
  'workGate.create': Services.WorkGateServiceTypes.Actions[21]['in'];
  'zalo.getAuthStatus': Services.ZaloServiceTypes.Actions[0]['in'];
  'zalo.loginQR': Services.ZaloServiceTypes.Actions[1]['in'];
  'zalo.removeAccount': Services.ZaloServiceTypes.Actions[2]['in'];
  'zalo.sendTextMessage': Services.ZaloServiceTypes.Actions[3]['in'];
  'zalo.sendGroupMessage': Services.ZaloServiceTypes.Actions[4]['in'];
  'zalo.logout': Services.ZaloServiceTypes.Actions[5]['in'];
  'zalo.logoutAll': Services.ZaloServiceTypes.Actions[6]['in'];
};

export type GetCallReturn = {
  'botChat.actionChat': Services.BotChatServiceTypes.Actions[0]['out'];
  'botChat.actionDirectChat': Services.BotChatServiceTypes.Actions[1]['out'];
  'botChat.actionStartChat': Services.BotChatServiceTypes.Actions[2]['out'];
  'botChat.actionMoreHistory': Services.BotChatServiceTypes.Actions[3]['out'];
  'botChat.actionHook': Services.BotChatServiceTypes.Actions[4]['out'];
  'botSetting.actionList': Services.BotSettingServiceTypes.Actions[0]['out'];
  'botSetting.actionCreate': Services.BotSettingServiceTypes.Actions[1]['out'];
  'botSetting.actionUpdate': Services.BotSettingServiceTypes.Actions[2]['out'];
  'botSetting.actionGet': Services.BotSettingServiceTypes.Actions[3]['out'];
  'botSetting.actionLocalGet': Services.BotSettingServiceTypes.Actions[4]['out'];
  'botSetting.actionDelete': Services.BotSettingServiceTypes.Actions[5]['out'];
  'botSetting.getAllName': Services.BotSettingServiceTypes.Actions[6]['out'];
  'botSetting.createSnapshot': Services.BotSettingServiceTypes.Actions[7]['out'];
  'botSetting.listSnapshots': Services.BotSettingServiceTypes.Actions[8]['out'];
  'botSetting.deleteSnapshot': Services.BotSettingServiceTypes.Actions[9]['out'];
  'botSetting.restoreFromSnapshot': Services.BotSettingServiceTypes.Actions[10]['out'];
  'botSetting.publish': Services.BotSettingServiceTypes.Actions[11]['out'];
  'botSetting.updateLogo': Services.BotSettingServiceTypes.Actions[12]['out'];
  'botSetting.getLiveConfig': Services.BotSettingServiceTypes.Actions[13]['out'];
  'botSetting.convertToTemplate': Services.BotSettingServiceTypes.Actions[14]['out'];
  'botSetting.cloneFromTemplate': Services.BotSettingServiceTypes.Actions[15]['out'];
  'botSetting.getListTemplate': Services.BotSettingServiceTypes.Actions[16]['out'];
  'botSetting.find': Services.BotSettingServiceTypes.Actions[17]['out'];
  'botSetting.count': Services.BotSettingServiceTypes.Actions[18]['out'];
  'botSetting.insert': Services.BotSettingServiceTypes.Actions[19]['out'];
  'botSetting.get': Services.BotSettingServiceTypes.Actions[20]['out'];
  'botSetting.update': Services.BotSettingServiceTypes.Actions[21]['out'];
  'botSetting.remove': Services.BotSettingServiceTypes.Actions[22]['out'];
  'botSetting.list': Services.BotSettingServiceTypes.Actions[23]['out'];
  'botSetting.create': Services.BotSettingServiceTypes.Actions[24]['out'];
  'chatHistory.actionList': Services.ChatHistoryServiceTypes.Actions[0]['out'];
  'chatHistory.actionCreate': Services.ChatHistoryServiceTypes.Actions[1]['out'];
  'chatHistory.actionUpdate': Services.ChatHistoryServiceTypes.Actions[2]['out'];
  'chatHistory.actionGet': Services.ChatHistoryServiceTypes.Actions[3]['out'];
  'chatHistory.actionDelete': Services.ChatHistoryServiceTypes.Actions[4]['out'];
  'chatHistory.getPaginatedHistory': Services.ChatHistoryServiceTypes.Actions[5]['out'];
  'chatHistory.actionGetSessionThread': Services.ChatHistoryServiceTypes.Actions[6]['out'];
  'chatHistory.changeChatMode': Services.ChatHistoryServiceTypes.Actions[7]['out'];
  'chatHistory.localChangeChatMode': Services.ChatHistoryServiceTypes.Actions[8]['out'];
  'chatHistory.actionSaveSessionHistoryRecord': Services.ChatHistoryServiceTypes.Actions[9]['out'];
  'chatHistory.actionGetThreadIdByBotUUID': Services.ChatHistoryServiceTypes.Actions[10]['out'];
  'chatHistory.actionGetBotChatHistoryByThreadId': Services.ChatHistoryServiceTypes.Actions[11]['out'];
  'chatHistory.find': Services.ChatHistoryServiceTypes.Actions[12]['out'];
  'chatHistory.count': Services.ChatHistoryServiceTypes.Actions[13]['out'];
  'chatHistory.insert': Services.ChatHistoryServiceTypes.Actions[14]['out'];
  'chatHistory.get': Services.ChatHistoryServiceTypes.Actions[15]['out'];
  'chatHistory.update': Services.ChatHistoryServiceTypes.Actions[16]['out'];
  'chatHistory.remove': Services.ChatHistoryServiceTypes.Actions[17]['out'];
  'chatHistory.list': Services.ChatHistoryServiceTypes.Actions[18]['out'];
  'chatHistory.create': Services.ChatHistoryServiceTypes.Actions[19]['out'];
  'config.actionGet': Services.ConfigServiceTypes.Actions[0]['out'];
  'config.actionSet': Services.ConfigServiceTypes.Actions[1]['out'];
  'config.actionAll': Services.ConfigServiceTypes.Actions[2]['out'];
  'config.actionMigrate': Services.ConfigServiceTypes.Actions[3]['out'];
  'coreAi.actionProcessFlow': Services.CoreAiServiceTypes.Actions[0]['out'];
  'coreAi.actionGetTopic': Services.CoreAiServiceTypes.Actions[1]['out'];
  'coreAi.actionGetOpenAiEmbedding': Services.CoreAiServiceTypes.Actions[2]['out'];
  'coreAi.actionGetOpenAiChat': Services.CoreAiServiceTypes.Actions[3]['out'];
  'coreAi.respondWithOllama': Services.CoreAiServiceTypes.Actions[4]['out'];
  'customerContact.actionCreate': Services.CustomerContactServiceTypes.Actions[0]['out'];
  'customerContact.actionUpdate': Services.CustomerContactServiceTypes.Actions[1]['out'];
  'customerContact.actionGet': Services.CustomerContactServiceTypes.Actions[2]['out'];
  'customerContact.actionDelete': Services.CustomerContactServiceTypes.Actions[3]['out'];
  'customerContact.actionQuickSearch': Services.CustomerContactServiceTypes.Actions[4]['out'];
  'customerContact.actionLinkChatHistory': Services.CustomerContactServiceTypes.Actions[5]['out'];
  'customerContact.actionUnlinkChatHistory': Services.CustomerContactServiceTypes.Actions[6]['out'];
  'customerContact.actionGetByThreadId': Services.CustomerContactServiceTypes.Actions[7]['out'];
  'customerContact.actionQuickCreateFromChatHistory': Services.CustomerContactServiceTypes.Actions[8]['out'];
  'customerContact.actionList': Services.CustomerContactServiceTypes.Actions[9]['out'];
  'customerContact.find': Services.CustomerContactServiceTypes.Actions[10]['out'];
  'customerContact.count': Services.CustomerContactServiceTypes.Actions[11]['out'];
  'customerContact.insert': Services.CustomerContactServiceTypes.Actions[12]['out'];
  'customerContact.get': Services.CustomerContactServiceTypes.Actions[13]['out'];
  'customerContact.update': Services.CustomerContactServiceTypes.Actions[14]['out'];
  'customerContact.remove': Services.CustomerContactServiceTypes.Actions[15]['out'];
  'customerContact.list': Services.CustomerContactServiceTypes.Actions[16]['out'];
  'customerContact.create': Services.CustomerContactServiceTypes.Actions[17]['out'];
  'dataset.actionList': Services.DatasetServiceTypes.Actions[0]['out'];
  'dataset.actionCreate': Services.DatasetServiceTypes.Actions[1]['out'];
  'dataset.actionUpdate': Services.DatasetServiceTypes.Actions[2]['out'];
  'dataset.actionGet': Services.DatasetServiceTypes.Actions[3]['out'];
  'dataset.actionDelete': Services.DatasetServiceTypes.Actions[4]['out'];
  'dataset.cleanKnowledge': Services.DatasetServiceTypes.Actions[5]['out'];
  'dataset.getKnowledge': Services.DatasetServiceTypes.Actions[6]['out'];
  'dataset.retrieveKnowledge': Services.DatasetServiceTypes.Actions[7]['out'];
  'dataset.actionSearchKnowledge': Services.DatasetServiceTypes.Actions[8]['out'];
  'dataset.deleteKnowledge': Services.DatasetServiceTypes.Actions[9]['out'];
  'dataset.createKnowledge': Services.DatasetServiceTypes.Actions[10]['out'];
  'dataset.actionImportKnowledgeUrl': Services.DatasetServiceTypes.Actions[11]['out'];
  'dataset.updateKnowledge': Services.DatasetServiceTypes.Actions[12]['out'];
  'dataset.getAllTeamDatasetName': Services.DatasetServiceTypes.Actions[13]['out'];
  'dataset.find': Services.DatasetServiceTypes.Actions[14]['out'];
  'dataset.count': Services.DatasetServiceTypes.Actions[15]['out'];
  'dataset.insert': Services.DatasetServiceTypes.Actions[16]['out'];
  'dataset.get': Services.DatasetServiceTypes.Actions[17]['out'];
  'dataset.update': Services.DatasetServiceTypes.Actions[18]['out'];
  'dataset.remove': Services.DatasetServiceTypes.Actions[19]['out'];
  'dataset.list': Services.DatasetServiceTypes.Actions[20]['out'];
  'dataset.create': Services.DatasetServiceTypes.Actions[21]['out'];
  'devTest.actionStressTestRecord': Services.DevTestServiceTypes.Actions[0]['out'];
  'devTest.actionStressTestSearch': Services.DevTestServiceTypes.Actions[1]['out'];
  'devTest.actionCurrentTest': Services.DevTestServiceTypes.Actions[2]['out'];
  'devTest.actionVMTest': Services.DevTestServiceTypes.Actions[3]['out'];
  'devTest.whatMyIP': Services.DevTestServiceTypes.Actions[4]['out'];
  'devTest.actionCleanCache': Services.DevTestServiceTypes.Actions[5]['out'];
  'model768.getEmbedding': Services.Model768ServiceTypes.Actions[0]['out'];
  'package.actionCreate': Services.PackageServiceTypes.Actions[0]['out'];
  'package.actionUpdate': Services.PackageServiceTypes.Actions[1]['out'];
  'package.actionDelete': Services.PackageServiceTypes.Actions[2]['out'];
  'package.actionGet': Services.PackageServiceTypes.Actions[3]['out'];
  'package.actionList': Services.PackageServiceTypes.Actions[4]['out'];
  'package.actionGetAllName': Services.PackageServiceTypes.Actions[5]['out'];
  'package.find': Services.PackageServiceTypes.Actions[6]['out'];
  'package.count': Services.PackageServiceTypes.Actions[7]['out'];
  'package.insert': Services.PackageServiceTypes.Actions[8]['out'];
  'package.get': Services.PackageServiceTypes.Actions[9]['out'];
  'package.update': Services.PackageServiceTypes.Actions[10]['out'];
  'package.remove': Services.PackageServiceTypes.Actions[11]['out'];
  'package.list': Services.PackageServiceTypes.Actions[12]['out'];
  'package.create': Services.PackageServiceTypes.Actions[13]['out'];
  'socketConnector.connect': Services.SocketConnectorServiceTypes.Actions[0]['out'];
  'socketConnector.sendClientTask': Services.SocketConnectorServiceTypes.Actions[1]['out'];
  'socketConnector.getActiveConnections': Services.SocketConnectorServiceTypes.Actions[2]['out'];
  'socketConnector.sendFacebookMessage': Services.SocketConnectorServiceTypes.Actions[3]['out'];
  'socketConnector.sendFacebookFeedReply': Services.SocketConnectorServiceTypes.Actions[4]['out'];
  'teamPackage.actionAssign': Services.TeamPackageServiceTypes.Actions[0]['out'];
  'teamPackage.actionExtend': Services.TeamPackageServiceTypes.Actions[1]['out'];
  'teamPackage.actionUpdateUsage': Services.TeamPackageServiceTypes.Actions[2]['out'];
  'teamPackage.actionGetCurrent': Services.TeamPackageServiceTypes.Actions[3]['out'];
  'teamPackage.actionList': Services.TeamPackageServiceTypes.Actions[4]['out'];
  'teamPackage.actionCheckCreditLimits': Services.TeamPackageServiceTypes.Actions[5]['out'];
  'teamPackage.actionAddCreditUsage': Services.TeamPackageServiceTypes.Actions[6]['out'];
  'teamPackage.actionUpdateTeamPackagesByPackageId': Services.TeamPackageServiceTypes.Actions[7]['out'];
  'teamPackage.find': Services.TeamPackageServiceTypes.Actions[8]['out'];
  'teamPackage.count': Services.TeamPackageServiceTypes.Actions[9]['out'];
  'teamPackage.insert': Services.TeamPackageServiceTypes.Actions[10]['out'];
  'teamPackage.get': Services.TeamPackageServiceTypes.Actions[11]['out'];
  'teamPackage.update': Services.TeamPackageServiceTypes.Actions[12]['out'];
  'teamPackage.remove': Services.TeamPackageServiceTypes.Actions[13]['out'];
  'teamPackage.list': Services.TeamPackageServiceTypes.Actions[14]['out'];
  'teamPackage.create': Services.TeamPackageServiceTypes.Actions[15]['out'];
  'telegram.actionMessageAll': Services.TelegramServiceTypes.Actions[0]['out'];
  'telegram.actionMessageAdmin': Services.TelegramServiceTypes.Actions[1]['out'];
  'telegram.actionMessageUser': Services.TelegramServiceTypes.Actions[2]['out'];
  'telegram.actionSystemMessage': Services.TelegramServiceTypes.Actions[3]['out'];
  'tokenUsage.getMe': Services.TokenUsageServiceTypes.Actions[0]['out'];
  'tokenUsage.getByTeam': Services.TokenUsageServiceTypes.Actions[1]['out'];
  'tokenUsage.addTokenUsage': Services.TokenUsageServiceTypes.Actions[2]['out'];
  'tokenUsage.find': Services.TokenUsageServiceTypes.Actions[3]['out'];
  'tokenUsage.count': Services.TokenUsageServiceTypes.Actions[4]['out'];
  'tokenUsage.insert': Services.TokenUsageServiceTypes.Actions[5]['out'];
  'tokenUsage.get': Services.TokenUsageServiceTypes.Actions[6]['out'];
  'tokenUsage.update': Services.TokenUsageServiceTypes.Actions[7]['out'];
  'tokenUsage.remove': Services.TokenUsageServiceTypes.Actions[8]['out'];
  'tokenUsage.list': Services.TokenUsageServiceTypes.Actions[9]['out'];
  'tokenUsage.create': Services.TokenUsageServiceTypes.Actions[10]['out'];
  'tokenUsageDaily.addTokenUsage': Services.TokenUsageDailyServiceTypes.Actions[0]['out'];
  'tokenUsageDaily.reportUsage': Services.TokenUsageDailyServiceTypes.Actions[1]['out'];
  'tokenUsageDaily.find': Services.TokenUsageDailyServiceTypes.Actions[2]['out'];
  'tokenUsageDaily.count': Services.TokenUsageDailyServiceTypes.Actions[3]['out'];
  'tokenUsageDaily.insert': Services.TokenUsageDailyServiceTypes.Actions[4]['out'];
  'tokenUsageDaily.get': Services.TokenUsageDailyServiceTypes.Actions[5]['out'];
  'tokenUsageDaily.update': Services.TokenUsageDailyServiceTypes.Actions[6]['out'];
  'tokenUsageDaily.remove': Services.TokenUsageDailyServiceTypes.Actions[7]['out'];
  'tokenUsageDaily.list': Services.TokenUsageDailyServiceTypes.Actions[8]['out'];
  'tokenUsageDaily.create': Services.TokenUsageDailyServiceTypes.Actions[9]['out'];
  'user.actionRegister': Services.UserServiceTypes.Actions[0]['out'];
  'user.actionLogin': Services.UserServiceTypes.Actions[1]['out'];
  'user.actionFirebaseLogin': Services.UserServiceTypes.Actions[2]['out'];
  'user.actionLogout': Services.UserServiceTypes.Actions[3]['out'];
  'user.updateUserStatus': Services.UserServiceTypes.Actions[4]['out'];
  'user.actionMe': Services.UserServiceTypes.Actions[5]['out'];
  'user.actionMeUpdate': Services.UserServiceTypes.Actions[6]['out'];
  'user.actionVerifyToken': Services.UserServiceTypes.Actions[7]['out'];
  'user.actionList': Services.UserServiceTypes.Actions[8]['out'];
  'user.find': Services.UserServiceTypes.Actions[9]['out'];
  'user.count': Services.UserServiceTypes.Actions[10]['out'];
  'user.insert': Services.UserServiceTypes.Actions[11]['out'];
  'user.get': Services.UserServiceTypes.Actions[12]['out'];
  'user.update': Services.UserServiceTypes.Actions[13]['out'];
  'user.remove': Services.UserServiceTypes.Actions[14]['out'];
  'user.list': Services.UserServiceTypes.Actions[15]['out'];
  'user.create': Services.UserServiceTypes.Actions[16]['out'];
  'vectorStore.processCsv': Services.VectorStoreServiceTypes.Actions[0]['out'];
  'vectorStore.createCollection': Services.VectorStoreServiceTypes.Actions[1]['out'];
  'vectorStore.cleanCollection': Services.VectorStoreServiceTypes.Actions[2]['out'];
  'vectorStore.getData': Services.VectorStoreServiceTypes.Actions[3]['out'];
  'vectorStore.countCollection': Services.VectorStoreServiceTypes.Actions[4]['out'];
  'vectorStore.delete': Services.VectorStoreServiceTypes.Actions[5]['out'];
  'vectorStore.search': Services.VectorStoreServiceTypes.Actions[6]['out'];
  'vectorStore.create': Services.VectorStoreServiceTypes.Actions[7]['out'];
  'vectorStore.update': Services.VectorStoreServiceTypes.Actions[8]['out'];
  'workGate.actionList': Services.WorkGateServiceTypes.Actions[0]['out'];
  'workGate.actionCount': Services.WorkGateServiceTypes.Actions[1]['out'];
  'workGate.actionCreate': Services.WorkGateServiceTypes.Actions[2]['out'];
  'workGate.actionUpdateGateConfig': Services.WorkGateServiceTypes.Actions[3]['out'];
  'workGate.actionLocalGet': Services.WorkGateServiceTypes.Actions[4]['out'];
  'workGate.actionUpdate': Services.WorkGateServiceTypes.Actions[5]['out'];
  'workGate.actionGet': Services.WorkGateServiceTypes.Actions[6]['out'];
  'workGate.actionDelete': Services.WorkGateServiceTypes.Actions[7]['out'];
  'workGate.getConfig': Services.WorkGateServiceTypes.Actions[8]['out'];
  'workGate.getListFacebookPosts': Services.WorkGateServiceTypes.Actions[9]['out'];
  'workGate.actionImportFacebookPages': Services.WorkGateServiceTypes.Actions[10]['out'];
  'workGate.actionPostWebhook': Services.WorkGateServiceTypes.Actions[11]['out'];
  'workGate.actionGetWebhook': Services.WorkGateServiceTypes.Actions[12]['out'];
  'workGate.getAllZaloQr': Services.WorkGateServiceTypes.Actions[13]['out'];
  'workGate.find': Services.WorkGateServiceTypes.Actions[14]['out'];
  'workGate.count': Services.WorkGateServiceTypes.Actions[15]['out'];
  'workGate.insert': Services.WorkGateServiceTypes.Actions[16]['out'];
  'workGate.get': Services.WorkGateServiceTypes.Actions[17]['out'];
  'workGate.update': Services.WorkGateServiceTypes.Actions[18]['out'];
  'workGate.remove': Services.WorkGateServiceTypes.Actions[19]['out'];
  'workGate.list': Services.WorkGateServiceTypes.Actions[20]['out'];
  'workGate.create': Services.WorkGateServiceTypes.Actions[21]['out'];
  'zalo.getAuthStatus': Services.ZaloServiceTypes.Actions[0]['out'];
  'zalo.loginQR': Services.ZaloServiceTypes.Actions[1]['out'];
  'zalo.removeAccount': Services.ZaloServiceTypes.Actions[2]['out'];
  'zalo.sendTextMessage': Services.ZaloServiceTypes.Actions[3]['out'];
  'zalo.sendGroupMessage': Services.ZaloServiceTypes.Actions[4]['out'];
  'zalo.logout': Services.ZaloServiceTypes.Actions[5]['out'];
  'zalo.logoutAll': Services.ZaloServiceTypes.Actions[6]['out'];
};

export type GetEmitParams = {
  'botChat.chat': Services.BotChatServiceTypes.Events[0]['in'];
  'botSetting.createdBotSetting': Services.BotSettingServiceTypes.Events[0]['in'];
  'botSetting.updatedBotSetting': Services.BotSettingServiceTypes.Events[1]['in'];
  'botSetting.removedBotSetting': Services.BotSettingServiceTypes.Events[2]['in'];
  'chatHistory.created': Services.ChatHistoryServiceTypes.Events[0]['in'];
  'chatHistory.updated': Services.ChatHistoryServiceTypes.Events[1]['in'];
  'chatHistory.removed': Services.ChatHistoryServiceTypes.Events[2]['in'];
  'config.changed': Services.ConfigServiceTypes.Events[0]['in'];
  'coreAi.chat': Services.CoreAiServiceTypes.Events[0]['in'];
  'customerContact.created': Services.CustomerContactServiceTypes.Events[0]['in'];
  'customerContact.updated': Services.CustomerContactServiceTypes.Events[1]['in'];
  'customerContact.removed': Services.CustomerContactServiceTypes.Events[2]['in'];
  'dataset.createDataset': Services.DatasetServiceTypes.Events[0]['in'];
  'dataset.updateDataset': Services.DatasetServiceTypes.Events[1]['in'];
  'dataset.removeDataset': Services.DatasetServiceTypes.Events[2]['in'];
  'package.created': Services.PackageServiceTypes.Events[0]['in'];
  'package.updated': Services.PackageServiceTypes.Events[1]['in'];
  'package.deleted': Services.PackageServiceTypes.Events[2]['in'];
  'teamPackage.assigned': Services.TeamPackageServiceTypes.Events[0]['in'];
  'teamPackage.extended': Services.TeamPackageServiceTypes.Events[1]['in'];
  'teamPackage.usageUpdated': Services.TeamPackageServiceTypes.Events[2]['in'];
  'teamPackage.monthlyCreditReset': Services.TeamPackageServiceTypes.Events[3]['in'];
  'telegram.userAccess': Services.TelegramServiceTypes.Events[0]['in'];
  'user.login': Services.UserServiceTypes.Events[0]['in'];
  'user.logout': Services.UserServiceTypes.Events[1]['in'];
  'user.created': Services.UserServiceTypes.Events[2]['in'];
  'user.updated': Services.UserServiceTypes.Events[3]['in'];
  'user.removed': Services.UserServiceTypes.Events[4]['in'];
  'workGate.createWorkGate': Services.WorkGateServiceTypes.Events[0]['in'];
  'workGate.updateWorkGate': Services.WorkGateServiceTypes.Events[1]['in'];
  'workGate.removeWorkGate': Services.WorkGateServiceTypes.Events[2]['in'];
  'zalo.auth.success': Services.ZaloServiceTypes.Events[0]['in'];
  'zalo.auth.failed': Services.ZaloServiceTypes.Events[1]['in'];
  'zalo.message.sent': Services.ZaloServiceTypes.Events[2]['in'];
};

export type ServiceNames = Exclude<
  | never
  | 'api'
  | 'botChat'
  | 'botSetting'
  | 'chatHistory'
  | 'config'
  | 'coreAi'
  | 'customerContact'
  | 'dataset'
  | 'devTest'
  | 'model768'
  | 'package'
  | 'socketConnector'
  | 'teamPackage'
  | 'telegram'
  | 'tokenUsage'
  | 'tokenUsageDaily'
  | 'user'
  | 'vectorStore'
  | 'workGate'
  | 'zalo',
  never
>;
export type ServiceEventNames = Exclude<
  | never
  | 'botChat.chat'
  | 'botSetting.createdBotSetting'
  | 'botSetting.updatedBotSetting'
  | 'botSetting.removedBotSetting'
  | 'chatHistory.created'
  | 'chatHistory.updated'
  | 'chatHistory.removed'
  | 'config.changed'
  | 'coreAi.chat'
  | 'customerContact.created'
  | 'customerContact.updated'
  | 'customerContact.removed'
  | 'dataset.createDataset'
  | 'dataset.updateDataset'
  | 'dataset.removeDataset'
  | 'package.created'
  | 'package.updated'
  | 'package.deleted'
  | 'teamPackage.assigned'
  | 'teamPackage.extended'
  | 'teamPackage.usageUpdated'
  | 'teamPackage.monthlyCreditReset'
  | 'telegram.userAccess'
  | 'user.login'
  | 'user.logout'
  | 'user.created'
  | 'user.updated'
  | 'user.removed'
  | 'workGate.createWorkGate'
  | 'workGate.updateWorkGate'
  | 'workGate.removeWorkGate'
  | 'zalo.auth.success'
  | 'zalo.auth.failed'
  | 'zalo.message.sent',
  never
>;
export type ServiceActionNames = Exclude<
  | never
  | 'botChat.actionChat'
  | 'botChat.actionDirectChat'
  | 'botChat.actionStartChat'
  | 'botChat.actionMoreHistory'
  | 'botChat.actionHook'
  | 'botSetting.actionList'
  | 'botSetting.actionCreate'
  | 'botSetting.actionUpdate'
  | 'botSetting.actionGet'
  | 'botSetting.actionLocalGet'
  | 'botSetting.actionDelete'
  | 'botSetting.getAllName'
  | 'botSetting.createSnapshot'
  | 'botSetting.listSnapshots'
  | 'botSetting.deleteSnapshot'
  | 'botSetting.restoreFromSnapshot'
  | 'botSetting.publish'
  | 'botSetting.updateLogo'
  | 'botSetting.getLiveConfig'
  | 'botSetting.convertToTemplate'
  | 'botSetting.cloneFromTemplate'
  | 'botSetting.getListTemplate'
  | 'botSetting.find'
  | 'botSetting.count'
  | 'botSetting.insert'
  | 'botSetting.get'
  | 'botSetting.update'
  | 'botSetting.remove'
  | 'botSetting.list'
  | 'botSetting.create'
  | 'chatHistory.actionList'
  | 'chatHistory.actionCreate'
  | 'chatHistory.actionUpdate'
  | 'chatHistory.actionGet'
  | 'chatHistory.actionDelete'
  | 'chatHistory.getPaginatedHistory'
  | 'chatHistory.actionGetSessionThread'
  | 'chatHistory.changeChatMode'
  | 'chatHistory.localChangeChatMode'
  | 'chatHistory.actionSaveSessionHistoryRecord'
  | 'chatHistory.actionGetThreadIdByBotUUID'
  | 'chatHistory.actionGetBotChatHistoryByThreadId'
  | 'chatHistory.find'
  | 'chatHistory.count'
  | 'chatHistory.insert'
  | 'chatHistory.get'
  | 'chatHistory.update'
  | 'chatHistory.remove'
  | 'chatHistory.list'
  | 'chatHistory.create'
  | 'config.actionGet'
  | 'config.actionSet'
  | 'config.actionAll'
  | 'config.actionMigrate'
  | 'coreAi.actionProcessFlow'
  | 'coreAi.actionGetTopic'
  | 'coreAi.actionGetOpenAiEmbedding'
  | 'coreAi.actionGetOpenAiChat'
  | 'coreAi.respondWithOllama'
  | 'customerContact.actionCreate'
  | 'customerContact.actionUpdate'
  | 'customerContact.actionGet'
  | 'customerContact.actionDelete'
  | 'customerContact.actionQuickSearch'
  | 'customerContact.actionLinkChatHistory'
  | 'customerContact.actionUnlinkChatHistory'
  | 'customerContact.actionGetByThreadId'
  | 'customerContact.actionQuickCreateFromChatHistory'
  | 'customerContact.actionList'
  | 'customerContact.find'
  | 'customerContact.count'
  | 'customerContact.insert'
  | 'customerContact.get'
  | 'customerContact.update'
  | 'customerContact.remove'
  | 'customerContact.list'
  | 'customerContact.create'
  | 'dataset.actionList'
  | 'dataset.actionCreate'
  | 'dataset.actionUpdate'
  | 'dataset.actionGet'
  | 'dataset.actionDelete'
  | 'dataset.cleanKnowledge'
  | 'dataset.getKnowledge'
  | 'dataset.retrieveKnowledge'
  | 'dataset.actionSearchKnowledge'
  | 'dataset.deleteKnowledge'
  | 'dataset.createKnowledge'
  | 'dataset.actionImportKnowledgeUrl'
  | 'dataset.updateKnowledge'
  | 'dataset.getAllTeamDatasetName'
  | 'dataset.find'
  | 'dataset.count'
  | 'dataset.insert'
  | 'dataset.get'
  | 'dataset.update'
  | 'dataset.remove'
  | 'dataset.list'
  | 'dataset.create'
  | 'devTest.actionStressTestRecord'
  | 'devTest.actionStressTestSearch'
  | 'devTest.actionCurrentTest'
  | 'devTest.actionVMTest'
  | 'devTest.whatMyIP'
  | 'devTest.actionCleanCache'
  | 'model768.getEmbedding'
  | 'package.actionCreate'
  | 'package.actionUpdate'
  | 'package.actionDelete'
  | 'package.actionGet'
  | 'package.actionList'
  | 'package.actionGetAllName'
  | 'package.find'
  | 'package.count'
  | 'package.insert'
  | 'package.get'
  | 'package.update'
  | 'package.remove'
  | 'package.list'
  | 'package.create'
  | 'socketConnector.connect'
  | 'socketConnector.sendClientTask'
  | 'socketConnector.getActiveConnections'
  | 'socketConnector.sendFacebookMessage'
  | 'socketConnector.sendFacebookFeedReply'
  | 'teamPackage.actionAssign'
  | 'teamPackage.actionExtend'
  | 'teamPackage.actionUpdateUsage'
  | 'teamPackage.actionGetCurrent'
  | 'teamPackage.actionList'
  | 'teamPackage.actionCheckCreditLimits'
  | 'teamPackage.actionAddCreditUsage'
  | 'teamPackage.actionUpdateTeamPackagesByPackageId'
  | 'teamPackage.find'
  | 'teamPackage.count'
  | 'teamPackage.insert'
  | 'teamPackage.get'
  | 'teamPackage.update'
  | 'teamPackage.remove'
  | 'teamPackage.list'
  | 'teamPackage.create'
  | 'telegram.actionMessageAll'
  | 'telegram.actionMessageAdmin'
  | 'telegram.actionMessageUser'
  | 'telegram.actionSystemMessage'
  | 'tokenUsage.getMe'
  | 'tokenUsage.getByTeam'
  | 'tokenUsage.addTokenUsage'
  | 'tokenUsage.find'
  | 'tokenUsage.count'
  | 'tokenUsage.insert'
  | 'tokenUsage.get'
  | 'tokenUsage.update'
  | 'tokenUsage.remove'
  | 'tokenUsage.list'
  | 'tokenUsage.create'
  | 'tokenUsageDaily.addTokenUsage'
  | 'tokenUsageDaily.reportUsage'
  | 'tokenUsageDaily.find'
  | 'tokenUsageDaily.count'
  | 'tokenUsageDaily.insert'
  | 'tokenUsageDaily.get'
  | 'tokenUsageDaily.update'
  | 'tokenUsageDaily.remove'
  | 'tokenUsageDaily.list'
  | 'tokenUsageDaily.create'
  | 'user.actionRegister'
  | 'user.actionLogin'
  | 'user.actionFirebaseLogin'
  | 'user.actionLogout'
  | 'user.updateUserStatus'
  | 'user.actionMe'
  | 'user.actionMeUpdate'
  | 'user.actionVerifyToken'
  | 'user.actionList'
  | 'user.find'
  | 'user.count'
  | 'user.insert'
  | 'user.get'
  | 'user.update'
  | 'user.remove'
  | 'user.list'
  | 'user.create'
  | 'vectorStore.processCsv'
  | 'vectorStore.createCollection'
  | 'vectorStore.cleanCollection'
  | 'vectorStore.getData'
  | 'vectorStore.countCollection'
  | 'vectorStore.delete'
  | 'vectorStore.search'
  | 'vectorStore.create'
  | 'vectorStore.update'
  | 'workGate.actionList'
  | 'workGate.actionCount'
  | 'workGate.actionCreate'
  | 'workGate.actionUpdateGateConfig'
  | 'workGate.actionLocalGet'
  | 'workGate.actionUpdate'
  | 'workGate.actionGet'
  | 'workGate.actionDelete'
  | 'workGate.getConfig'
  | 'workGate.getListFacebookPosts'
  | 'workGate.actionImportFacebookPages'
  | 'workGate.actionPostWebhook'
  | 'workGate.actionGetWebhook'
  | 'workGate.getAllZaloQr'
  | 'workGate.find'
  | 'workGate.count'
  | 'workGate.insert'
  | 'workGate.get'
  | 'workGate.update'
  | 'workGate.remove'
  | 'workGate.list'
  | 'workGate.create'
  | 'zalo.getAuthStatus'
  | 'zalo.loginQR'
  | 'zalo.removeAccount'
  | 'zalo.sendTextMessage'
  | 'zalo.sendGroupMessage'
  | 'zalo.logout'
  | 'zalo.logoutAll',
  never
>;
export type ServiceNamesEmitGroup = ServiceNames | ServiceNames[];
