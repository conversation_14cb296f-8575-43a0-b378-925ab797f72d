import * as MoleculerTs from 'moleculer-ts';
import * as Moleculer from './moleculer';
import * as Services from './services.types';

export interface ServiceBroker {
  call<T extends ServiceActionNames>(
    actionName: T,
    params: GetCallParams[T],
    opts?: Moleculer.CallingOptions,
  ): Promise<GetCallReturn[T]>;

  emit<T extends ServiceEventNames>(eventName: T, payload: GetEmitParams[T], groups?: ServiceNamesEmitGroup): void;

  broadcast: ServiceBroker['emit'];
  broadcastLocal: ServiceBroker['emit'];
}

export type GetCallParams = {
  'coreAi.actionProcessFlow': Services.CoreAiServiceTypes.Actions[0]['in'];
  'socketConnector.connect': Services.SocketConnectorServiceTypes.Actions[0]['in'];
  'socketConnector.sendClientTask': Services.SocketConnectorServiceTypes.Actions[1]['in'];
  'socketConnector.getActiveConnections': Services.SocketConnectorServiceTypes.Actions[2]['in'];
  'socketConnector.sendFacebookMessage': Services.SocketConnectorServiceTypes.Actions[3]['in'];
  'socketConnector.sendFacebookFeedReply': Services.SocketConnectorServiceTypes.Actions[4]['in'];
  'teamPackage.actionAssign': Services.TeamPackageServiceTypes.Actions[0]['in'];
  'teamPackage.actionExtend': Services.TeamPackageServiceTypes.Actions[1]['in'];
  'teamPackage.actionUpdateUsage': Services.TeamPackageServiceTypes.Actions[2]['in'];
  'teamPackage.actionGetCurrent': Services.TeamPackageServiceTypes.Actions[3]['in'];
  'teamPackage.actionList': Services.TeamPackageServiceTypes.Actions[4]['in'];
  'teamPackage.actionCheckCreditLimits': Services.TeamPackageServiceTypes.Actions[5]['in'];
  'teamPackage.actionAddCreditUsage': Services.TeamPackageServiceTypes.Actions[6]['in'];
  'teamPackage.actionUpdateTeamPackagesByPackageId': Services.TeamPackageServiceTypes.Actions[7]['in'];
  'teamPackage.find': Services.TeamPackageServiceTypes.Actions[8]['in'];
  'teamPackage.count': Services.TeamPackageServiceTypes.Actions[9]['in'];
  'teamPackage.insert': Services.TeamPackageServiceTypes.Actions[10]['in'];
  'teamPackage.get': Services.TeamPackageServiceTypes.Actions[11]['in'];
  'teamPackage.update': Services.TeamPackageServiceTypes.Actions[12]['in'];
  'teamPackage.remove': Services.TeamPackageServiceTypes.Actions[13]['in'];
  'teamPackage.list': Services.TeamPackageServiceTypes.Actions[14]['in'];
  'teamPackage.create': Services.TeamPackageServiceTypes.Actions[15]['in'];
  'workGate.actionList': Services.WorkGateServiceTypes.Actions[0]['in'];
  'workGate.actionCount': Services.WorkGateServiceTypes.Actions[1]['in'];
  'workGate.actionCreate': Services.WorkGateServiceTypes.Actions[2]['in'];
  'workGate.actionUpdateGateConfig': Services.WorkGateServiceTypes.Actions[3]['in'];
  'workGate.actionLocalGet': Services.WorkGateServiceTypes.Actions[4]['in'];
  'workGate.actionUpdate': Services.WorkGateServiceTypes.Actions[5]['in'];
  'workGate.actionGet': Services.WorkGateServiceTypes.Actions[6]['in'];
  'workGate.actionDelete': Services.WorkGateServiceTypes.Actions[7]['in'];
  'workGate.getConfig': Services.WorkGateServiceTypes.Actions[8]['in'];
  'workGate.getListFacebookPosts': Services.WorkGateServiceTypes.Actions[9]['in'];
  'workGate.actionImportFacebookPages': Services.WorkGateServiceTypes.Actions[10]['in'];
  'workGate.actionPostWebhook': Services.WorkGateServiceTypes.Actions[11]['in'];
  'workGate.actionGetWebhook': Services.WorkGateServiceTypes.Actions[12]['in'];
  'workGate.getAllZaloQr': Services.WorkGateServiceTypes.Actions[13]['in'];
  'workGate.find': Services.WorkGateServiceTypes.Actions[14]['in'];
  'workGate.count': Services.WorkGateServiceTypes.Actions[15]['in'];
  'workGate.insert': Services.WorkGateServiceTypes.Actions[16]['in'];
  'workGate.get': Services.WorkGateServiceTypes.Actions[17]['in'];
  'workGate.update': Services.WorkGateServiceTypes.Actions[18]['in'];
  'workGate.remove': Services.WorkGateServiceTypes.Actions[19]['in'];
  'workGate.list': Services.WorkGateServiceTypes.Actions[20]['in'];
  'workGate.create': Services.WorkGateServiceTypes.Actions[21]['in'];
  'zalo.getAuthStatus': Services.ZaloServiceTypes.Actions[0]['in'];
  'zalo.loginQR': Services.ZaloServiceTypes.Actions[1]['in'];
  'zalo.removeAccount': Services.ZaloServiceTypes.Actions[2]['in'];
  'zalo.sendTextMessage': Services.ZaloServiceTypes.Actions[3]['in'];
  'zalo.sendGroupMessage': Services.ZaloServiceTypes.Actions[4]['in'];
  'zalo.apiTextMessage': Services.ZaloServiceTypes.Actions[5]['in'];
  'zalo.apiGroupMessage': Services.ZaloServiceTypes.Actions[6]['in'];
  'zalo.logout': Services.ZaloServiceTypes.Actions[7]['in'];
  'zalo.logoutAll': Services.ZaloServiceTypes.Actions[8]['in'];
};

export type GetCallReturn = {
  'coreAi.actionProcessFlow': Services.CoreAiServiceTypes.Actions[0]['out'];
  'socketConnector.connect': Services.SocketConnectorServiceTypes.Actions[0]['out'];
  'socketConnector.sendClientTask': Services.SocketConnectorServiceTypes.Actions[1]['out'];
  'socketConnector.getActiveConnections': Services.SocketConnectorServiceTypes.Actions[2]['out'];
  'socketConnector.sendFacebookMessage': Services.SocketConnectorServiceTypes.Actions[3]['out'];
  'socketConnector.sendFacebookFeedReply': Services.SocketConnectorServiceTypes.Actions[4]['out'];
  'teamPackage.actionAssign': Services.TeamPackageServiceTypes.Actions[0]['out'];
  'teamPackage.actionExtend': Services.TeamPackageServiceTypes.Actions[1]['out'];
  'teamPackage.actionUpdateUsage': Services.TeamPackageServiceTypes.Actions[2]['out'];
  'teamPackage.actionGetCurrent': Services.TeamPackageServiceTypes.Actions[3]['out'];
  'teamPackage.actionList': Services.TeamPackageServiceTypes.Actions[4]['out'];
  'teamPackage.actionCheckCreditLimits': Services.TeamPackageServiceTypes.Actions[5]['out'];
  'teamPackage.actionAddCreditUsage': Services.TeamPackageServiceTypes.Actions[6]['out'];
  'teamPackage.actionUpdateTeamPackagesByPackageId': Services.TeamPackageServiceTypes.Actions[7]['out'];
  'teamPackage.find': Services.TeamPackageServiceTypes.Actions[8]['out'];
  'teamPackage.count': Services.TeamPackageServiceTypes.Actions[9]['out'];
  'teamPackage.insert': Services.TeamPackageServiceTypes.Actions[10]['out'];
  'teamPackage.get': Services.TeamPackageServiceTypes.Actions[11]['out'];
  'teamPackage.update': Services.TeamPackageServiceTypes.Actions[12]['out'];
  'teamPackage.remove': Services.TeamPackageServiceTypes.Actions[13]['out'];
  'teamPackage.list': Services.TeamPackageServiceTypes.Actions[14]['out'];
  'teamPackage.create': Services.TeamPackageServiceTypes.Actions[15]['out'];
  'workGate.actionList': Services.WorkGateServiceTypes.Actions[0]['out'];
  'workGate.actionCount': Services.WorkGateServiceTypes.Actions[1]['out'];
  'workGate.actionCreate': Services.WorkGateServiceTypes.Actions[2]['out'];
  'workGate.actionUpdateGateConfig': Services.WorkGateServiceTypes.Actions[3]['out'];
  'workGate.actionLocalGet': Services.WorkGateServiceTypes.Actions[4]['out'];
  'workGate.actionUpdate': Services.WorkGateServiceTypes.Actions[5]['out'];
  'workGate.actionGet': Services.WorkGateServiceTypes.Actions[6]['out'];
  'workGate.actionDelete': Services.WorkGateServiceTypes.Actions[7]['out'];
  'workGate.getConfig': Services.WorkGateServiceTypes.Actions[8]['out'];
  'workGate.getListFacebookPosts': Services.WorkGateServiceTypes.Actions[9]['out'];
  'workGate.actionImportFacebookPages': Services.WorkGateServiceTypes.Actions[10]['out'];
  'workGate.actionPostWebhook': Services.WorkGateServiceTypes.Actions[11]['out'];
  'workGate.actionGetWebhook': Services.WorkGateServiceTypes.Actions[12]['out'];
  'workGate.getAllZaloQr': Services.WorkGateServiceTypes.Actions[13]['out'];
  'workGate.find': Services.WorkGateServiceTypes.Actions[14]['out'];
  'workGate.count': Services.WorkGateServiceTypes.Actions[15]['out'];
  'workGate.insert': Services.WorkGateServiceTypes.Actions[16]['out'];
  'workGate.get': Services.WorkGateServiceTypes.Actions[17]['out'];
  'workGate.update': Services.WorkGateServiceTypes.Actions[18]['out'];
  'workGate.remove': Services.WorkGateServiceTypes.Actions[19]['out'];
  'workGate.list': Services.WorkGateServiceTypes.Actions[20]['out'];
  'workGate.create': Services.WorkGateServiceTypes.Actions[21]['out'];
  'zalo.getAuthStatus': Services.ZaloServiceTypes.Actions[0]['out'];
  'zalo.loginQR': Services.ZaloServiceTypes.Actions[1]['out'];
  'zalo.removeAccount': Services.ZaloServiceTypes.Actions[2]['out'];
  'zalo.sendTextMessage': Services.ZaloServiceTypes.Actions[3]['out'];
  'zalo.sendGroupMessage': Services.ZaloServiceTypes.Actions[4]['out'];
  'zalo.apiTextMessage': Services.ZaloServiceTypes.Actions[5]['out'];
  'zalo.apiGroupMessage': Services.ZaloServiceTypes.Actions[6]['out'];
  'zalo.logout': Services.ZaloServiceTypes.Actions[7]['out'];
  'zalo.logoutAll': Services.ZaloServiceTypes.Actions[8]['out'];
};

export type GetEmitParams = {
  'coreAi.chat': Services.CoreAiServiceTypes.Events[0]['in'];
  'teamPackage.assigned': Services.TeamPackageServiceTypes.Events[0]['in'];
  'teamPackage.extended': Services.TeamPackageServiceTypes.Events[1]['in'];
  'teamPackage.usageUpdated': Services.TeamPackageServiceTypes.Events[2]['in'];
  'teamPackage.monthlyCreditReset': Services.TeamPackageServiceTypes.Events[3]['in'];
  'workGate.createWorkGate': Services.WorkGateServiceTypes.Events[0]['in'];
  'workGate.updateWorkGate': Services.WorkGateServiceTypes.Events[1]['in'];
  'workGate.removeWorkGate': Services.WorkGateServiceTypes.Events[2]['in'];
  'zalo.auth.success': Services.ZaloServiceTypes.Events[0]['in'];
  'zalo.auth.failed': Services.ZaloServiceTypes.Events[1]['in'];
  'zalo.message.sent': Services.ZaloServiceTypes.Events[2]['in'];
};

export type ServiceNames = Exclude<never | 'coreAi' | 'socketConnector' | 'teamPackage' | 'workGate' | 'zalo', never>;
export type ServiceEventNames = Exclude<
  | never
  | 'coreAi.chat'
  | 'teamPackage.assigned'
  | 'teamPackage.extended'
  | 'teamPackage.usageUpdated'
  | 'teamPackage.monthlyCreditReset'
  | 'workGate.createWorkGate'
  | 'workGate.updateWorkGate'
  | 'workGate.removeWorkGate'
  | 'zalo.auth.success'
  | 'zalo.auth.failed'
  | 'zalo.message.sent',
  never
>;
export type ServiceActionNames = Exclude<
  | never
  | 'coreAi.actionProcessFlow'
  | 'socketConnector.connect'
  | 'socketConnector.sendClientTask'
  | 'socketConnector.getActiveConnections'
  | 'socketConnector.sendFacebookMessage'
  | 'socketConnector.sendFacebookFeedReply'
  | 'teamPackage.actionAssign'
  | 'teamPackage.actionExtend'
  | 'teamPackage.actionUpdateUsage'
  | 'teamPackage.actionGetCurrent'
  | 'teamPackage.actionList'
  | 'teamPackage.actionCheckCreditLimits'
  | 'teamPackage.actionAddCreditUsage'
  | 'teamPackage.actionUpdateTeamPackagesByPackageId'
  | 'teamPackage.find'
  | 'teamPackage.count'
  | 'teamPackage.insert'
  | 'teamPackage.get'
  | 'teamPackage.update'
  | 'teamPackage.remove'
  | 'teamPackage.list'
  | 'teamPackage.create'
  | 'workGate.actionList'
  | 'workGate.actionCount'
  | 'workGate.actionCreate'
  | 'workGate.actionUpdateGateConfig'
  | 'workGate.actionLocalGet'
  | 'workGate.actionUpdate'
  | 'workGate.actionGet'
  | 'workGate.actionDelete'
  | 'workGate.getConfig'
  | 'workGate.getListFacebookPosts'
  | 'workGate.actionImportFacebookPages'
  | 'workGate.actionPostWebhook'
  | 'workGate.actionGetWebhook'
  | 'workGate.getAllZaloQr'
  | 'workGate.find'
  | 'workGate.count'
  | 'workGate.insert'
  | 'workGate.get'
  | 'workGate.update'
  | 'workGate.remove'
  | 'workGate.list'
  | 'workGate.create'
  | 'zalo.getAuthStatus'
  | 'zalo.loginQR'
  | 'zalo.removeAccount'
  | 'zalo.sendTextMessage'
  | 'zalo.sendGroupMessage'
  | 'zalo.apiTextMessage'
  | 'zalo.apiGroupMessage'
  | 'zalo.logout'
  | 'zalo.logoutAll',
  never
>;
export type ServiceNamesEmitGroup = ServiceNames | ServiceNames[];
