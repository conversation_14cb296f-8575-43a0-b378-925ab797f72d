'use strict';

import { Action, Event, Method, Service as DService } from 'moleculer-decorators';
import { LoginQRCallbackEventType } from 'zca-js';
import { Context, Service } from '../../types/moleculer';
import { ApiGatewayMeta, ZaloServiceTypes } from '../../types';
import { name } from './zalo.service.types';
import { PassThrough } from 'stream';
import { ReadStream } from 'fs';
import { FileStreamMeta } from '../../utils/FileUploadUtils';
import { CommonErrors } from '../../commons/error.helper';
import { RequireRoles } from '../../commons/annotation.helper';
import { IWorkGate, WORKGATE_STATUSES } from '../../entities/workGate.entity';
import { USER_ROLE } from '../../entities/user.entity';
import { ZaloAccount, ZaloInstanceManager } from './zalo.instance.manager';
import { ZaloBusinessLogic } from './zalo.business.logic';

@DService({
  name,
})
class ZaloService extends Service<{}> {
  // Instance manager and business logic
  private instanceManager: ZaloInstanceManager;
  private businessLogic: ZaloBusinessLogic;

  dependencies: ['workGate'];

  /**
   * Service started lifecycle event handler
   */
  started() {
    this.logger.info('Zalo service started');

    // Initialize instance manager and business logic
    this.instanceManager = new ZaloInstanceManager(this.logger);
    this.businessLogic = new ZaloBusinessLogic(this.logger, this.broker, this.instanceManager);

    // Initialize the service
    this.businessLogic.initZaloService();
  }

  /**
   * Service stopped lifecycle event handler
   */
  stopped() {
    this.logger.info('Zalo service stopped');
  }

  /**
   * Get authentication status
   */
  @RequireRoles([USER_ROLE.ADMIN])
  @Action({
    rest: 'GET /auth/status',
  })
  async getAuthStatus(ctx: Context<any, ApiGatewayMeta>): Promise<any> {
    const accountList = this.instanceManager.getAccountList();
    const authStatus = {
      accounts: accountList,
      totalAccounts: this.instanceManager.getAccountsCount(),
      authenticatedAccounts: this.instanceManager.getAuthenticatedAccountsCount(),
    };

    return {
      success: true,
      data: authStatus,
    };
  }

  /**
   * Login with QR code via SSE
   */
  @RequireRoles()
  @Action({
    rest: 'GET /auth/loginQr',
    openapi: {
      summary: 'Login with QR code via SSE',
      description:
        'Login with QR code via SSE. Please make sure endpoint using EventSource. Access verification over token param',
    },
    params: {
      token: { type: 'string', required: true },
    },
  })
  async loginQR(ctx: Context<any, ApiGatewayMeta>): Promise<any> {
    // Set up SSE response first
    ctx.meta.$responseType = 'text/event-stream';
    ctx.meta.$responseHeaders = {
      'Cache-Control': 'no-cache',
      'Content-Type': 'text/event-stream',
      Connection: 'keep-alive',
    };

    const stream = new PassThrough();

    // Helper function to send SSE events
    const sendEvent = this.createSSEEventSender(stream);

    try {
      console.log('ctx.meta.user', ctx.meta.user);
      // Get current team package
      const teamPackage = await this.broker.call('teamPackage.actionGetCurrent', {}, { parentCtx: ctx });

      if (teamPackage.connectorLimit !== undefined && teamPackage.connectorLimit > 0) {
        // Count current active workgates for the team
        const { count: currentWorkGateCount } = await this.broker.call(
          'workGate.actionCount',
          {
            query: {
              team_id: ctx.meta.user.teamId,
              status: WORKGATE_STATUSES.ACTIVE,
              type: 'zalo_qr', // Only count Zalo QR connectors
            },
          },
          { parentCtx: ctx },
        );

        if (currentWorkGateCount >= teamPackage.connectorLimit) {
          const errorResponse = {
            success: false,
            message: `Connector limit exceeded. Your package allows maximum ${teamPackage.connectorLimit} connectors, but you already have ${currentWorkGateCount} active connectors.`,
            reason: 'connector_limit_exceeded',
            maxLimit: teamPackage.connectorLimit,
            currentConnections: currentWorkGateCount,
          };
          sendEvent('error', errorResponse);
          stream.end();
          return stream;
        }
      }
    } catch (error) {
      this.logger.error('Error checking connector limits:', error as Error);
      // Continue with login process if limit check fails (fallback behavior)
      this.logger.warn('Proceeding with login despite limit check failure');
    }

    // Send initial connection confirmation with updated connector info
    try {
      const teamPackage = await this.broker.call('teamPackage.actionGetCurrent', {}, { parentCtx: ctx });
      const { count: currentWorkGateCount } = await this.broker.call(
        'workGate.actionCount',
        {
          query: {
            team_id: ctx.meta.user.teamId,
            status: { $ne: WORKGATE_STATUSES.DISABLED },
            type: 'zalo_qr',
          },
        },
        { parentCtx: ctx },
      );

      sendEvent('connected', {
        message: 'Connected to Zalo login stream',
        connectorInfo: {
          currentConnections: currentWorkGateCount,
          maxLimit: teamPackage.connectorLimit || 'unlimited',
          available: teamPackage.connectorLimit
            ? Math.max(0, teamPackage.connectorLimit - currentWorkGateCount)
            : 'unlimited',
        },
      });
    } catch (error) {
      // Fallback if we can't get connector info
      sendEvent('connected', { message: 'Connected to Zalo login stream' });
    }

    // Create new account for login (will get real ID after successful login)
    const zaloInstance = this.instanceManager.getZaloInstance();
    const account = this.instanceManager.createTempAccount();

    // Track SSE connection state
    let isSSEClosed = false;
    let loginProcess: any = null;
    let accountAddedToMap = false;
    // Add retry counter for QR code regeneration
    let retryCount = 0;
    const maxRetries = 1;

    // Monitor stream close
    stream.on('close', () => {
      isSSEClosed = true;
      this.logger.info(`SSE stream closed during login process, stopping login process`);

      // Clean up login process
      if (loginProcess && typeof loginProcess.abort === 'function') {
        try {
          loginProcess.abort();
          this.logger.info(`Login process aborted`);
        } catch (error) {
          this.logger.warn(`Error aborting login process:`, error);
        }
      }

      // Reset account state if not authenticated
      if (account && !account.isAuthenticated) {
        this.logger.info(`Cleaning up unauthenticated account after SSE close`);
        try {
          this.instanceManager.cleanupAccount(account, accountAddedToMap, 'SSE connection closed');
        } catch (error) {
          this.logger.error(`Error cleaning up account:`, error as Error);
        }
      }
    });

    // Start the login process asynchronously
    (async () => {
      try {
        loginProcess = zaloInstance.loginQR({}, (event: any) => {
          // Check if SSE response is alive
          const isSseAlive = stream && !isSSEClosed && !stream.writableEnded && !stream.destroyed;

          if (!isSseAlive) {
            this.logger.info(`Ignoring login event for closed SSE connection`);
            return;
          }

          switch (event.type) {
            case LoginQRCallbackEventType.QRCodeGenerated:
              const qrEventData = {
                data: event.data.image,
                retryCount,
                retriesRemaining: maxRetries - retryCount,
              };
              sendEvent('qr', qrEventData);
              this.logger.info(
                `QR code generated and sent to SSE client (attempt ${retryCount + 1}/${maxRetries + 1})`,
              );
              break;

            case LoginQRCallbackEventType.QRCodeScanned:
              this.logger.info('QR code scanned by:', event.data.display_name);
              const scannedEventData = {
                message: `QR code scanned by: ${event.data.display_name}`,
                displayName: event.data.display_name,
              };
              sendEvent('qr_scanned', scannedEventData);
              break;

            case LoginQRCallbackEventType.QRCodeDeclined:
              this.logger.warn('QR code login declined');
              const declinedMessage = 'QR code login was declined. Please try again.';
              sendEvent('error', { message: declinedMessage });
              break;

            case LoginQRCallbackEventType.GotLoginInfo:
              this.logger.info('Successfully received login info');

              try {
                account.sessionData = {
                  cookie: event.data.cookie,
                  imei: event.data.imei,
                  userAgent: event.data.userAgent,
                };
                account.savedAt = Date.now();
                this.logger.info('Login information saved temporarily');
              } catch (error) {
                this.logger.error('Error saving login information', error as Error);
              }
              break;

            case LoginQRCallbackEventType.QRCodeExpired:
              // Check if SSE response is alive before handling expiration
              const isSseAliveOnExpired = stream && !isSSEClosed && !stream.writableEnded && !stream.destroyed;

              if (!isSseAliveOnExpired) {
                this.logger.info(`SSE connection closed during QR code expiration, ending stream`);
                stream.end();
                return;
              }

              this.logger.warn(`QR code expired (retry ${retryCount + 1}/${maxRetries + 1})`);

              if (retryCount >= maxRetries) {
                this.logger.warn(`Maximum retry limit (${maxRetries}) reached, closing stream`);
                const maxRetriesMessage = `QR code expired. Maximum retry limit (${maxRetries}) reached. Please try again later.`;
                sendEvent('error', {
                  message: maxRetriesMessage,
                  reason: 'max_retries_exceeded',
                  maxRetries,
                  totalAttempts: retryCount + 1,
                });
                stream.end();
                return;
              }

              retryCount++;
              const expiredMessage = `QR code expired. Generating a new one... (${retryCount}/${maxRetries} retries used)`;
              sendEvent('qr_expired', {
                message: expiredMessage,
                retryCount,
                retriesRemaining: maxRetries - retryCount,
              });

              try {
                event.actions.retry();
                this.logger.info(`Retrying QR generation (attempt ${retryCount + 1}/${maxRetries + 1})`);
              } catch (error) {
                this.logger.error('Failed to retry QR generation:', error as Error);
                sendEvent('error', {
                  message: `Failed to retry QR generation: ${(error as Error).message}`,
                });
                stream.end();
              }
              break;
          }
        });

        account.zaloApi = await loginProcess;

        // Give some time for the API to stabilize
        await new Promise((resolve) => setTimeout(resolve, 5000));

        // Check if SSE response is alive
        const isSseAlive = stream && !isSSEClosed && !stream.writableEnded && !stream.destroyed;

        if (account.zaloApi && isSseAlive) {
          try {
            // Handle successful login - this will set the real account ID and user info
            await this.businessLogic.handleSuccessfulLogin(account);

            // NOW add the account to the accounts map with real user ID
            this.instanceManager.addAccount(account);
            accountAddedToMap = true;
            this.logger.info(`Account ${account.id} (${account.displayName}) added to accounts map`);

            // Create workgate entry for this account
            try {
              const newWorkGate = await this.businessLogic.createWorkgateForAccount(account, ctx);

              const successResult = {
                success: true,
                message: `QR code generated and login successful for account ${account.displayName}`,
                account: {
                  id: account.id,
                  name: account.displayName,
                  user: account.user,
                  workGateId: account.workgate?.id,
                },
              };

              // Send success event through SSE
              sendEvent('auth_success', successResult);

              // Send updated auth status
              const authStatus = await this.getAuthStatus(ctx);
              sendEvent('auth_status', authStatus.data);

              // End the stream
              stream.end();
            } catch (workGateError) {
              this.logger.error('Failed to create/update workgate:', workGateError as Error);

              // Clean up the account when workgate creation fails
              this.instanceManager.cleanupAccount(account, true, 'workgate creation failure');
              accountAddedToMap = false;

              const errorResult = this.createErrorResponse(
                `Login successful but failed to create workgate: ${(workGateError as Error).message}`,
                'workgate_creation_failed',
              );

              sendEvent('error', errorResult);
              stream.end();
              return;
            }
          } catch (error: any) {
            this.logger.error('Login verification failed', error);

            const errorResult = {
              success: false,
              message: `Failed to verify login: ${error.message}`,
            };

            sendEvent('error', errorResult);
            stream.end();
          }
        } else {
          const errorResult = {
            success: false,
            message: !isSseAlive
              ? `Login cancelled: Connection closed`
              : `Login failed: API instance not available after QR code scan`,
          };

          if (isSseAlive) {
            sendEvent('error', errorResult);
            stream.end();
          }
        }
      } catch (error: any) {
        this.logger.error(`Login error:`, error);
        account.zaloApi = null;
        account.isAuthenticated = false;
        account.user = null;
        account.phoneToUserIdCache = {};
        account.groupCache = {};
        account.workgate = undefined;

        // Check if SSE response is alive
        const isSseAlive = stream && !isSSEClosed && !stream.writableEnded && !stream.destroyed;

        const errorResult = {
          success: false,
          message: `Login failed: ${error.message}`,
        };

        if (isSseAlive) {
          sendEvent('error', errorResult);
          stream.end();
        }
      }
    })();

    return stream;
  }

  /**
   * Remove a Zalo account
   */
  @RequireRoles([USER_ROLE.ADMIN])
  @Action({
    rest: 'DELETE /auth/accounts/:accountId',
    params: {
      accountId: { type: 'string' },
    },
  })
  async removeAccount(ctx: Context<any, ApiGatewayMeta>): Promise<any> {
    const { accountId } = ctx.params;
    const account = this.instanceManager.getAccount(accountId);

    if (!account) {
      return this.createErrorResponse(`Account ${accountId} not found`);
    }

    try {
      // Use cleanupAccount helper to stop listener and remove from memory
      this.instanceManager.cleanupAccount(account, true, 'manual removal');

      return this.createSuccessResponse(`Account ${account.displayName} removed successfully`);
    } catch (error) {
      this.logger.error(`Error removing account ${accountId}:`, error as Error);
      return this.createErrorResponse(`Error removing account: ${(error as Error).message}`);
    }
  }

  /**
   * Send text message (no file handling)
   */
  @RequireRoles([USER_ROLE.ADMIN])
  @Action({
    params: {
      accountId: { type: 'string', optional: true },
      recipient: { type: 'string' },
      text: { type: 'string' },
      filePath: { type: 'string', optional: true },
    },
  })
  async sendTextMessage(
    ctx: Context<{ accountId?: string; recipient: string; text: string; filePath?: string }, ApiGatewayMeta>,
  ): Promise<ZaloServiceTypes.ActionReturn<'sendTextMessage'>> {
    const { accountId, recipient, text, filePath } = ctx.params;

    try {
      this.logger.info(
        `Starting sendTextMessage - accountId: ${accountId}, recipient: ${recipient}, text: ${text}, filePath: ${filePath}`,
      );

      // Get account (will throw error if not found/authenticated)
      const account = this.businessLogic.getAccountSafely(accountId);

      // Send message (business logic handles everything)
      const result = await this.businessLogic.sendTextMessage(account, recipient, text, filePath);

      this.logger.info(
        `sendTextMessage success - msgId: ${result.msgId}, recipient: ${result.recipient}, account: ${account.displayName}`,
      );
      return result;
    } catch (error) {
      this.logger.error(`sendTextMessage error - accountId: ${accountId}, recipient: ${recipient}:`, error as Error);
      throw error;
    }
  }

  /**
   * Send group message (no file handling)
   */
  @RequireRoles([USER_ROLE.ADMIN])
  @Action({
    params: {
      accountId: { type: 'string', optional: true },
      groupIdentifier: { type: 'string' },
      text: { type: 'string' },
      filePath: { type: 'string', optional: true },
    },
  })
  async sendGroupMessage(
    ctx: Context<{ accountId?: string; groupIdentifier: string; text: string; filePath?: string }, ApiGatewayMeta>,
  ): Promise<ZaloServiceTypes.ActionReturn<'sendGroupMessage'>> {
    const { accountId, groupIdentifier, text, filePath } = ctx.params;

    try {
      this.logger.info(
        `Starting sendGroupMessage - accountId: ${accountId}, groupIdentifier: ${groupIdentifier}, text: ${text}, filePath: ${filePath}`,
      );

      // Get account (will throw error if not found/authenticated)
      const account = this.businessLogic.getAccountSafely(accountId);

      // Send group message (business logic handles everything)
      const result = await this.businessLogic.sendGroupMessage(account, groupIdentifier, text, filePath);

      this.logger.info(
        `sendGroupMessage success - msgId: ${result.msgId}, recipient: ${result.recipient}, groupName: ${result.groupName}, account: ${account.displayName}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `sendGroupMessage error - accountId: ${accountId}, groupIdentifier: ${groupIdentifier}:`,
        error as Error,
      );
      throw error;
    }
  }

  /**
   * Send text message with file attachment (API with file handling)
   */
  @RequireRoles([USER_ROLE.ADMIN])
  @Action({
    rest: 'POST /apiTextMessage',
    openapi: {
      description: 'Send a text message with optional file attachment via Zalo',
      requestBody: {
        content: {
          'multipart/form-data': {
            schema: {
              type: 'object',
              properties: {
                accountId: {
                  type: 'string',
                  description:
                    'ID of the Zalo account to send from (optional - uses first authenticated account if not provided)',
                },
                recipient: {
                  type: 'string',
                  description: 'Phone number or user ID of the recipient',
                },
                text: {
                  type: 'string',
                  description: 'Text message content',
                },
                file: {
                  type: 'string',
                  format: 'binary',
                  description: 'Optional file attachment (images, documents, or audio files)',
                },
              },
              required: ['recipient', 'text'],
            },
          },
        },
        required: true,
      },
    },
  })
  async apiTextMessage(
    ctx: Context<
      ReadStream,
      FileStreamMeta<{ accountId: string; recipient: string; text: string; file: any }> & ApiGatewayMeta
    >,
  ): Promise<ZaloServiceTypes.ActionReturn<'apiTextMessage'>> {
    const { accountId, recipient, text } = ctx.meta.$multipart;
    const fileStream = ctx.params;
    let savedFilePath: string | null = null;

    try {
      // Get account (will throw error if not found/authenticated)
      const account = this.businessLogic.getAccountSafely(accountId);

      // Save file if provided (will throw error if invalid)
      savedFilePath = await this.businessLogic.validateAndSaveFile(
        fileStream,
        ctx.meta.mimetype,
        account.id,
        ctx.meta.filename,
      );

      // Send message with file (business logic handles everything)
      const result = await this.businessLogic.sendTextMessage(account, recipient, text, savedFilePath);

      // Clean up file and return success
      this.businessLogic.cleanupFile(savedFilePath);
      return this.createSuccessResponse(`Message sent successfully from account ${account.displayName}`, result);
    } catch (error) {
      this.logger.error(`Send message error:`, error as Error);
      this.businessLogic.cleanupFile(savedFilePath);

      return this.handleMessageError(error, accountId, ctx);
    }
  }

  /**
   * Send group message with file attachment (API with file handling)
   */
  @RequireRoles([USER_ROLE.ADMIN])
  @Action({
    rest: 'POST /apiGroupMessage',
    openapi: {
      description: 'Send a text message with optional file attachment to a Zalo group',
      requestBody: {
        content: {
          'multipart/form-data': {
            schema: {
              type: 'object',
              properties: {
                accountId: {
                  type: 'string',
                  description:
                    'ID of the Zalo account to send from (optional - uses first authenticated account if not provided)',
                },
                groupIdentifier: {
                  type: 'string',
                  description: 'Group ID or group name to send message to',
                },
                text: {
                  type: 'string',
                  description: 'Text message content',
                },
                file: {
                  type: 'string',
                  format: 'binary',
                  description: 'Optional file attachment (images, documents, or audio files)',
                },
              },
              required: ['groupIdentifier', 'text'],
            },
          },
        },
        required: true,
      },
    },
  })
  async apiGroupMessage(
    ctx: Context<
      ReadStream,
      FileStreamMeta<{ accountId: string; groupIdentifier: string; text: string; file: any }> & ApiGatewayMeta
    >,
  ): Promise<ZaloServiceTypes.ActionReturn<'apiGroupMessage'>> {
    const { accountId, groupIdentifier, text } = ctx.meta.$multipart;
    const fileStream = ctx.params;
    let savedFilePath: string | null = null;

    try {
      // Get account (will throw error if not found/authenticated)
      const account = this.businessLogic.getAccountSafely(accountId);

      // Save file if provided (will throw error if invalid)
      savedFilePath = await this.businessLogic.validateAndSaveFile(
        fileStream,
        ctx.meta.mimetype,
        account.id,
        ctx.meta.filename,
      );

      // Send group message with file (business logic handles everything)
      const result = await this.businessLogic.sendGroupMessage(account, groupIdentifier, text, savedFilePath);

      // Clean up file and return success
      this.businessLogic.cleanupFile(savedFilePath);
      return this.createSuccessResponse(
        `Đã gửi tin nhắn đến nhóm "${result.groupName}" (ID: ${result.recipient}) từ account ${account.displayName} thành công`,
        result,
      );
    } catch (error) {
      this.logger.error(`Group message error:`, error as Error);
      this.businessLogic.cleanupFile(savedFilePath);

      return this.handleMessageError(error, accountId, ctx);
    }
  }

  /**
   * Logout from specific account
   */
  @RequireRoles([USER_ROLE.ADMIN])
  @Action({
    rest: 'POST /auth/logout/:accountId',
    params: {
      accountId: { type: 'string' },
    },
  })
  async logout(ctx: Context<any, ApiGatewayMeta>): Promise<any> {
    const { accountId } = ctx.params;
    const account = this.instanceManager.getAccount(accountId);

    if (!account) {
      return this.createErrorResponse(`Account ${accountId} not found`);
    }

    if (!account.isAuthenticated) {
      return this.createSuccessResponse(`Account ${account.displayName} is not authenticated`);
    }

    try {
      // Use cleanupAccount helper but don't remove from map (just logout)
      this.instanceManager.cleanupAccount(account, false, 'logout');

      return this.createSuccessResponse(`Account ${account.displayName} logged out successfully`);
    } catch (error) {
      this.logger.error(`Logout error for account ${accountId}:`, error as Error);
      return this.createErrorResponse(`Error logging out account ${account.displayName}: ${(error as Error).message}`);
    }
  }

  /**
   * Logout from all accounts
   */
  @RequireRoles([USER_ROLE.ADMIN])
  @Action({
    rest: 'POST /auth/logout-all',
  })
  async logoutAll(ctx: Context<any, ApiGatewayMeta>): Promise<any> {
    const results = [];

    for (const [accountId, account] of this.instanceManager.getAllAccounts()) {
      if (account.isAuthenticated) {
        try {
          // Use cleanupAccount helper but don't remove from map (just logout)
          this.instanceManager.cleanupAccount(account, false, 'logout');

          results.push({
            accountId,
            accountName: account.displayName,
            success: true,
            message: `Account ${account.displayName} logged out successfully`,
          });
        } catch (error) {
          this.logger.error(`Logout error for account ${accountId}:`, error as Error);
          results.push({
            accountId,
            accountName: account.displayName,
            success: false,
            message: `Error logging out account ${account.displayName}: ${(error as Error).message}`,
          });
        }
      }
    }

    return {
      success: true,
      message: `Logged out ${results.length} accounts`,
      accountResults: results,
    };
  }

  /**
   * Create SSE event sender helper
   */
  private createSSEEventSender(stream: PassThrough) {
    return (eventName: string, data: any) => {
      const message = `event: ${eventName}\ndata: ${JSON.stringify(data)}\n\n`;
      stream.push(message);
      if (eventName === 'qr') {
        this.logger.info(`SSE Event sent: qr with qr code`);
      } else {
        this.logger.info(`SSE Event sent: ${eventName}`, data);
      }
    };
  }

  /**
   * Create standardized success response
   */
  private createSuccessResponse(message: string, data?: any, additionalFields?: Record<string, any>): any {
    return {
      success: true,
      message,
      ...(data && { data }),
      ...additionalFields,
    };
  }

  /**
   * Create standardized error response
   */
  private createErrorResponse(message: string, reason?: string, additionalFields?: Record<string, any>): any {
    return {
      success: false,
      message,
      ...(reason && { reason }),
      ...additionalFields,
    };
  }

  /**
   * Handle authentication error for account
   */
  private async handleAuthenticationError(
    ctx: Context<any, any>,
    account: ZaloAccount,
    savedFilePath: string | null,
  ): Promise<any> {
    this.logger.warn(`Authentication error detected for account ${account.id}, removing account...`);

    this.businessLogic.cleanupFile(savedFilePath, 'Auth error cleanup: ');

    try {
      await this.removeAccount(ctx);
      this.logger.info(`Account ${account.id} removed due to authentication error`);
    } catch (removeError) {
      this.logger.error(`Failed to remove account ${account.id}:`, removeError as Error);
    }

    return {
      success: false,
      message: `Tài khoản ${account.displayName} không hợp lệ và đã bị xóa. Vui lòng thêm tài khoản mới.`,
    };
  }

  /**
   * Handle message sending errors in a unified way
   */
  private async handleMessageError(error: any, accountId: string | undefined, ctx: Context<any, any>): Promise<any> {
    // Check if this is an authentication error
    if (this.businessLogic.isAuthenticationError(error)) {
      const account = this.instanceManager.getAccount(
        accountId || this.instanceManager.getFirstAuthenticatedAccount()?.id || '',
      );
      if (account) {
        return await this.handleAuthenticationError(ctx, account, null);
      }
    }

    // Handle validation errors (already have proper messages)
    if (error instanceof CommonErrors.BadRequestError) {
      return this.createErrorResponse(error.message);
    }

    // Generic error
    return this.createErrorResponse(`Error: ${(error as Error).message}`);
  }

  /**
   * Handle workgate creation event
   */
  @Method
  @Event({
    name: 'workGate.createWorkGate',
  })
  updateAccountOnWorkGateCreate(workGate: IWorkGate) {
    this.logger.info(`WorkGate created: ${workGate._id}, updating account info`);
    this.businessLogic.updateAccountFromWorkGate(workGate);
  }

  /**
   * Handle workgate update event
   */
  @Method
  @Event({
    name: 'workGate.updateWorkGate',
  })
  updateAccountOnWorkGateUpdate(workGate: IWorkGate) {
    this.logger.info(`WorkGate updated: ${workGate._id}, updating account info`);
    this.businessLogic.updateAccountFromWorkGate(workGate);
  }

  /**
   * Handle workgate removal event
   */
  @Method
  @Event({
    name: 'workGate.removeWorkGate',
  })
  updateAccountOnWorkGateRemove(workGate: IWorkGate) {
    this.logger.info(`WorkGate removed: ${workGate._id}, updating account info`);
    this.businessLogic.updateAccountFromWorkGate(workGate, true);
  }
}

export = ZaloService;
