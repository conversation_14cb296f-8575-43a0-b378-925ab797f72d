import { Action, ConcatMultiple, Event } from 'moleculer-ts';

export const name: 'zalo' = 'zalo';

// Define interfaces for Zalo service
interface AuthStatus {
  accounts: any[];
  totalAccounts: number;
  authenticatedAccounts: number;
}

interface MessageResult {
  success: boolean;
  message: string;
  data: {
    msgId: number;
    recipient: string;
    groupName?: string;
    message: { msgId: number } | null;
    attachment: { msgId: number }[];
  };
}

interface LoginResult {
  success: boolean;
  message: string;
  account?: any;
}

interface AccountResult {
  success: boolean;
  message: string;
}

interface LogoutAllResult {
  success: boolean;
  message: string;
  accountResults?: any[];
}

export type OwnInternalActions = [
  // Get authentication status
  Action<
    'getAuthStatus',
    {},
    {
      success: boolean;
      data: AuthStatus;
    }
  >,

  // Login with QR code
  Action<'loginQR', {}, LoginResult>,

  // Remove account
  Action<
    'removeAccount',
    {
      accountId: string;
    },
    AccountResult
  >,

  // Send text message
  Action<
    'sendTextMessage',
    {
      accountId?: string;
      recipient: string;
      text: string;
      file?: any;
    },
    MessageResult['data']
  >,

  // Send group message
  Action<
    'sendGroupMessage',
    {
      accountId?: string;
      groupIdentifier: string;
      text: string;
      file?: any;
    },
    MessageResult['data']
  >,
  // Send text message
  Action<
    'apiTextMessage',
    {
      accountId?: string;
      recipient: string;
      text: string;
      file?: any;
    },
    MessageResult
  >,
  // Send group message
  Action<
    'apiGroupMessage',
    {
      accountId?: string;
      groupIdentifier: string;
      text: string;
      file?: any;
    },
    MessageResult
  >,

  // Logout from specific account
  Action<
    'logout',
    {
      accountId: string;
    },
    AccountResult
  >,

  // Logout from all accounts
  Action<'logoutAll', {}, LogoutAllResult>,
];

export type OwnActions = ConcatMultiple<[OwnInternalActions]>;

export type OwnEvents = [
  // Zalo authentication events
  Event<'auth.success', { accountId: string; accountName: string }>,
  Event<'auth.failed', { accountId: string; error: string }>,
  Event<'message.sent', { accountId: string; recipient: string; success: boolean }>,
];

export type Actions = ConcatMultiple<[OwnActions]>;
export type Events = ConcatMultiple<[OwnEvents]>;
