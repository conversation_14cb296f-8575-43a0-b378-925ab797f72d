import { GroupInfoResponse, Message, ThreadType } from 'zca-js';
import { LoggerInstance } from 'moleculer';
import { Context, ServiceBroker } from '../../types/moleculer';
import * as fs from 'fs';
import { ReadStream } from 'fs';
import * as path from 'path';
import { getPromiseFileSteam } from '../../utils/FileUploadUtils';
import { CommonErrors } from '../../commons/error.helper';
import { IWorkGate, WORKGATE_STATUSES, WORKGATE_TYPES, ZaloQRServerGateConfig } from '../../entities/workGate.entity';
import { ZaloAccount, ZaloInstanceManager } from './zalo.instance.manager';
import { ZaloServiceTypes } from '../../types';

export class ZaloBusinessLogic {
  private logger: LoggerInstance;
  private broker: ServiceBroker;
  private instanceManager: ZaloInstanceManager;

  constructor(logger: LoggerInstance, broker: ServiceBroker, instanceManager: ZaloInstanceManager) {
    this.logger = logger;
    this.broker = broker;
    this.instanceManager = instanceManager;
  }

  /**
   * Initialize the Zalo service
   */
  async initZaloService(): Promise<void> {
    this.logger.info('Initializing Zalo Client API with multi-account support...');

    // Initialize the global Zalo instance
    this.instanceManager.getZaloInstance();

    // Wait 10 seconds before loading workgate accounts
    this.logger.info('Waiting 10 seconds before loading workgate Zalo accounts...');
    await new Promise<void>((resolve) => setTimeout(() => resolve(), 10000));

    // Load workgate Zalo QR accounts
    await this.loadWorkgateZaloAccounts();

    this.logger.info(`Zalo service initialized with ${this.instanceManager.getAccountsCount()} accounts`);
  }

  /**
   * Load Zalo QR accounts from workgate
   */
  async loadWorkgateZaloAccounts(): Promise<void> {
    try {
      this.logger.info('Loading Zalo QR accounts from workgate...');

      // Get all Zalo QR accounts from workgate
      const zaloQrAccounts = await this.broker.call('workGate.getAllZaloQr', {});

      if (!Array.isArray(zaloQrAccounts)) {
        this.logger.warn('No Zalo QR accounts found in workgate');
        return;
      }

      this.logger.info(`Found ${zaloQrAccounts.length} Zalo QR accounts in workgate`);

      // Load each account session one by one with 3-second delays
      for (const workgateAccount of zaloQrAccounts) {
        try {
          // Validate account data
          if (!this.instanceManager.isValidWorkgateAccount(workgateAccount)) {
            this.logger.warn(`Invalid workgate account data, skipping:`, workgateAccount?.id || 'unknown');
            continue;
          }

          this.logger.info(
            `Loading session for account: ${workgateAccount.gateConfig?.displayName || workgateAccount.id}`,
          );

          // Load session
          await this.loginBySessionData(workgateAccount);

          // Store workgate ID for future reference
          const account = this.instanceManager.getAccount(workgateAccount.gateConfig.id);
          if (account) {
            this.logger.info(`Successfully loaded account: ${account.displayName} (${account.id})`);
          }
        } catch (error) {
          this.logger.error(
            `Failed to load session for account ${workgateAccount?.gateConfig?.displayName || 'unknown'}:`,
            error as Error,
          );
        }

        // Wait 3 seconds before loading next account
        if (zaloQrAccounts.indexOf(workgateAccount) < zaloQrAccounts.length - 1) {
          this.logger.info('Waiting 3 seconds before loading next account...');
          await new Promise<void>((resolve) => setTimeout(() => resolve(), 3000));
        }
      }

      this.logger.info(`Finished loading workgate accounts. Total loaded: ${this.instanceManager.getAccountsCount()}`);
    } catch (error) {
      this.logger.error('Error loading workgate Zalo accounts:', error as Error);
    }
  }

  /**
   * Login by session data (internal method)
   */
  async loginBySessionData(workgateAccount: any): Promise<ZaloAccount> {
    const zaloInstance = this.instanceManager.getZaloInstance();

    // Create account with saved data
    const account = this.instanceManager.createAccount(workgateAccount);

    try {
      // Parse the cookie string into the expected format
      let cookieData;
      if (typeof workgateAccount.gateConfig.sessionData.cookie === 'string') {
        try {
          // Try to parse as JSON first
          cookieData = JSON.parse(workgateAccount.gateConfig.sessionData.cookie);
        } catch {
          // If not JSON, create URL format
          cookieData = {
            url: 'https://chat.zalo.me',
            cookies: workgateAccount.gateConfig.sessionData.cookie,
          };
        }
      } else {
        cookieData = workgateAccount.gateConfig.sessionData.cookie;
      }

      // Login using saved session
      account.zaloApi = await zaloInstance.login({
        cookie: cookieData,
        imei: workgateAccount.gateConfig.sessionData.imei,
        userAgent: workgateAccount.gateConfig.sessionData.userAgent,
      });

      if (account.zaloApi) {
        account.isAuthenticated = true;

        // Set up message event handlers for automatic replies
        this.setupMessageHandlers(account);

        // Start listener
        if (account.zaloApi.listener) {
          account.zaloApi.listener.start();
        }

        // Add to accounts
        this.instanceManager.addAccount(account);
        this.logger.info(`Successfully restored account: ${account.displayName} (${account.id})`);
      } else {
        throw new Error('Failed to create API instance from session');
      }

      return account;
    } catch (error) {
      this.logger.error(`Error restoring session for ${workgateAccount.gateConfig.displayName}:`, error as Error);
      throw error;
    }
  }

  /**
   * Handle successful login
   */
  async handleSuccessfulLogin(account: ZaloAccount): Promise<void> {
    if (!account.zaloApi) {
      throw new Error('API instance not available');
    }

    account.phoneToUserIdCache = {};
    account.groupCache = {};

    try {
      // Get user information
      const ownId = await account.zaloApi.getOwnId();

      if (!ownId) {
        throw new Error('Failed to get user ID');
      }

      const userInfo = (await account.zaloApi.getUserInfo(ownId))?.changed_profiles?.[ownId];

      // Extract name from user info object
      let displayName = 'Unknown';
      if (userInfo && typeof userInfo === 'object') {
        displayName = userInfo.displayName || 'Unknown';
      }

      // Generate new account ID with real user data
      const newAccountId = this.instanceManager.generateAccountId(ownId, displayName);
      const oldAccountId = account.id;

      // Update account with real user data
      account.id = newAccountId;
      account.displayName = displayName;
      account.phoneNumber = userInfo.phoneNumber || '';
      account.user = {
        id: ownId,
      };
      account.isAuthenticated = true;

      // Update session data with real user information
      if (account.sessionData) {
        account.savedAt = Date.now();
      }

      this.logger.info(`Successfully authenticated account ${newAccountId} (${displayName})`);
    } catch (error) {
      this.logger.error('Error getting user information', error as Error);
      throw error;
    }

    // Set up message event handlers for automatic replies
    this.setupMessageHandlers(account);

    // Start listener
    if (account.zaloApi.listener) {
      account.zaloApi.listener.start();
    }
  }

  /**
   * Set up message event handlers for automatic AI replies
   */
  setupMessageHandlers(account: ZaloAccount): void {
    if (!account.zaloApi || !account.zaloApi.listener) {
      this.logger.warn(`No API or listener available for account ${account.id}`);
      return;
    }

    this.logger.info(`Setting up message handlers for account ${account.id} (${account.displayName})`);

    // Handle incoming messages using the correct zca-js API
    // Type assertion to handle the listener API that TypeScript doesn't recognize
    // @ts-expect-error
    account.zaloApi.listener.on('message', async (message: Message) => {
      try {
        this.logger.info(`Received message: ${JSON.stringify(message)}`);
        await this.handleIncomingMessage(account, message);
      } catch (error) {
        this.logger.error(`Error handling incoming message for account ${account.id}:`, error as Error);
      }
    });

    this.logger.info(`====>Message handlers set up successfully for account ${account.id}`);
  }

  /**
   * Handle incoming messages and generate AI replies
   */
  async handleIncomingMessage(account: ZaloAccount, message: Message): Promise<void> {
    // Skip messages sent by this account (avoid responding to own messages)
    if (message.isSelf) {
      this.logger.info(`====>Skipping message sent by this account ${account.id}: ${JSON.stringify(message)}`);
      return;
    }

    // Skip if no workgate configuration
    if (!account.workgate?.gateConfig) {
      this.logger.warn(`No workgate configuration for account ${account.id}, skipping message processing`);
      return;
    }

    // Check if on_chat_bot_uuid is configured (similar to socketConnector logic)
    const config = account.workgate.config as any;
    const on_chat_uuid = config?.on_chat_bot_uuid;

    if (!on_chat_uuid) {
      this.logger.warn(`No on_chat_bot_uuid configured for account ${account.id}, skipping AI processing`);
      return;
    }

    // Extract message information based on zca-js message structure
    const senderId = message.threadId;
    const messageText = typeof message.data.content === 'string' ? message.data.content : '';
    const timestamp = Date.now();
    const isGroupMessage = message.type === ThreadType.Group;

    // Create appropriate channel ID
    const channel_id = isGroupMessage ? `zalo_group:${message.threadId}` : `zalo:${account.id}`;
    const environment = 'prod';

    // For group messages, check if bot should respond
    if (isGroupMessage) {
      const shouldRespond = this.shouldRespondToGroupMessage(message, account);
      if (!shouldRespond) {
        this.logger.debug(`Skipping group message - no response trigger detected`);
        return;
      }
    }

    this.logger.info(
      `📨 Received ${isGroupMessage ? 'group ' : ''}message from ${senderId} for account ${
        account.displayName
      }: ${messageText}`,
    );

    try {
      // Process the message with coreAi (similar to socketConnector logic)
      const result: any = await this.broker.call('coreAi.actionProcessFlow' as any, {
        request_body: {
          message: messageText,
          profile: {
            name: message.data.dName || '',
            avatar: '',
            phone: '',
          },
          rawMessage: message.data,
          type: message.type,
          msgType: message.data.msgType,
        },
        bot_uuid: on_chat_uuid,
        thread_id: isGroupMessage ? `${message.threadId}_${senderId}` : senderId,
        channel_id,
        environment,
        timestamp,
      });

      // Send AI reply if available
      if (result?.message) {
        this.logger.info(
          `🤖 Sending AI reply to ${isGroupMessage ? `group ${message.threadId}` : senderId} from account ${
            account.displayName
          }: ${result.message}`,
        );

        try {
          await account.zaloApi!.sendMessage(result.message, message.threadId, message.type);
          this.logger.info(
            `✅ AI reply sent successfully to ${isGroupMessage ? `group ${message.threadId}` : senderId}`,
          );
        } catch (sendError) {
          this.logger.error(
            `Failed to send AI reply to ${isGroupMessage ? `group ${message.threadId}` : senderId}:`,
            sendError as Error,
          );
        }
      }
    } catch (error) {
      this.logger.error(`Error processing message from ${senderId} for account ${account.id}:`, error as Error);
    }
  }

  /**
   * Determine if the bot should respond to a group message
   */
  shouldRespondToGroupMessage(message: Message, account: ZaloAccount): boolean {
    const messageText = typeof message.data.content === 'string' ? message.data.content : '';
    const botName = account.displayName.toLowerCase();
    const text = messageText.toLowerCase();

    // Check if the bot is mentioned in the message
    const messageData = message.data as any;
    if (messageData.mentions && Array.isArray(messageData.mentions)) {
      const botUserId = account.user?.id;
      if (botUserId) {
        const isMentioned = messageData.mentions.some((mention: any) => mention.uid === botUserId);
        if (isMentioned) {
          return true;
        }
      }
    }

    // Respond if: Message start with ai:, /ai
    const commandPrefixes = ['ai:', '/ai'];

    // Check for command prefixes
    if (commandPrefixes.some((prefix) => text.startsWith(prefix))) {
      return true;
    }

    return false;
  }

  /**
   * Resolve a recipient (phone number or user ID) to a user ID
   */
  async resolveRecipient(account: ZaloAccount, recipient: string): Promise<string> {
    if (!account.zaloApi) {
      throw new Error('Zalo client not initialized for this account');
    }

    let userId = recipient;

    // Check if recipient is likely a phone number
    if (/^(\+|[0-9])[0-9]{8,}$/.test(recipient)) {
      // Check cache first
      if (account.phoneToUserIdCache[recipient]) {
        this.logger.debug(
          `Using cached user ID for phone number ${recipient}: ${account.phoneToUserIdCache[recipient]}`,
        );
        return account.phoneToUserIdCache[recipient];
      }

      this.logger.debug(`Phone number ${recipient} not in cache, looking up user ID...`);

      // Find the user using the phone number
      const user = await account.zaloApi.findUser(recipient);

      if (!user || !user.uid) {
        throw new Error(`User with phone number ${recipient} not found`);
      }

      userId = user.uid;
      this.logger.debug(`Found user ID: ${userId} for phone number: ${recipient}`);

      // Cache the result
      account.phoneToUserIdCache[recipient] = userId;
    }

    return userId;
  }

  /**
   * Update group cache for specific account
   */
  async updateGroupCache(account: ZaloAccount): Promise<void> {
    if (!account.zaloApi) {
      throw new Error('Zalo client chưa được khởi tạo');
    }

    const groups: any = await account.zaloApi.getAllGroups();
    if (!groups || !groups.gridVerMap) {
      throw new Error('Không thể lấy danh sách nhóm');
    }

    const groupIds = Object.keys(groups.gridVerMap);
    const groupInfo: GroupInfoResponse = await account.zaloApi.getGroupInfo(groupIds);

    // Remove old groups
    account.groupCache = {};

    // Update cache with new group information
    for (const [id, info] of Object.entries(groupInfo.gridInfoMap)) {
      if (info.name) {
        account.groupCache[id] = {
          name: info.name,
          lastUpdated: Date.now(),
          info: info,
        };
      }
    }
  }

  /**
   * Find group by name in cache
   */
  findGroupInCache(account: ZaloAccount, groupName: string): string | null {
    const searchName = groupName.toLowerCase();
    for (const [id, group] of Object.entries(account.groupCache)) {
      if (group.name.toLowerCase().includes(searchName)) {
        return id;
      }
    }
    return null;
  }

  /**
   * Validate and save uploaded file
   */
  async validateAndSaveFile(
    fileStream: ReadStream | null,
    mimetype: string | undefined,
    accountId: string,
    originalFilename?: string,
  ): Promise<string | null> {
    if (!fileStream || !mimetype) {
      return null;
    }

    // Validate file type - allow images, documents, and audio
    const allowedMimeTypes = [
      // Images
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      // Documents
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      // Audio
      'audio/mpeg',
      'audio/mp3',
      'audio/wav',
      'audio/m4a',
      'audio/aac',
      'audio/ogg',
      'audio/webm',
    ];

    if (!allowedMimeTypes.includes(mimetype)) {
      fileStream.destroy();
      throw new CommonErrors.BadRequestError(
        'Only images (JPEG, PNG, GIF, WEBP), documents (PDF, DOC, DOCX, TXT), and audio files (MP3, WAV, M4A, AAC, OGG) are allowed',
      );
    }

    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'uploads', 'zalo-attachments', accountId || 'default');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    // Generate filename - preserve original name if available, otherwise use mimetype-based extension
    let fileName: string;
    if (originalFilename) {
      fileName = originalFilename;
    } else {
      // Fallback to mimetype-based naming when no original filename
      const extension = mimetype.split('/')[1];
      fileName = `${Date.now()}_${Math.random().toString(36).substring(2, 9)}.${extension}`;
    }

    const filePath = path.join(uploadsDir, fileName);

    try {
      // Save file to uploads directory
      const { filePath: savedPath } = await getPromiseFileSteam(
        fileStream,
        filePath,
        25 * 1024 * 1024, // 25MB limit
      );
      this.logger.info(`File saved to: ${savedPath}`);
      return savedPath;
    } catch (error) {
      this.logger.error('Error saving file:', error as Error);
      throw new CommonErrors.BadRequestError(`Error saving file: ${(error as Error).message}`);
    }
  }

  /**
   * Validate and get target account
   */
  validateAndGetAccount(accountId: string | undefined, savedFilePath: string | null): ZaloAccount {
    // If no specific accountId provided, use the first authenticated account
    let targetAccountId = accountId;
    if (!targetAccountId) {
      const firstAccount = this.instanceManager.getFirstAuthenticatedAccount();
      if (!firstAccount) {
        this.cleanupFile(savedFilePath);
        throw new CommonErrors.BadRequestError('No authenticated accounts available');
      }
      targetAccountId = firstAccount.id;
    }

    const account = this.instanceManager.getAccount(targetAccountId);
    if (!account) {
      this.cleanupFile(savedFilePath);
      throw new CommonErrors.BadRequestError(`Account ${targetAccountId} not found`);
    }

    if (!account.zaloApi || !account.isAuthenticated) {
      this.cleanupFile(savedFilePath);
      throw new CommonErrors.BadRequestError(`Account ${account.displayName} is not authenticated`);
    }

    return account;
  }

  /**
   * Get account safely without file cleanup (for use when no file is involved)
   */
  getAccountSafely(accountId: string | undefined): ZaloAccount {
    return this.validateAndGetAccount(accountId, null);
  }

  /**
   * Clean up saved file
   */
  cleanupFile(filePath: string | null, logPrefix: string = ''): void {
    if (!filePath || !fs.existsSync(filePath)) {
      return;
    }

    try {
      fs.unlinkSync(filePath);
      this.logger.info(`${logPrefix}Cleaned up file: ${filePath}`);
    } catch (error) {
      this.logger.warn(`${logPrefix}Error cleaning up file:`, error as Error);
    }
  }

  /**
   * Check if error is a ZaloApiError with authentication issues
   */
  isAuthenticationError(error: any): boolean {
    return (
      error &&
      error.message &&
      typeof error.message === 'string' &&
      error.message.includes('zpw_sek bị thiếu hoặc không đúng') &&
      error.code === 600
    );
  }

  /**
   * Check if error is a login session failure
   */
  isLoginSessionError(error: any): boolean {
    return (
      error &&
      error.message &&
      typeof error.message === 'string' &&
      (error.message.includes("null is not an object (evaluating 'loginData.data.zpw_enk')") ||
        error.message.includes("Cannot read property 'zpw_enk' of null") ||
        error.message.includes('Đăng nhập thất bại'))
    );
  }

  /**
   * Create workgate entry for an account
   */
  async createWorkgateForAccount(account: ZaloAccount, ctx: Context<any, any>): Promise<any> {
    const workGateConfig: ZaloQRServerGateConfig = {
      id: account.id,
      displayName: account.displayName,
      phoneNumber: account.phoneNumber || '',
      user: account.user,
      sessionData: account.sessionData!,
      savedAt: account.savedAt,
    };

    // Create new workgate with ZALO_QR type
    const newWorkGate: any = await this.broker.call(
      'workGate.actionCreate',
      {
        description: `Zalo QR Account - ${account.displayName}`,
        type: WORKGATE_TYPES.ZALO_QR,
        config: {}, // Empty config for now
      },
      { parentCtx: ctx },
    );

    this.logger.info(`Created workgate ${newWorkGate._id} for account ${account.id}`);

    // Update gateConfig with account information
    await this.broker.call(
      'workGate.actionUpdateGateConfig',
      {
        _id: newWorkGate._id,
        gateConfig: workGateConfig,
      },
      { parentCtx: ctx },
    );

    this.logger.info(`Updated gateConfig for workgate ${newWorkGate._id} with account data`);

    // Update account workgate info with new workgate data
    account.workgate = {
      id: newWorkGate._id,
      team_id: ctx.meta.user.teamId.toString(),
      config: newWorkGate.config,
      gateConfig: workGateConfig,
    };

    return newWorkGate;
  }

  /**
   * Update existing workgate entry for an account (re-link functionality)
   */
  async updateWorkgateForAccount(account: ZaloAccount, workgateId: string, ctx: Context<any, any>): Promise<any> {
    // Verify the workgate exists and belongs to the user's team
    const existingWorkGate: any = await this.broker.call(
      'workGate.actionGet',
      { _id: workgateId },
      { parentCtx: ctx },
    );

    if (!existingWorkGate) {
      throw new Error(`Workgate ${workgateId} not found`);
    }

    if (existingWorkGate.type !== WORKGATE_TYPES.ZALO_QR) {
      throw new Error(`Workgate ${workgateId} is not a Zalo QR workgate`);
    }

    const workGateConfig: ZaloQRServerGateConfig = {
      id: account.id,
      displayName: account.displayName,
      phoneNumber: account.phoneNumber || '',
      user: account.user,
      sessionData: account.sessionData!,
      savedAt: account.savedAt,
    };

    this.logger.info(`Updating workgate ${workgateId} for account ${account.id}`);

    // Update gateConfig with new account information
    await this.broker.call(
      'workGate.actionUpdateGateConfig',
      {
        _id: workgateId,
        gateConfig: workGateConfig,
      },
      { parentCtx: ctx },
    );

    this.logger.info(`Updated gateConfig for workgate ${workgateId} with account data`);

    // Update account workgate info with existing workgate data
    account.workgate = {
      id: existingWorkGate._id,
      team_id: ctx.meta.user.teamId.toString(),
      config: existingWorkGate.config,
      gateConfig: workGateConfig,
    };

    return existingWorkGate;
  }

  /**
   * Send text message - complete business logic
   */
  async sendTextMessage(
    account: ZaloAccount,
    recipient: string,
    text: string,
    savedFilePath?: string | null,
  ): Promise<ZaloServiceTypes.ActionReturn<'sendTextMessage'>> {
    // Resolve recipient to user ID
    const userId = await this.resolveRecipient(account, recipient);

    // Send the message
    let result;
    if (savedFilePath) {
      // Send message with file attachment
      result = await account.zaloApi!.sendMessage(
        { msg: text || '', attachments: [savedFilePath] },
        userId,
        ThreadType.User,
      );
    } else {
      // Send text-only message
      result = await account.zaloApi!.sendMessage(text, userId, ThreadType.User);
    }

    this.logger.info(`Message sent successfully from account ${account.displayName} to ${recipient}`);
    return {
      msgId: result?.message?.msgId || result?.attachment?.[0]?.msgId,
      recipient: userId,
      ...result,
    };
  }

  /**
   * Send group message - complete business logic
   */
  async sendGroupMessage(
    account: ZaloAccount,
    groupIdentifier: string,
    text: string,
    savedFilePath?: string | null,
  ): Promise<ZaloServiceTypes.ActionReturn<'sendGroupMessage'>> {
    // Resolve group identifier to group ID
    let groupId = groupIdentifier;

    // If the identifier doesn't look like a group ID, try to find by name
    if (!/^\d+$/.test(groupIdentifier)) {
      // Check if cache is empty
      if (Object.keys(account.groupCache).length === 0) {
        await this.updateGroupCache(account);
      }

      // Try to find group in cache
      const foundGroupId = this.findGroupInCache(account, groupIdentifier);
      if (foundGroupId) {
        groupId = foundGroupId;
      } else {
        // If not found in cache, try to update cache and search again
        await this.updateGroupCache(account);
        const foundGroupIdAfterUpdate = this.findGroupInCache(account, groupIdentifier);
        if (foundGroupIdAfterUpdate) {
          groupId = foundGroupIdAfterUpdate;
        } else {
          throw new Error(`Không tìm thấy nhóm có tên chứa "${groupIdentifier}" trong account ${account.displayName}`);
        }
      }
    }

    // Send the message
    let result;
    if (savedFilePath) {
      // Send message with file attachment
      result = await account.zaloApi!.sendMessage(
        { msg: text || '', attachments: [savedFilePath] },
        groupId,
        ThreadType.Group,
      );
    } else {
      // Send text-only message
      result = await account.zaloApi!.sendMessage(text, groupId, ThreadType.Group);
    }

    const groupName = account.groupCache[groupId]?.name || 'Unknown';
    this.logger.info(
      `Message sent successfully from account ${account.displayName} to group ${groupName} (${groupId})`,
    );

    return {
      msgId: result?.message?.msgId || result?.attachment?.[0]?.msgId,
      recipient: groupId,
      groupName,
      ...result,
    };
  }

  /**
   * Update account information based on workgate changes
   */
  updateAccountFromWorkGate(workGate: IWorkGate, isRemoval: boolean = false): void {
    try {
      // Skip if not a Zalo QR workgate
      if (workGate.type !== 6) {
        // WORKGATE_TYPES.ZALO_QR = 6
        this.logger.debug(`Ignoring non-Zalo workgate type: ${workGate.type}`);
        return;
      }

      // Extract account ID from gateConfig
      const gateConfig = workGate.gateConfig as any;
      if (!gateConfig || !gateConfig.id) {
        this.logger.warn(`No account id found in workgate ${workGate._id} gateConfig`);
        return;
      }

      const accountId = gateConfig.id;
      const account = this.instanceManager.getAccount(accountId);

      if (isRemoval) {
        // Handle workgate removal - remove account from list
        this.logger.info(`Removing workgate and account ${accountId} due to workgate removal`);

        if (account) {
          // Clean up and remove account from the accounts map
          this.instanceManager.cleanupAccount(account, true, 'workgate removed');
        } else {
          this.logger.warn(`Account ${accountId} not found for workgate removal`);
        }
        return;
      }

      // Handle workgate creation/update
      if (!account) {
        // Account doesn't exist - check if we should create it for active workgates
        if (workGate.status === WORKGATE_STATUSES.ACTIVE) {
          this.logger.info(
            `Account ${accountId} not found for active workgate ${workGate._id}, will attempt to login after 5s`,
          );

          // Delay login attempt by 5 seconds to prevent errors
          setTimeout(async () => {
            try {
              // Check if account was created by another process during the delay
              const existingAccount = this.instanceManager.getAccount(accountId);
              if (existingAccount) {
                this.logger.info(`Account ${accountId} already exists, skipping login attempt`);
                return;
              }

              await this.loginBySessionData(workGate);
              this.logger.info(`Successfully logged in account ${accountId} from workgate activation`);
            } catch (error) {
              this.logger.error(`Failed to login account ${accountId} from workgate activation:`, error as Error);
            }
          }, 5000);
        } else {
          this.logger.debug(`Account ${accountId} not found for inactive workgate ${workGate._id}, skipping`);
        }
        return;
      }

      // Account exists - update workgate reference
      this.logger.info(`Updating workgate reference for account ${accountId}`);
      account.workgate = {
        id: workGate._id || '',
        team_id: workGate.team_id?.toString() || '',
        config: workGate.config,
        gateConfig: gateConfig as any,
      };

      // Handle status changes
      if (workGate.status === WORKGATE_STATUSES.DISABLED) {
        this.logger.info(`Removing account ${accountId} due to workgate disabled`);
        // Remove account from list when workgate is disabled
        this.instanceManager.cleanupAccount(account, true, 'workgate disabled');
      } else if (workGate.status === WORKGATE_STATUSES.ACTIVE) {
        this.logger.info(`WorkGate ${workGate._id} is active for account ${accountId}`);
        // Account can continue operating if already authenticated
        // No action needed for activation if account is already running
      }

      // Update any other workgate config that might affect the account
      this.updateAccountConfigFromWorkGate(account, workGate);

      this.logger.info(`Account ${accountId} updated successfully for workgate event`);
    } catch (error) {
      this.logger.error(`Error updating account from workgate event:`, error as Error);
    }
  }

  /**
   * Update account configuration from workgate settings
   */
  private updateAccountConfigFromWorkGate(account: ZaloAccount, workGate: IWorkGate): void {
    try {
      const gateConfig = workGate.gateConfig as any;

      // Update any account settings from workgate config
      if (gateConfig) {
        // Example: Update settings that might be configurable per workgate
        // This could include message handling preferences, rate limits, etc.
        this.logger.debug(`Updating account ${account.id} config from workgate settings`);

        // Add any specific configuration updates here based on your workgate schema
        // For example:
        // if (gateConfig.messageSettings) {
        //   account.messageSettings = gateConfig.messageSettings;
        // }
      }
    } catch (error) {
      this.logger.error(`Error updating account config from workgate:`, error as Error);
    }
  }
}
