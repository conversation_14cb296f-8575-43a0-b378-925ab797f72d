import { Action, ConcatMultiple, Event } from 'moleculer-ts';
import { ICustomerContact, ThreadInfo } from '../../entities/customerContact.entity';
import { IUser } from '../../entities/user.entity';
import { DbBaseMixinActions } from '../../mixins';
import { ActionListQuery, DBPagination } from '../../types';
import { ThreadProfile } from '../../types/flow.node.types';

export const name: 'customerContact' = 'customerContact';

export type OwnInternalActions = [
  // Basic CRUD operations
  Action<
    'actionCreate',
    {
      bot_uuid: string;
      name: string;
      phone?: string;
      address?: string;
      note?: string;
      threads?: ThreadInfo[];
    },
    ICustomerContact
  >,
  Action<
    'actionUpdate',
    {
      _id: string;
      name?: string;
      phone?: string;
      address?: string;
      note?: string;
    },
    ICustomerContact
  >,
  Action<
    'actionGet',
    {
      _id: string;
    },
    ICustomerContact
  >,
  Action<
    'actionDelete',
    {
      _id: string;
    },
    { success: boolean }
  >,
  // Quick search for dropdown
  Action<
    'actionQuickSearch',
    {
      bot_uuid: string;
      search: string;
      page?: number;
      pageSize?: number;
    },
    DBPagination<ICustomerContact>
  >,
  // Link/unlink chat histories
  Action<
    'actionLinkChatHistory',
    {
      _id: string;
      thread_id: string;
      bot_uuid: string;
    },
    { success: boolean }
  >,
  Action<
    'actionUnlinkChatHistory',
    {
      _id: string;
      thread_id: string;
    },
    { success: boolean }
  >,
  // Get customer contact by thread_id
  Action<
    'actionGetByThreadId',
    {
      thread_id: string;
      bot_uuid: string;
    },
    ICustomerContact
  >,
  // Quick create from chat history
  Action<
    'actionQuickCreateFromChatHistory',
    {
      bot_uuid: string;
      thread_id: string;
    },
    ICustomerContact
  >,
  // List action
  Action<
    'actionList',
    ActionListQuery & {
      bot_uuid?: string;
    },
    DBPagination<ICustomerContact>
  >,
];

export type OwnActions = ConcatMultiple<[OwnInternalActions, DbBaseMixinActions<IUser>]>;

export type OwnEvents = [
  // Entity events
  Event<'created', ICustomerContact>,
  Event<'updated', ICustomerContact>,
  Event<'removed', ICustomerContact>,
];

export type Actions = ConcatMultiple<[OwnActions]>;
export type Events = ConcatMultiple<[OwnEvents]>;
