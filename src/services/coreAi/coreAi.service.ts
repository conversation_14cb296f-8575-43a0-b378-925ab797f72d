'use strict';
import axios from 'axios';
import IsolatedVM from 'isolated-vm';
import { Action, Service as DService } from 'moleculer-decorators';
import mongoose from 'mongoose';
import OpenAI from 'openai';
import { ChatCompletionMessageParam } from 'openai/src/resources/chat/completions';
import { API_CODE, CommonErrors } from '../../commons/error.helper';
import { ChatMode } from '../../entities/chatHistory.entity';
import ConfigMixin from '../../mixins/config.mixin';
import { ConfigServiceTypes, CoreAiServiceTypes } from '../../types';
import { Context, Service } from '../../types/moleculer';
import CommonUtils from '../../utils/CommonUtils';
import { initError } from '../../utils/ErrorTracking';
import JSVMUtils from '../../utils/JSVMUtils';
import { calculateTokenUsage, runFlowBlock } from './runFlowBlock';
import { BotThreadSession, TokenUsage } from '../../types/flow.node.types';

const supportedGptModel = ['gpt-4o-mini'];

/**
 * Get credit from tokens
 * 1k Cre = 0.5$ = 1m tokens (0.5$)
 * 1 Cre = 0.0005$ = 1k tokens (0.5$)
 *
 * Logic: Real price (in millions tokens) (Round up 0.25) * 4
 */
function getCreditFromTokens(
  modelType: 'gpt',
  modelName: 'gpt-4o-mini' | 'text-embedding-ada-002' | 'text-embedding-3-small' | 'text-embedding-3-large',
  prompt_tokens: number,
  completion_tokens: number,
) {
  let cost = 0;
  if (modelType === 'gpt') {
    if (modelName === 'gpt-4o-mini') {
      // Cost: 0.15 in ,0.6 out
      cost = (prompt_tokens / 1000) * 0.5 + (completion_tokens / 1000) * 1.5;
    } else if (modelName === 'text-embedding-ada-002' || modelName === 'text-embedding-3-large') {
      // Cost: 0.1 ~ 0.13
      cost = ((prompt_tokens + completion_tokens) / 1000) * 0.25;
    } else if (modelName === 'text-embedding-3-small') {
      // Cost: 0.02 (Too low, free)
      cost = 0;
    }
  }

  return cost;
}

@DService({
  name: CoreAiServiceTypes.name,
  mixins: [ConfigMixin(['users.**'])],
  settings: {
    fields: ['_id', 'username', 'name', 'permissions', 'role', 'status'],
  },
})
class CoreAiService extends Service<{
  fields: string[];
}> {
  dependencies: ['api', 'botSetting', 'chatHistory'];

  openai?: OpenAI | undefined;

  @Action({
    // rest: 'POST /processFlow',
    params: {
      request_body: { $$t: 'Schema url', type: 'object', optional: false },
      bot_uuid: { $$t: 'Schema url', type: 'string', optional: false },
      thread_id: { $$t: 'Schema url', type: 'string', optional: false },
      environment: { $$t: 'Schema url', type: 'string', optional: false },
      channel_id: { $$t: 'Schema url', type: 'string', optional: false },
      timestamp: { $$t: 'Schema url', type: 'number', optional: true },
    },
  })
  async actionProcessFlow(
    ctx: Context<CoreAiServiceTypes.ActionParams<'actionProcessFlow'>>,
  ): Promise<CoreAiServiceTypes.ActionReturn<'actionProcessFlow'>> {
    const bot_uuid = ctx.params.bot_uuid;
    const thread_id = ctx.params.thread_id;
    const environment = ctx.params.environment;
    const channel_id = ctx.params.channel_id;
    const timestamp = ctx.params.timestamp;
    const request_body = ctx.params.request_body;

    const usage: TokenUsage = {
      prompt_tokens: 0,
      eval_tokens: 0,
      cost: 0,
    };

    // Main flow for running core AI processing
    // 1. Get flow info from bot_uuid
    const botSetting = await this.broker.call(
      'botSetting.actionLocalGet',
      {
        _id: bot_uuid,
        environment: environment as 'dev' | 'prod',
      },
      { parentCtx: ctx },
    );
    if (!botSetting?.config) {
      throw new CommonErrors.NotFoundError('Bot not found!');
    }

    // Check team package status and limits
    const creditCheck = await this.broker.call(
      'teamPackage.actionCheckCreditLimits',
      { teamId: botSetting.team_id },
      { parentCtx: ctx },
    );

    if (!creditCheck.isValid) {
      throw new CommonErrors.CreditLimitExceededError(creditCheck.errors.join(', '));
    }

    const flow = botSetting.config;

    // 2. Get session and chat history
    const user_msg_id = new mongoose.Types.ObjectId().toString();
    const { session, histories, chatMode } = await this.broker.call(
      'chatHistory.actionSaveSessionHistoryRecord',
      {
        bot_uuid,
        thread_id,
        environment,
        channel_id,
        // session: undefined, // For set user message, no need to send session
        history: {
          _id: user_msg_id,
          message: request_body.message,
          timestamp: timestamp || Date.now(),
          sender: 'user',
        },
      },
      { parentCtx: ctx },
    );

    // Return if chatMode is not 'chat'
    if (chatMode === ChatMode.DISABLE_CHAT) {
      return {
        msg_id: user_msg_id,
        message: '',
        usage: usage,
        thinking: [],
      };
    }

    // Check current timestamp less than 4hours
    if (timestamp) {
      const diff = Date.now() - timestamp;
      if (diff > 4 * 60 * 60 * 1000) {
        this.logger.warn(`Old message: ${bot_uuid} ${thread_id} ${environment} ${channel_id} ${timestamp} ${diff}`);
        return {
          msg_id: user_msg_id,
          message: '',
          usage: usage,
          thinking: [],
        };
      }
    }

    // Filter histories to include only those with sender equal to 'user' or 'assistant'
    const filteredHistories = histories.filter(
      (history) => history.sender === 'user' || history.sender === 'assistant',
    );

    const jvm = {
      isolate: new IsolatedVM.Isolate({ memoryLimit: 12 }),
      jsContext: undefined as IsolatedVM.Context,
      jail: undefined as IsolatedVM.Reference,
    };

    // Merge profile info with session.profile taking precedence at the field level
    if (request_body.profile) {
      if (!session.profile) {
        session.profile = {};
      }
      Object.keys(request_body.profile).forEach((key) => {
        if (
          (session.profile[key] === undefined || session.profile[key] === '') &&
          request_body.profile[key] !== undefined
        ) {
          session.profile[key] = request_body.profile[key];
        }
      });
    }
    const threadSession = {
      session: session,
      topic: '',
      histories: filteredHistories, // Use the filtered histories
      variables: {
        human_input: '',
      },
      startTime: Date.now(), // Set start time when flow processing begins
      currentBlockId: '',
      currentBlockType: '',
      eval: async (statement: string) => {
        await JSVMUtils.eval(jvm.jsContext, 'global.objectResult = ' + statement);
        const objectResult = await jvm.jail.get('objectResult');
        if (typeof objectResult === 'object') {
          return await objectResult.copy();
        } else {
          return objectResult;
        }
      },
      run: async (statement: string) => {
        let result = undefined;
        // Inject custom console.log into the isolated context
        await jvm.jail.set('consolelog', function (...args: any) {
          if (args?.length > 0) {
            // TODO: Need check script can handle object
            result = args[0];
          }
        });
        await JSVMUtils.eval(jvm.jsContext, 'console.log = consolelog;\n' + statement);
        return result;
      },
      evalString: (statement: string) => {
        return JSVMUtils.evalString(jvm.jsContext, statement);
      },
      setVariables(newVariables: { [p: string]: any }) {
        const keys = Object.keys(newVariables); // Get the keys of newVariables
        const updateVariable: { [key: string]: any } = {};
        for (let i = 0; i < keys.length; i++) {
          const key = keys[i];
          const valueResult = newVariables[key];
          const nestedPath = CommonUtils.extractNestedPath(key);

          // Set back to thread session
          if (nestedPath.primaryKey === 'session') {
            if (nestedPath.nestedPath) {
              CommonUtils.setNestedValue(this.session, nestedPath.nestedPath, valueResult, true);
            } else {
              this.session = { ...this.session, ...valueResult };
            }
            updateVariable['session'] = this.session;
          } else {
            CommonUtils.setNestedValue(this.variables, key, valueResult, true);
            updateVariable[key] = this.variables[key];
          }
        }

        return JSVMUtils.setVariables(jvm.jail, updateVariable);
      },
      isBreak: false, // Initialize isBreak to false
      isRecall: false, // Initialize isRecall to false
      runCount: 0, // Initialize runCount to 0
      thinking: [],
    } as BotThreadSession;

    try {
      // 3. Running step by step action that define in flow info -> sequence
      let recall = false;
      let recallCounter = 0;

      do {
        if (jvm.jsContext) {
          jvm.jsContext.release();
          jvm.jsContext = undefined;
          jvm.jail = undefined;
        }
        const jvm2 = await JSVMUtils.createSmallContext(jvm.isolate);
        jvm.jsContext = jvm2.jsContext;
        jvm.jail = jvm2.jail;

        const chat_link = `https://hub.bizino.ai/workbots/${bot_uuid}/chat/${environment}/${channel_id}/${thread_id}/${user_msg_id}`;

        // Add init variable to jsContext
        const initVariables = {
          human_input: request_body.message,
          request_body: request_body,
          bot_reply: threadSession.variables.bot_reply || request_body.message,
          recall_counter: recallCounter,
          bot_uuid: bot_uuid,
          thread_id: thread_id,
          channel_type: environment,
          channel_id: channel_id,
          user_msg_id: user_msg_id, // Use the _id of the history saved in step "2. Get session and chat history"
          chat_link: chat_link,
        };
        await threadSession.setVariables(initVariables);
        // Add init variable to thinking
        threadSession.thinking.push({
          id: 'input',
          type: 'input',
          name: 'Input',
          data: initVariables,
        });

        for (const action of flow.sequence) {
          if (action.disable) {
            continue;
          }
          const blockResult = await runFlowBlock(ctx, threadSession, action, { bot_uuid, thread_id });
          calculateTokenUsage(usage, blockResult.usage);
          if (threadSession.isBreak) {
            this.broker.logger.info('Break encountered, stopping flow processing');
            break;
          }
        }
        if (threadSession.isRecall) {
          if (recallCounter < 3) {
            this.broker.logger.info('Recall encountered, rerunning flow');
            recall = true;
            recallCounter++;
            threadSession.isRecall = false;
            threadSession.isBreak = false;
          } else {
            // Set result to empty
            threadSession.variables.bot_reply = '';
            recall = false; // Stop the recall loop and continue with the next block
          }
        } else {
          recall = false; // Stop the recall loop and continue with the next block
        }
      } while (recall);
    } catch (err) {
      const isDev = environment === 'dev';
      if (typeof err === 'object') {
        err.code = API_CODE.UNPROCESSABLE;
        err.message = `Error at block ${threadSession.currentBlockType}|${threadSession.currentBlockId}: ${
          isDev ? err?.message : ''
        }`;
      }
      if (isDev) {
        // Add more detail for dev environment
        if (typeof err === 'string') {
          throw new CommonErrors.ServerError(
            err,
            {
              thinking: threadSession.thinking,
              usage: usage,
            },
            'UNPROCESSABLE',
          );
        } else {
          if (Array.isArray(err.data)) {
            err.data = { data: err.data, thinking: threadSession.thinking, usage: usage };
          } else if (err.data && typeof err.data === 'object') {
            err.data.thinking = threadSession.thinking;
            err.data.usage = usage;
          } else {
            err.data = { thinking: threadSession.thinking, usage: usage };
          }
        }
      }

      // Save chat history with error message before returning to client
      await this.broker
        .call(
          'chatHistory.actionSaveSessionHistoryRecord',
          {
            bot_uuid,
            thread_id,
            environment,
            channel_id,
            history: {
              _id: new mongoose.Types.ObjectId(),
              message: err.message,
              timestamp: Date.now(),
              sender: 'error',
              thinking: threadSession.thinking, // Include thinking array in the history object
            },
          },
          { parentCtx: ctx },
        )
        .catch((err) => {
          this.broker.logger.error('Failed to save chat history:', err);
        })
        .then(() => {
          if (err.data && err.data.usage) {
            const roundedCost = err.data.usage.cost < 1 ? 1 : Math.round(err.data.usage.cost);
            return this.broker.call(
              'tokenUsage.addTokenUsage',
              {
                teamId: botSetting.team_id,
                bot_uuid: ctx.params.bot_uuid,
                prompt_token: err.data.usage.prompt_tokens,
                eval_token: err.data.usage.eval_tokens,
                cost: roundedCost,
              },
              { parentCtx: ctx },
            );
            // .catch((err) => {
            //   this.broker.logger.error('Failed to add token usage:', err);
            // });
          }
        });

      throw err;
    }

    // 4. Save session and chat history. No need to wait save success
    await this.broker
      .call(
        'chatHistory.actionSaveSessionHistoryRecord',
        {
          bot_uuid,
          thread_id,
          environment,
          channel_id,
          session: threadSession.session,
          history: {
            _id: new mongoose.Types.ObjectId(),
            message: threadSession.variables.bot_reply,
            timestamp: Date.now(),
            sender: 'assistant',
            _usage: usage,
            thinking: threadSession.thinking, // Include thinking array in the history object
          },
        },
        { parentCtx: ctx },
      )
      .catch((err) => {
        this.broker.logger.error('Failed to save chat history:', err);
      })
      .then(() => {
        const roundedCost = usage.cost < 1 ? 1 : Math.round(usage.cost);
        return this.broker.call(
          'tokenUsage.addTokenUsage',
          {
            teamId: botSetting.team_id,
            bot_uuid: ctx.params.bot_uuid,
            prompt_token: usage.prompt_tokens,
            eval_token: usage.eval_tokens,
            cost: roundedCost,
          },
          { parentCtx: ctx },
        );
      });

    // Release memory

    try {
      jvm.jsContext.release();
      if (jvm.isolate?.isDisposed) {
        jvm.isolate.dispose();
      }
      jvm.jsContext = undefined;
      jvm.isolate = undefined;
    } catch (err) {
      // Do nothing
    }

    return {
      msg_id: user_msg_id,
      message: threadSession.variables.bot_reply,
      usage: usage,
      thinking: ctx.params.environment === 'dev' ? threadSession.thinking : undefined,
    };
  }

  @Action({})
  async actionGetTopic(
    ctx: Context<CoreAiServiceTypes.ActionParams<'actionGetTopic'>>,
  ): Promise<CoreAiServiceTypes.ActionReturn<'actionGetTopic'>> {
    const message = ctx.params.message;
    const embeddingModel = 'text-embedding-3-small';

    let [mesResponse, ...topicsResponses] = await Promise.all([
      this.broker.call(
        'coreAi.actionGetOpenAiEmbedding',
        {
          texts: [message],
          modelName: embeddingModel,
        },
        { parentCtx: ctx, $cache: false },
      ),
      // Get list of topic with embeddings
      ...Object.keys(ctx.params.topics).map((topic) =>
        this.broker
          .call(
            'coreAi.actionGetOpenAiEmbedding',
            {
              texts: ctx.params.topics[topic],
              modelName: embeddingModel,
            },
            { parentCtx: ctx, $cache: true },
          )
          .then((response) => ({ topic, ...response })),
      ),
    ]);
    let bestMatch = '';
    let highestSimilarity = -1;
    let eval_tokens = mesResponse?.eval_tokens || 0,
      prompt_tokens = mesResponse.prompt_tokens || 0;

    for (let i = 0; i < topicsResponses.length; i++) {
      const topic = topicsResponses[i];

      // Check phrase high similarity
      let highestPhraseSimilarity = -1;
      for (const embedding of topic.embeddings) {
        const similarity = cosineSimilarity(mesResponse.embeddings[0], embedding);
        if (similarity > highestPhraseSimilarity) {
          highestPhraseSimilarity = similarity;
        }
      }

      // Check topic high similarity
      if (highestPhraseSimilarity > highestSimilarity) {
        highestSimilarity = highestPhraseSimilarity;
        bestMatch = topic.topic;
      }

      // Update token count
      eval_tokens += topic.eval_tokens;
      prompt_tokens += topic.prompt_tokens;
    }

    return {
      topic: bestMatch,
      eval_tokens,
      prompt_tokens,
      cost: getCreditFromTokens('gpt', embeddingModel, prompt_tokens, eval_tokens),
    };
  }

  @Action({
    cache: {
      keys: ['modelName', 'texts'],
      ttl: 10000, // in seconds
    },
  })
  async actionGetOpenAiEmbedding(
    ctx: Context<CoreAiServiceTypes.ActionParams<'actionGetOpenAiEmbedding'>>,
  ): Promise<CoreAiServiceTypes.ActionReturn<'actionGetOpenAiEmbedding'>> {
    const texts = ctx.params.texts;
    const modelName = ctx.params.modelName;
    if (this.openai == null) {
      throw new Error('Model not configured yet! Please contact to admin!');
    }
    try {
      const response = await this.openai.embeddings.create(
        {
          model: modelName,
          input: texts,
        },
        {
          timeout: 30000,
        },
      );

      return {
        embeddings: response.data.map((item) => item.embedding),
        eval_tokens: 0,
        prompt_tokens: response.usage.total_tokens,
        cost: getCreditFromTokens('gpt', modelName, response.usage.total_tokens, 0),
      };
    } catch (error) {
      this.broker.logger.error('Error fetching embedding:', error);
      throw error;
    }
  }

  @Action({})
  async actionGetOpenAiChat(
    ctx: Context<CoreAiServiceTypes.ActionParams<'actionGetOpenAiChat'>>,
  ): Promise<CoreAiServiceTypes.ActionReturn<'actionGetOpenAiChat'>> {
    if (!supportedGptModel.includes(ctx.params.modelName)) {
      throw new Error(`Model ${ctx.params.modelName} is not supported!`);
    }

    try {
      const messages: ChatCompletionMessageParam[] = [];
      // Add instruction
      if (ctx.params.instruction) {
        messages.push({
          role: 'system',
          content: ctx.params.instruction,
        });
      }

      // Add chat history
      if (ctx.params.chatHistories) {
        const totalHistory = ctx.params.chatHistories.length;
        for (let i = totalHistory - 1; i >= 0; i--) {
          const chatHistory = ctx.params.chatHistories[i];
          messages.push({
            role: chatHistory.sender === 'assistant' ? 'assistant' : 'user',
            content: chatHistory.message,
          });
        }
      }

      // Add user message
      if (ctx.params.message) {
        messages.push({
          role: 'user',
          content: ctx.params.message,
        });
      }

      const response = await this.openai.chat.completions.create({
        messages: messages,
        model: ctx.params.modelName,
        stream: false,
        max_completion_tokens: ctx.params.maxTokens > 0 ? ctx.params.maxTokens : 4000,
        response_format: { type: ctx.params.json_mode ? 'json_object' : 'text' },
        temperature: ctx.params.temperature ?? 0.7,
      });

      return {
        message: response.choices[0].message.content,
        eval_tokens: response.usage.completion_tokens,
        prompt_tokens: response.usage.prompt_tokens,
        cost: getCreditFromTokens(
          'gpt',
          ctx.params.modelName,
          response.usage.prompt_tokens,
          response.usage.completion_tokens,
        ),
      };
    } catch (error) {
      this.broker.logger.error('Error fetching embedding:', error);
      throw error;
    }
  }

  async respondWithOllama(
    ctx: Context<CoreAiServiceTypes.ActionParams<'respondWithOllama'>>,
  ): Promise<CoreAiServiceTypes.ActionReturn<'respondWithOllama'>> {
    const message = ctx.params.message;
    const response = await axios.post('http://localhost:11434/api/generate', {
      model: 'llama3.1',
      // model: 'deepseek-coder-v2',
      stream: false,
      prompt: message,
    });
    return response.data;
  }

  async configChanged(payload: ConfigServiceTypes.EventParams<'changed'>) {
    payload.forEach((config) => {
      if (config.key === 'config.firebase_admin') {
      }
    });
  }

  created() {
    // this.waitForServices(['api']);

    initError(this.broker);

    // TODO: Should remove token after test finished
    this.openai = new OpenAI({
      apiKey:
        '************************************************************************************************************************************',
    });
  }
}

// Function to calculate cosine similarity between two embeddings
function cosineSimilarity(vecA: number[], vecB: number[]): number {
  let dotProduct = 0;
  let normA = 0;
  let normB = 0;
  for (let i = 0; i < vecA.length; i++) {
    dotProduct += vecA[i] * vecB[i];
    normA += vecA[i] ** 2;
    normB += vecB[i] ** 2;
  }
  return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}

export = CoreAiService;
