'use strict';
import { Action, Service as DService } from 'moleculer-decorators';
import * as MoleculerTs from 'moleculer-ts';
import { OwnActions } from './model768.service.types';
import { Model768ServiceTypes } from '../../types';
import { Service } from 'moleculer';
import { Context } from '../../types/moleculer';
import { AutoTokenizer, FeatureExtractionPipeline, pipeline, PreTrainedTokenizer } from '@huggingface/transformers';

@DService({
  name: Model768ServiceTypes.name,
})
class Model768Service extends Service<{}> implements MoleculerTs.GetServiceOwnActions<OwnActions> {
  embedder: FeatureExtractionPipeline;
  tokenizer: PreTrainedTokenizer;

  @Action({
    params: {
      messages: { type: 'array', items: { type: 'string' } },
    },
  })
  async getEmbedding(
    ctx: Context<Model768ServiceTypes.ActionParams<'getEmbedding'>>,
  ): Promise<Model768ServiceTypes.ActionReturn<'getEmbedding'>> {
    const startTime = Date.now();
    // Dynamically import the transformers library
    if (!this.tokenizer) {
      this.logger.info('Load model to generate embeddings...');

      // Load the tokenizer
      this.tokenizer = await AutoTokenizer.from_pretrained('xnohat/sup-SimCSE-VietNamese-phobert-base-onnx', {
        cache_dir: './model_cache',
      });

      // Load the model
      this.embedder = await pipeline('feature-extraction', 'xnohat/sup-SimCSE-VietNamese-phobert-base-onnx', {
        cache_dir: './model_cache',
      });
    }

    // Tokenize and count tokens
    const tokenCounts = ctx.params.messages.map((message) => {
      const tokens = this.tokenizer.tokenize(message);
      return tokens.length;
    });

    // // Generate embeddings
    // const output = await this.embedder(ctx.params.messages, { pooling: 'mean', normalize: true });
    //
    // // The output is a tensor, so we convert it to a regular array
    // const embeddings: number[][] = Array.from(output.data);
    // Generate embeddings for each message
    const output = await Promise.all(
      ctx.params.messages.map((message) => this.embedder(message, { pooling: 'mean', normalize: true })),
    );

    // Since each output is a number[] for each message, you should now have number[][]
    const embeddings: number[][] = output.map((item) => Array.from(item.data));

    this.logger.info('Embeddings:', embeddings.length, tokenCounts, (Date.now() - startTime) / 1000);
    return { embeddings, tokens: tokenCounts, totalToken: tokenCounts.reduce((a, b) => a + b) };
  }

  created() {}
}

export = Model768Service;
