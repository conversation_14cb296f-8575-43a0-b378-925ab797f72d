import { Action, ConcatMultiple } from 'moleculer-ts';
import { DbBaseMixinActions } from '../../mixins';
import { IUser } from '../../entities/user.entity';
import { ITokenUsage } from '../../entities/tokenUsage.entity';

export const name: 'tokenUsage' = 'tokenUsage';

export type OwnInternalActions = [
  Action<'getMe', { month: number; forceUpdate?: boolean }, ITokenUsage>,
  Action<'getByTeam', { month: number; teamId: string }, ITokenUsage>,
  Action<
    'addTokenUsage',
    { teamId: string; bot_uuid: string; prompt_token: number; eval_token: number; cost: number },
    ITokenUsage
  >,
];

export type OwnActions = ConcatMultiple<[OwnInternalActions, DbBaseMixinActions<IUser>]>;

export type OwnEvents = [];

export type Actions = ConcatMultiple<[OwnActions]>;
export type Events = ConcatMultiple<[OwnEvents]>;
