'use strict';
import { getModelForClass } from '@typegoose/typegoose';
import _ from 'lodash';
import { Action, Service as DService, Method } from 'moleculer-decorators';
import * as MoleculerTs from 'moleculer-ts';
import mongoose from 'mongoose';
import { RequireRoles } from '../../commons/annotation.helper';
import { CommonConfig } from '../../commons/common.config';
import { CommonErrors } from '../../commons/error.helper';
import { ChatRecord, IChatHistory } from '../../entities/chatHistory.entity';
import { USER_ROLE } from '../../entities/user.entity';
import { DbBaseMixin, DbBaseMixinActionTypes } from '../../mixins';
import ConfigMixin from '../../mixins/config.mixin';
import { ChatHistory, ChatHistorySchema, ChatHistoryType } from '../../models/chatHistory';
import {
  ApiGatewayMeta,
  ChatHistoryServiceTypes,
  ConfigServiceTypes,
  MoleculerDBService,
  ObjectIdNull,
} from '../../types';
import { Context } from '../../types/moleculer';
import { convertPaginateOptions, convertPaginateQuery } from '../../utils/mongoPlugins/paginate';
import { OwnActions } from './chatHistory.service.types';

const dbBaseMixin = new DbBaseMixin({
  dbUri: CommonConfig.DB_URI,
  name: 'dbChatHistoryMixin',
  collection: 'chatHistory',
  model: getModelForClass(ChatHistory),
});
const allowUpdateFields = _.pick(ChatHistorySchema, [
  '_id',
  'bot_uuid',
  'thread_id',
  'channel_id',
  'environment',
  'session',
  'histories',
  'isFinished',
  'total',
]);
allowUpdateFields['histories'] = {
  type: 'array',
  optional: true,
  items: {
    type: 'object',
    properties: {
      timestamp: { type: 'number', empty: false, required: true },
      message: { type: 'string', empty: true, required: true },
      sender: { type: 'string', empty: true, required: true },
    },
  },
};
Object.keys(allowUpdateFields).forEach((key) => {
  // @ts-ignore
  allowUpdateFields[key].optional = true;
});
// @ts-ignore
allowUpdateFields['$$strict'] = true;

@DService({
  name: ChatHistoryServiceTypes.name,
  mixins: [ConfigMixin(['chatHistory.**']), dbBaseMixin.getMixin(async (adapter) => {}, [])],
  settings: {
    itemsPerPage: 20, // default items per page. each user or assistant will call 1 item
    fields: [
      '_id',
      'bot_uuid',
      'thread_id',
      'channel_id',
      'environment',
      'session',
      'histories',
      'isFinished',
      'total',
    ],
  },
})
class ChatHistoryService
  extends MoleculerDBService<
    {
      itemsPerPage: number;
      fields: string[];
    },
    ChatHistoryType
  >
  implements Omit<MoleculerTs.GetServiceOwnActions<OwnActions>, DbBaseMixinActionTypes>
{
  @RequireRoles()
  @Action({
    rest: 'POST /list',
    cache: {
      keys: ['#user.teamId', 'search', 'sort', 'page', 'pageSize'],
      ttl: 60 * 30, // 30 minutes
    },
    params: {
      $$strict: true,
      page: { type: 'number', integer: true, min: 1, optional: true, convert: true },
      pageSize: { type: 'number', integer: true, min: 0, optional: true, convert: true },
      sort: { type: 'string', optional: true },
      search: { type: 'string', optional: true },
    },
  })
  async actionList(
    ctx: Context<ChatHistoryServiceTypes.ActionParams<'actionList'>, ApiGatewayMeta>,
  ): Promise<ChatHistoryServiceTypes.ActionReturn<'actionList'>> {
    return this.adapter.model.paginate(
      convertPaginateQuery(ctx.params),
      convertPaginateOptions(
        { ...ctx.params, fields: this.settings.fields },
        this.adapter.model.defaultPaginateOptions,
      ),
    );
  }

  @RequireRoles()
  @Action({
    rest: 'POST /create',
    params: {
      ..._.omit(ChatHistorySchema, ['_id', '$$strict']),
      histories: {
        type: 'array',
        optional: false,
        items: {
          type: 'object',
          properties: {
            timestamp: { type: 'number', empty: false, required: true },
            message: { type: 'string', empty: true, required: true },
            sender: { type: 'string', empty: true, required: true },
          },
        },
      },
    },
    openapi: {
      description: 'Create new chat history.',
    },
  })
  async actionCreate(
    ctx: Context<ChatHistoryServiceTypes.ActionParams<'actionCreate'>, ApiGatewayMeta>,
  ): Promise<ChatHistoryServiceTypes.ActionReturn<'actionCreate'>> {
    const data: IChatHistory = {
      ...ctx.params,
    };
    const result = await this.adapter.insert(data);
    const doc = (await this.transformDocuments(ctx, {}, result)) as IChatHistory;
    this.broker.broadcast('chatHistory.created', doc);
    this.cleanCache(doc._id, ctx.meta.user.teamId.toString());
    return doc;
  }

  @RequireRoles()
  @Action({
    rest: 'PUT /update',
    params: allowUpdateFields,
  })
  async actionUpdate(
    ctx: Context<ChatHistoryServiceTypes.ActionParams<'actionUpdate'>, ApiGatewayMeta>,
  ): Promise<ChatHistoryServiceTypes.ActionReturn<'actionUpdate'>> {
    let data = await this.adapter.model.findOne({ _id: ctx.params._id });
    if (!data) {
      throw new CommonErrors.NotFoundError('Chat history not found');
    }
    data = _.merge(data, ctx.params);
    await data.save();

    const doc = (await this.transformDocuments(ctx, {}, data)) as IChatHistory;
    this.broker.broadcast('chatHistory.updated', doc);
    this.cleanCache(doc._id, ctx.meta.user.teamId.toString());
    return doc;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /get',
    cache: {
      keys: ['#user.teamId', '_id'],
      ttl: 60 * 30, // 30 minutes
    },
    params: {
      $$strict: true,
      _id: { type: 'string', optional: false },
    },
  })
  async actionGet(
    ctx: Context<ChatHistoryServiceTypes.ActionParams<'actionGet'>, ApiGatewayMeta>,
  ): Promise<ChatHistoryServiceTypes.ActionReturn<'actionGet'>> {
    const data = await this.adapter.findOne({ _id: ctx.params._id });
    return (await this.transformDocuments(ctx, {}, data)) as IChatHistory;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /delete',
    params: {
      $$strict: true,
      _id: { type: 'string', optional: false },
    },
  })
  async actionDelete(
    ctx: Context<ChatHistoryServiceTypes.ActionParams<'actionDelete'>, ApiGatewayMeta>,
  ): Promise<ChatHistoryServiceTypes.ActionReturn<'actionDelete'>> {
    const doc = await this.adapter.findOne({ _id: ctx.params._id });
    if (!doc) {
      throw new CommonErrors.NotFoundError('Chat history not found');
    }
    await this.adapter.removeMany({ _id: ctx.params._id });
    this.broker.broadcast('chatHistory.removed', doc);
    this.cleanCache(doc._id, ctx.meta.user.teamId.toString());
    return true;
  }

  // Add the new action here
  @Action({
    params: {
      bot_uuid: { $$t: 'Bot UUID', type: 'string', required: true },
      thread_id: { $$t: 'Thread ID', type: 'string', required: true },
      channel_id: { $$t: 'Channel ID', type: 'string', required: true },
      environment: {
        $$t: 'Environment',
        type: 'string',
        default: 'prod',
        enum: ['dev', 'prod'],
        required: true,
      },
      page: { $$t: 'Page Number', type: 'number', required: true, min: 1 },
    },
  })
  async getPaginatedHistory(
    ctx: Context<ChatHistoryServiceTypes.ActionParams<'getPaginatedHistory'>>,
  ): Promise<ChatHistoryServiceTypes.ActionReturn<'getPaginatedHistory'>> {
    const { bot_uuid, thread_id, channel_id, environment, page } = ctx.params;
    const skip = page - 1; // Subtract 1 from the page to calculate the skip value

    const record = await this.adapter.model
      .findOne({ bot_uuid, thread_id, channel_id, environment, isFinished: true })
      .sort({ created: -1 })
      .skip(skip)
      .exec();

    return record;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /getThreadIdByBotUUID',
    params: {
      bot_uuid: { $$t: 'Bot UUID', type: 'string', required: true },
      channel_id: { $$t: 'Channel ID', type: 'string', optional: true },
      environment: {
        $$t: 'Environment',
        type: 'string',
        optional: true,
        enum: ['dev', 'prod', ''],
      },
      page: { type: 'number', integer: true, min: 1, optional: true, convert: true },
    },
  })
  async actionGetThreadIdByBotUUID(
    ctx: Context<ChatHistoryServiceTypes.ActionParams<'actionGetThreadIdByBotUUID'>, ApiGatewayMeta>,
  ): Promise<ChatHistoryServiceTypes.ActionReturn<'actionGetThreadIdByBotUUID'>> {
    const { bot_uuid, channel_id, environment, page } = ctx.params;
    const query: any = { bot_uuid, isFinished: false };
    if (channel_id) query.channel_id = channel_id;
    if (environment) query.environment = environment;

    const botSetting = await this.broker.call(
      'botSetting.actionLocalGet',
      { _id: bot_uuid, environment: 'dev' },
      { parentCtx: ctx },
    );
    if (!botSetting || botSetting.team_id !== ctx.meta.user.teamId) {
      throw new CommonErrors.NotFoundError('Bot not found');
    }

    const paginatedResult = await this.adapter.model.paginate(
      convertPaginateQuery({ query }),
      convertPaginateOptions(
        {
          ...query,
          fields: [
            'thread_id',
            'updated',
            'channel_id',
            'environment',
            'thread_id',
            'recentMessage',
            'chatMode',
            'session.profile',
          ],
          sort: { updated: -1 },
          page,
        },
        this.adapter.model.defaultPaginateOptions,
      ),
    );

    return {
      ...paginatedResult,
      botName: botSetting.name,
    };
  }

  @RequireRoles()
  @Action({
    rest: 'POST /getSessionThread',
    cache: {
      ttl: 30, // 30s
    },
    params: {
      $$strict: true,
      bot_uuid: { type: 'string', optional: false },
      thread_id: { type: 'string', optional: false },
      channel_id: { type: 'string', optional: false },
      environment: { type: 'string', optional: false },
    },
  })
  async actionGetSessionThread(
    ctx: Context<ChatHistoryServiceTypes.ActionParams<'actionGetSessionThread'>, ApiGatewayMeta>,
  ): Promise<ChatHistoryServiceTypes.ActionReturn<'actionGetSessionThread'>> {
    const { bot_uuid, thread_id, environment } = ctx.params;
    const data = await this.adapter.findOne({ bot_uuid, thread_id, environment, isFinished: false });
    if (!data) {
      this.logger.info(
        `New chat session for bot_uuid: ${bot_uuid} thread_id: ${thread_id} environment: ${environment}`,
      );
    }
    return {
      _id: data?._id ?? '',
      session: data?.session ?? {},
      histories: data?.histories ?? [],
      chatMode: data?.chatMode ?? 0,
    };
  }

  @RequireRoles()
  @Action({
    rest: 'POST /saveSessionHistoryRecord',
    params: {
      $$strict: true,
      bot_uuid: { type: 'string', optional: false },
      thread_id: { type: 'string', optional: false },
      environment: { type: 'string', optional: false },
      channel_id: { type: 'string', optional: false },
      session: { type: 'object', optional: true },
      history: {
        type: 'object',
        optional: true,
        properties: {
          timestamp: { type: 'number', empty: false, required: true },
          message: { type: 'string', empty: true, required: true },
          sender: { type: 'string', empty: true, required: true },
          thinking: {
            type: 'array',
            optional: true,
            items: {
              type: 'object',
              properties: {
                id: { type: 'string', empty: false, required: true },
                type: { type: 'string', empty: false, required: true },
                msg: { type: 'any', empty: false, optional: true }, // any
              },
            },
          },
        },
      },
    },
  })
  async actionSaveSessionHistoryRecord(
    ctx: Context<ChatHistoryServiceTypes.ActionParams<'actionSaveSessionHistoryRecord'>, ApiGatewayMeta>,
  ): Promise<ChatHistoryServiceTypes.ActionReturn<'actionSaveSessionHistoryRecord'>> {
    const { bot_uuid, thread_id, environment, channel_id, session, history } = ctx.params;
    const maxCachingHistory = 10;
    const cacheKey = this.broker.cacher.getCacheKey(
      'chatHistory.actionGetSessionThread',
      {
        bot_uuid,
        thread_id,
        channel_id,
        environment,
      },
      {},
      null,
    );

    // Add message id if not existed
    if (history) {
      if (!history._id) {
        history._id = new mongoose.Types.ObjectId().toString();
      }
    }

    let data = (await this.broker.cacher.get(cacheKey)) as {
      _id: string;
      session: IChatHistory['session'];
      histories: IChatHistory['histories'];
      chatMode: IChatHistory['chatMode'];
    };
    if (!data) {
      const record = await this.adapter.findOne({ bot_uuid, thread_id, environment, channel_id, isFinished: false });
      if (!record) {
        // Instead throw error. Generate start session
        data = {
          _id: '',
          session: {},
          histories: [],
          chatMode: 0,
        };
        this.logger.info(
          `Generate chat session for bot_uuid: ${bot_uuid} thread_id: ${thread_id} environment: ${environment}`,
        );
      } else {
        data = {
          _id: record._id,
          session: record.session,
          histories: record.histories,
          chatMode: record.chatMode,
        };
      }
    }

    // If session null, use previous session. It's for save user message without session
    data.session = session ?? data.session;
    if (history && !data.histories.find((value) => value.timestamp === history.timestamp)) {
      if (data.histories) {
        data.histories.unshift(history);
        const historyLength = data.histories.length;
        if (
          historyLength >= 2 &&
          data.histories[historyLength - 2].timestamp > data.histories[historyLength - 1].timestamp
        ) {
          this.broker.logger.info(
            `History timestamp not valid: ${bot_uuid} thread_id: ${thread_id} environment: ${environment}`,
          );
          // Sort asc by timestamp
          data.histories.sort((a, b) => a.timestamp - b.timestamp);
        }
      } else {
        data.histories = [history];
      }
    }

    if (this.settings.itemsPerPage > 0 && data.histories.length > maxCachingHistory + this.settings.itemsPerPage) {
      // To Prevent history overflow, we only save last itemsPerPage messages
      const oldHistory = data.histories.splice(-this.settings.itemsPerPage);
      // Save old record
      const result = await this.adapter.updateById(
        { _id: data._id },
        {
          histories: oldHistory,
          session: {},
          isFinished: true,
          total: oldHistory.length,
          recentMessage: '', // Save recentMessage as blank
          updated: new Date(),
        },
      );

      // Clean for new record
      data._id = '';
    }

    // Calculate total field
    const total = data.histories.length;
    const recentMessage = data.histories.length > 0 ? data.histories[0].message : '';
    if (data._id) {
      // Update record
      await this.adapter.updateById({ _id: data._id }, { ...data, total, recentMessage, updated: new Date() });
    } else {
      // Save new record
      const newRecord = await this.adapter.insert({
        bot_uuid,
        thread_id,
        environment,
        channel_id,
        session: data.session,
        histories: data.histories,
        chatMode: data.chatMode,
        isFinished: false,
        total,
        recentMessage,
      });
      if (newRecord) {
        data._id = newRecord._id;
      }
    }

    // Save to cache. Only save if _id not null
    if (data._id) {
      this.broker.cacher.set(
        cacheKey,
        { _id: data._id, session: data.session, histories: data.histories, chatMode: data.chatMode },
        15 * 60, // 15 minutes
      );
    } else {
      this.logger.error(`IMPORTANT: Chat history record not saved! ${bot_uuid} ${thread_id} ${environment}`);
    }
    return data;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /getBotChatHistoryByThreadId',
    params: {
      bot_uuid: { $$t: 'Bot UUID', type: 'string', required: true },
      thread_id: { $$t: 'Thread ID', type: 'string', required: false },
      channel_id: { $$t: 'Channel ID', type: 'string', required: false },
      environment: {
        $$t: 'Environment',
        type: 'string',
        default: 'prod',
        enum: ['dev', 'prod'],
        required: true,
      },
      page: { $$t: 'Page Number', type: 'number', required: true, min: 1 },
    },
  })
  async actionGetBotChatHistoryByThreadId(
    ctx: Context<ChatHistoryServiceTypes.ActionParams<'actionGetBotChatHistoryByThreadId'>, ApiGatewayMeta>,
  ): Promise<ChatHistoryServiceTypes.ActionReturn<'actionGetBotChatHistoryByThreadId'>> {
    const { bot_uuid, thread_id, channel_id, environment, page } = ctx.params;
    const showDetail = ctx.meta.user.role >= USER_ROLE.MODERATOR;

    const botSetting = await this.broker.call(
      'botSetting.actionLocalGet',
      { _id: bot_uuid, environment: 'dev' },
      { parentCtx: ctx },
    );
    if (!botSetting || botSetting.team_id !== ctx.meta.user.teamId) {
      throw new CommonErrors.NotFoundError('Bot not found');
    }

    if (page === 1) {
      const record = await this.adapter.model
        .findOne({
          bot_uuid,
          thread_id,
          channel_id,
          environment,
          isFinished: false,
        })
        .exec();
      return {
        histories: record ? record.histories.map((item) => this.mapHistoryFields(item, showDetail)) : [],
        profile: record.session?.profile,
      };
    } else {
      const result = await this.broker.call(
        'chatHistory.getPaginatedHistory',
        {
          ...ctx.params,
          page: page - 1,
        },
        { parentCtx: ctx },
      );
      return { histories: result ? result.histories.map((item) => this.mapHistoryFields(item, showDetail)) : [] };
    }
  }

  @RequireRoles()
  @Action({
    rest: 'POST /changeChatMode',
    params: {
      $$strict: true,
      bot_uuid: { type: 'string', optional: false },
      thread_id: { type: 'string', optional: false },
      channel_id: { type: 'string', optional: false },
      environment: { type: 'string', optional: false },
      chatMode: { type: 'number', enum: [0, 1], optional: false },
    },
  })
  async changeChatMode(
    ctx: Context<ChatHistoryServiceTypes.ActionParams<'changeChatMode'>, ApiGatewayMeta>,
  ): Promise<ChatHistoryServiceTypes.ActionReturn<'changeChatMode'>> {
    const { bot_uuid, thread_id, channel_id, environment, chatMode } = ctx.params;

    // Find and update chat history
    const result = await this.adapter.model.findOneAndUpdate(
      {
        bot_uuid,
        thread_id,
        channel_id,
        environment,
        team_id: ctx.meta.user.teamId,
        isFinished: false,
      },
      {
        $set: {
          chatMode,
          updated: new Date(),
        },
      },
      { new: true },
    );

    if (!result) {
      throw new CommonErrors.NotFoundError('Chat history not found');
    }

    // Clear cache
    const cacheKey = this.broker.cacher.getCacheKey(
      'chatHistory.actionGetSessionThread',
      {
        bot_uuid,
        thread_id,
        channel_id,
        environment,
      },
      {},
      null,
    );
    this.broker.cacher.clean([cacheKey]);

    return { chatMode: result.chatMode };
  }

  @Action({
    rest: 'POST /localChangeChatMode',
    params: {
      $$strict: true,
      bot_uuid: { type: 'string', optional: false },
      thread_id: { type: 'string', optional: false },
      channel_id: { type: 'string', optional: false },
      environment: { type: 'string', optional: false },
      chatMode: { type: 'number', enum: [0, 1], optional: false },
    },
  })
  async localChangeChatMode(
    ctx: Context<ChatHistoryServiceTypes.ActionParams<'localChangeChatMode'>, ApiGatewayMeta>,
  ): Promise<ChatHistoryServiceTypes.ActionReturn<'localChangeChatMode'>> {
    const { bot_uuid, thread_id, channel_id, environment, chatMode } = ctx.params;

    // Find and update chat history
    const result = await this.adapter.model.findOneAndUpdate(
      {
        bot_uuid,
        thread_id,
        channel_id,
        environment,
        isFinished: false,
      },
      {
        $set: {
          chatMode,
          updated: new Date(),
        },
      },
      { new: true },
    );

    if (!result) {
      throw new CommonErrors.NotFoundError('Chat history not found');
    }

    // Clear cache
    const cacheKey = this.broker.cacher.getCacheKey(
      'chatHistory.actionGetSessionThread',
      {
        bot_uuid,
        thread_id,
        channel_id,
        environment,
      },
      {},
      null,
    );
    this.broker.cacher.clean([cacheKey]);

    return { chatMode: result.chatMode };
  }

  private mapHistoryFields(history: ChatRecord, showDetail: boolean): Partial<ChatRecord> {
    return {
      _id: history._id,
      timestamp: history.timestamp,
      message: history.message,
      sender: history.sender,
      thinking: showDetail ? history.thinking : [],
    };
  }

  async configChanged(payload: ConfigServiceTypes.EventParams<'changed'>) {
    console.log('ChatHistory event configChanged', payload);
  }

  @Method
  cleanCache(id: ObjectIdNull, teamId?: string) {
    const meta = { user: { teamId } };

    const cacheKeys = [
      this.broker.cacher.getCacheKey('chatHistory.actionList', { search: '*' }, meta, ['#user.teamId', 'search']),
      this.broker.cacher.getCacheKey('chatHistory.actionGet', { _id: id }, meta, ['#user.teamId', '_id']),
      // No need clean getSessionThread because it live to short
    ];

    this.broker.cacher.clean(cacheKeys);
  }

  created() {
    this.waitForServices(['api']);
  }
}

export = ChatHistoryService;
