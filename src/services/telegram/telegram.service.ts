'use strict';
// import types from custom Moleculer typedef
// import other services types
import {
  ApiGatewayMeta,
  ConfigServiceTypes,
  TeamPackageServiceTypes,
  TelegramServiceTypes,
  UserServiceTypes,
} from '../../types';
// use moleculer-decorators
import axios from 'axios';
import { Action, Event, Method, Service as DService } from 'moleculer-decorators';
import * as MoleculerTs from 'moleculer-ts';
import { Telegraf } from 'telegraf';
import ConfigMixin, { DefaultConfigType } from '../../mixins/config.mixin';
import { TeleGramMessageResult } from '../../types/model.types';
import { Context, eventName, Service } from '../../types/moleculer';
import { OwnActions } from './telegram.service.types';

function convertToTeleGramMessageResult(result: {
  successIds: (number | string)[];
  needDeleted: (number | string)[];
  failedIds: (number | string)[];
}): TeleGramMessageResult {
  return {
    success: result?.successIds?.length,
    failed: result?.failedIds?.length,
  };
}

@DService({
  name: TelegramServiceTypes.name,
  mixins: [ConfigMixin(['telegram.**'])],
})
class TelegramService extends Service<{}> implements MoleculerTs.GetServiceOwnActions<OwnActions> {
  bot: Telegraf;
  config: DefaultConfigType = {};

  @Action({
    params: {
      message: 'string',
    },
  })
  async actionMessageAll(
    ctx: Context<TelegramServiceTypes.ActionParams<'actionMessageAll'>, ApiGatewayMeta>,
  ): Promise<TelegramServiceTypes.ActionReturn<'actionMessageAll'>> {
    const result = await this.sendMessages(this.config['telegram.all_user_ids'], ctx.params.message);
    console.log('result', result);
    if (result.needDeleted?.length > 0) {
      await this.removeInvalidTeleIds(result.needDeleted);
    }
    return convertToTeleGramMessageResult(result);
  }

  @Action({})
  async actionMessageAdmin(
    ctx: Context<TelegramServiceTypes.ActionParams<'actionMessageAdmin'>, ApiGatewayMeta>,
  ): Promise<TelegramServiceTypes.ActionReturn<'actionMessageAdmin'>> {
    const result = await this.sendMessages(this.config['telegram.admin_user_ids'], ctx.params.message);
    if (result.needDeleted?.length > 0) {
      await this.removeInvalidTeleIds(result.needDeleted);
    }
    return convertToTeleGramMessageResult(result);
  }

  @Action({})
  actionMessageUser(
    ctx: Context<TelegramServiceTypes.ActionParams<'actionMessageUser'>, ApiGatewayMeta>,
  ): Promise<TelegramServiceTypes.ActionReturn<'actionMessageUser'>> {
    return this.sendMessage(ctx.params.teleId, ctx.params.message)
      .then((value) => true)
      .catch((reason) => {
        if (reason && reason.code === 400) {
          // Need remove tele id
          this.removeInvalidTeleIds([ctx.params.teleId]);
        }
        return false;
      });
  }

  @Action({})
  async actionSystemMessage(
    ctx: Context<TelegramServiceTypes.ActionParams<'actionSystemMessage'>, ApiGatewayMeta>,
  ): Promise<TelegramServiceTypes.ActionReturn<'actionSystemMessage'>> {
    const message = `${ctx.params.message}`;

    if (process.env.TELEGRAM_SYSTEM_TOKEN && process.env.TELEGRAM_SYSTEM_CHAT_ID) {
      const url = `https://api.telegram.org/bot${process.env.TELEGRAM_SYSTEM_TOKEN}/sendMessage`;

      try {
        const fullMessage = ctx.params.metadata
          ? `${message}\n\nMetadata:\n${JSON.stringify(ctx.params.metadata, null, 2)}`
          : message;

        await axios.post(url, {
          chat_id: process.env.TELEGRAM_SYSTEM_CHAT_ID,
          text: fullMessage,
          // parse_mode: 'Markdown',
        });
      } catch (error) {
        this.broker.logger.error('Error sending system message:', error);
      }
    } else {
      this.broker.logger.warn('TELEGRAM_SYSTEM_TOKEN or TELEGRAM_SYSTEM_CHAT_ID not set!');
    }
  }

  @Method
  async removeInvalidTeleIds(deletedIds: (number | string)[]) {
    await this.broker.call('config.actionSet', {
      rules: [
        {
          key: 'telegram.all_user_ids',
          value: this.config?.['telegram.all_user_ids'].filter((value2) => !deletedIds.includes(value2)),
        },
        {
          key: 'telegram.admin_user_ids',
          value: this.config?.['telegram.admin_user_ids'].filter((value2) => !deletedIds.includes(value2)),
        },
      ],
    });
  }

  @Method
  async sendMessages(chatIds: (number | string)[], message: string) {
    const needDeleted: (number | string)[] = [];
    return Promise.all(
      chatIds.map((value) =>
        this.sendMessage(value, message)
          .then((value1) => value)
          .catch((reason) => {
            if (reason && reason.code === 400) {
              needDeleted.push(value);
            } else {
              this.broker.logger.error('Error on telegram send message', reason);
            }
            return null;
          }),
      ),
    ).then((successIds) => {
      successIds = successIds.filter((value) => value !== null);
      return {
        successIds: successIds,
        failedIds: chatIds.filter((value) => !successIds.includes(value)),
        needDeleted,
      };
    });
  }

  @Method
  async sendMessage(chatId: number | string, message: string) {
    if (this.bot) {
      return this.bot.telegram?.sendMessage(chatId, message);
    }
  }

  @Method
  async startBot(): Promise<boolean> {
    // Stop last bot
    try {
      if (this.bot) {
        this.bot.stop();
      }
    } catch (e) {
      this.broker.logger.error(e);
    }
    this.bot = undefined;

    try {
      // See more https://github.com/telegraf/telegraf
      if (!this.config?.['telegram.bot_token']) {
        return false;
      }

      this.bot = new Telegraf(this.config?.['telegram.bot_token']);
      this.bot.command('quit', (ctx) => {
        this.broker.call('config.actionSet', {
          rules: [
            {
              key: 'telegram.all_user_ids',
              value: this.config?.['telegram.all_user_ids'].filter((value) => value != ctx.message.chat.id),
            },
          ],
        });

        ctx.telegram.sendMessage(ctx.message.chat.id, 'Bạn đã tắt nhân tin nhắn!');

        // Only leave chat not type private chat
        if (ctx.message.chat.type !== 'private') {
          // Explicit usage
          ctx.telegram.leaveChat(ctx.message.chat.id);

          // Using context shortcut
          ctx.leaveChat();
        }
      });

      this.bot.command('start', (ctx) => {
        if (!this.config?.['telegram.all_user_ids']?.includes(ctx.message.chat.id)) {
          // Add new user id to list register user
          this.config?.['telegram.all_user_ids'].push(ctx.message.chat.id);
          this.broker.call('config.actionSet', {
            rules: [{ key: 'telegram.all_user_ids', value: this.config?.['telegram.all_user_ids'] }],
          });

          this.bot.telegram.sendMessage(ctx.message.chat.id, 'Bạn đã đăng ký nhận thông tin!');
        } else {
          this.bot.telegram.sendMessage(ctx.message.chat.id, 'Bạn đã được nhận thông tin!');
        }
      });

      this.bot.on('text', (ctx) => {
        this.broker.logger.info(`Message from "${ctx.message.from.username}":`, ctx.message.text);
        // Explicit usage
        ctx.telegram.sendMessage(
          ctx.message.chat.id,
          `Hello ${ctx.message.from.first_name} ${ctx.message.from.last_name}`,
        );

        // Using context shortcut
        // ctx.reply(`Hello ${ctx.state.role}`);
      });

      this.bot.on('callback_query', (ctx) => {
        // Explicit usage
        ctx.telegram.answerCbQuery(ctx.callbackQuery.id);

        // Using context shortcut
        ctx.answerCbQuery();
      });

      this.bot.on('inline_query', (ctx) => {
        const result: any[] = [];
        // Explicit usage
        ctx.telegram.answerInlineQuery(ctx.inlineQuery.id, result);

        // Using context shortcut
        ctx.answerInlineQuery(result);
      });

      await this.bot.launch();
      return true;
    } catch (e) {
      this.broker.logger.error('Error when start telegram bot!', e);
    }
    return false;
  }

  async configChanged(payload: ConfigServiceTypes.EventParams<'changed'>) {
    let needReloadBot = false;
    payload.forEach((config) => {
      if (config.key === 'telegram.bot_token') {
        needReloadBot = true;
      }
    });
    if (needReloadBot) {
      await this.startBot();
    }
  }

  @Event({
    name: eventName('user.created'),
  })
  async onUserCreated(user: UserServiceTypes.EventParams<'created'>) {
    const message = `Nhà có khách mới nè mọi người ơi: #${user._id} ${user.username}(${user.name})`;

    await this.broker.call('telegram.actionSystemMessage', {
      message,
    });
  }

  @Event({
    name: eventName('teamPackage.assigned'),
  })
  async onTeamPackageAssigned(payload: TeamPackageServiceTypes.EventParams<'assigned'>) {
    const startDate = payload.teamPackage.startDate?.toISOString().split('T')[0] || 'N/A';
    const expirationDate = payload.teamPackage.expirationDate?.toISOString().split('T')[0] || 'N/A';
    const message = `🎉 Team #${payload.teamPackage.teamId?.toString()} vừa được đăng ký gói ${
      payload.teamPackage.packageName
    } nè!\n📅 Bắt đầu: ${startDate} -> ⏳ Hết hạn: ${expirationDate}`;

    await this.broker.call('telegram.actionSystemMessage', {
      message,
    });
  }

  async started() {
    await this.startBot();
  }
}

export = TelegramService;
