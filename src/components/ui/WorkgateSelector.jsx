import React, { useState, useEffect } from 'react';
import { useTranslation } from "react-i18next";
import { Icon } from '@iconify/react';
import { useLazyGetIntegrationListQuery } from "@/store/api/project/integrationSlice";

const WorkgateSelector = ({ onSelect, selectedWorkgate, teamId, workgateType = 6 }) => {
    const { t } = useTranslation();
    const [isOpen, setIsOpen] = useState(false);
    const [workgateData, setWorkgateData] = useState([]);
    const [getIntegrationList, { isLoading, error }] = useLazyGetIntegrationListQuery();

    // Fetch workgates on component mount
    useEffect(() => {
        const fetchWorkgates = async () => {
            try {
                const result = await getIntegrationList({
                    page: 1,
                    pageSize: 100,
                    sort: "",
                    search: "",
                    bot_uuid: "",
                }).unwrap();

                if (result && Array.isArray(result.rows)) {
                    setWorkgateData(result.rows);
                }
            } catch (err) {
                console.error('Error fetching workgates:', err);
            }
        };

        fetchWorkgates();
    }, [getIntegrationList]);

    // Filter for Zalo QR workgates (type 6)
    const zaloQrWorkgates = workgateData.filter(workgate => workgate.type === workgateType) || [];

    const handleSelect = (workgate) => {
        onSelect(workgate);
        setIsOpen(false);
    };

    const handleClear = () => {
        onSelect(null);
        setIsOpen(false);
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center p-4 border rounded-md bg-gray-50">
                <Icon icon="line-md:loading-loop" className="w-5 h-5 mr-2" />
                <span className="text-sm text-gray-600">{t('workgate.loading_workgates')}</span>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex items-center p-4 border rounded-md bg-red-50 border-red-200">
                <Icon icon="material-symbols:error" className="w-5 h-5 mr-2 text-red-500" />
                <span className="text-sm text-red-600">{t('workgate.error_loading_workgates')}</span>
            </div>
        );
    }

    return (
        <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('workgate.select_existing_workgate')}
            </label>

            <div className="relative">
                <button
                    type="button"
                    onClick={() => setIsOpen(!isOpen)}
                    className="w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-md bg-white text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                    <span className="flex items-center">
                        {selectedWorkgate ? (
                            <>
                                <Icon icon="material-symbols:check-circle" className="w-4 h-4 mr-2 text-green-500" />
                                <span className="text-sm">{selectedWorkgate.description}</span>
                            </>
                        ) : (
                            <>
                                <Icon icon="material-symbols:add-circle-outline" className="w-4 h-4 mr-2 text-gray-400" />
                                <span className="text-sm text-gray-500">{t('workgate.select_workgate_placeholder')}</span>
                            </>
                        )}
                    </span>
                    <Icon
                        icon={isOpen ? "material-symbols:keyboard-arrow-up" : "material-symbols:keyboard-arrow-down"}
                        className="w-5 h-5 text-gray-400"
                    />
                </button>

                {isOpen && (
                    <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                        {zaloQrWorkgates.length === 0 ? (
                            <div className="px-3 py-2 text-sm text-gray-500">
                                {t('workgate.no_existing_workgates')}
                            </div>
                        ) : (
                            <>
                                {selectedWorkgate && (
                                    <button
                                        type="button"
                                        onClick={handleClear}
                                        className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center border-b border-gray-100"
                                    >
                                        <Icon icon="material-symbols:close" className="w-4 h-4 mr-2 text-red-500" />
                                        <span className="text-red-600">{t('workgate.clear_selection')}</span>
                                    </button>
                                )}
                                {zaloQrWorkgates.map((workgate) => (
                                    <button
                                        key={workgate._id}
                                        type="button"
                                        onClick={() => handleSelect(workgate)}
                                        className={`w-full px-3 py-2 text-left text-sm hover:bg-blue-50 flex items-center justify-between ${
                                            selectedWorkgate?._id === workgate._id ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                                        }`}
                                    >
                                        <div className="flex items-center">
                                            <Icon
                                                icon="material-symbols:account-circle"
                                                className="w-4 h-4 mr-2 text-blue-500"
                                            />
                                            <div>
                                                <div className="font-medium">{workgate.description}</div>
                                                <div className="text-xs text-gray-500">
                                                    ID: {workgate._id}
                                                </div>
                                            </div>
                                        </div>
                                        {selectedWorkgate?._id === workgate._id && (
                                            <Icon icon="material-symbols:check" className="w-4 h-4 text-blue-500" />
                                        )}
                                    </button>
                                ))}
                            </>
                        )}
                    </div>
                )}
            </div>

            {selectedWorkgate && (
                <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="flex items-center text-sm text-blue-700">
                        <Icon icon="material-symbols:info" className="w-4 h-4 mr-2" />
                        <span>{t('workgate.relink_mode_info')}</span>
                    </div>
                </div>
            )}
        </div>
    );
};

export default WorkgateSelector;
