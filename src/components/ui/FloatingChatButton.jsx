import React, { useState } from "react";

const FloatingChatButton = ({
    onClick,
    position = {},
    colors = ["#B1C29E", "#D4EBF8"],
    theme = "light",
    botName = "Chatbot",
    logo = "",
    isSliding = false,
}) => {
    return (
        <div
            className={`messenger-popup ${isSliding ? "hide" : ""}`}
            style={{
                backgroundColor: colors[0],
                color: colors[1],
                [position.align]: position.side_spacing,
                bottom: position.bottom_spacing,
            }}
            onClick={onClick}
        >
            <img src={logo} alt="logo" className="chatbot-logo" />
        </div>
    )
}
export default FloatingChatButton;