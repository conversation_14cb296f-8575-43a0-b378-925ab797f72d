import { useState } from 'react';
import Hint from './Hint';

interface Option {
  name: string;
  value: string;
}

interface NodeTextDropdownProps {
  title: string;
  value: string;
  options: string[] | Option[];
  hint?: string;
  onChangeText: (key: string, value: string) => void;
}

export default function NodeTextDropdown({ title, value, options, hint, onChangeText }: NodeTextDropdownProps) {
  const [selectedValue, setSelectedValue] = useState(value);

  const handleChange = (newValue: string) => {
    setSelectedValue(newValue);
    onChangeText(title, newValue);
  };

  const isObjectOptions = options.length > 0 && typeof options[0] === 'object';

  return (
    <div className="space-y-1">
      <div className="flex items-center">
        <span className="font-semibold mr-2">{title}:</span>
        {hint && <Hint hint={hint} />}
      </div>
      <select
        value={selectedValue}
        onChange={(e) => handleChange(e.target.value)}
        className="w-full p-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
        {isObjectOptions 
          ? (options as Option[]).map((option) => (
              <option key={option.value} value={option.value}>
                {option.name}
              </option>
            ))
          : (options as string[]).map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
      </select>
    </div>
  );
}
