import Hint from './Hint';

interface NodeNumberProps {
  title: string;
  value: number;
  hint?: string;
  onChange: (newValue: number) => void;
  onDelete?: () => void;
}

export default function NodeNumber({ title, value, onChange, onDelete, hint }: NodeNumberProps) {
  return (
    <div className="space-y-2">
      <div className="flex items-center">
        {onDelete && (
          <button type="button" className="text-red-500 hover:text-red-600 p-1" onClick={onDelete}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-4 w-4">
              <path d="M3 6h18" />
              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
            </svg>
          </button>
        )}
        <span className="font-medium">{title}:</span>
        {hint && <Hint hint={hint} />}
        <input
          type="number"
          value={value}
          onChange={(e) => onChange(parseFloat(e.target.value))}
          className="w-24 rounded-md border border-gray-200 p-1 text-sm ml-2"
        />
      </div>
    </div>
  );
}
