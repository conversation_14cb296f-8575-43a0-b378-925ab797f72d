import { BotFacebookPostProperty } from '@/nodes/flow.types';
import { BasicNode } from '@/nodes/types.ts';
import { ExplainView } from './ExplainView';
import NodeTextArea from './NodeTextArea';
import NodeTextInput from './NodeTextInput';
import NodeArrayText from '@/components/nodeBlock/NodeArrayText.tsx';

interface NodeFacebookPostProps {
  selectedNode: BasicNode;
  onChangeProperty: (key: string, value: any) => void;
  onOpenPopup: (key: string, value: string) => void;
  onDeleteProperty?: (key: string) => void;
}

export default function NodeFacebookPost({ selectedNode, onOpenPopup, onChangeProperty }: NodeFacebookPostProps) {
  const properties = selectedNode.data.properties as BotFacebookPostProperty;

  return (
    <>
      <ExplainView>
        This block allows posting content to a Facebook page.
        <br />
        You can specify the message text, images, link URL, and scheduled publish time.
        <br />
        Use this for integrating Facebook posting into your workflow.
      </ExplainView>
      <NodeTextInput
        title="workgateId"
        value={properties.workgateId}
        hint="Workgate ID that contains Facebook page credentials"
        onChangeText={onChangeProperty}
      />
      <NodeTextArea
        title="message"
        value={properties.message}
        hint="Message text to post - can contain JS variables (${var}) and template strings"
        onOpenPopup={() => onOpenPopup('message', properties.message)}
        onChangeText={onChangeProperty}
      />
      <NodeArrayText
        title="images"
        value={properties.images as string[]}
        hint="Optional images to include (URLs, media IDs, or base64) - can be a single string or comma-separated list"
        onChange={(values) => {
          onChangeProperty('images', values);
        }}
      />
      <NodeTextInput
        title="linkUrl"
        value={properties.linkUrl || ''}
        hint="Optional link URL to include with the post - can be a JS variable (${var}) or string"
        onChangeText={onChangeProperty}
      />
      <NodeTextInput
        title="scheduledPublishTime"
        value={(properties.scheduledPublishTime as string) || ''}
        hint="Optional scheduled publish time (Unix timestamp in seconds) - leave empty for immediate posting"
        onChangeText={onChangeProperty}
      />
      <NodeTextInput
        title="varName"
        value={properties.varName || 'facebook_post_result'}
        hint="Variable name to store the result of the Facebook post operation"
        onChangeText={onChangeProperty}
      />
    </>
  );
}
