import NodeArrayText from './NodeArrayText';
import NodeBoolean from './NodeBoolean';
import NodeNumber from './NodeNumber';
import NodeObjectText from './NodeObjectText';
import NodeTextArea from './NodeTextArea';

interface PropertyRendererProps {
  propKey: string;
  value: any;
  hint?: string;
  onChangeProperty: (key: string, value: any) => void;
  onOpenPopup: (key: string, value: string) => void;
  onDeleteProperty?: () => void;
}

export default function PropertyRenderer({
  propKey,
  value,
  hint,
  onChangeProperty,
  onOpenPopup,
  onDeleteProperty,
}: PropertyRendererProps) {
  if (typeof value === 'string') {
    return (
      <div className="space-y-2">
        <NodeTextArea
          title={propKey}
          value={value}
          hint={hint}
          onOpenPopup={() => {
            onOpenPopup(propKey, value);
          }}
          onChangeText={onChangeProperty}
          onDelete={onDeleteProperty}
        />
      </div>
    );
  }

  if (typeof value === 'number') {
    return (
      <NodeNumber
        title={propKey}
        value={value}
        onChange={(newValue) => onChangeProperty(propKey, newValue)}
        onDelete={onDeleteProperty}
      />
    );
  }

  if (Array.isArray(value)) {
    return (
      <div className="space-y-2">
        {value.every((item) => typeof item === 'string') ? (
          <NodeArrayText
            title={propKey}
            value={value}
            hint={'Just sample for view'}
            onChange={(newValue) => onChangeProperty(propKey, newValue)}
            onDelete={onDeleteProperty}
          />
        ) : (
          <>
            <div className="font-semibold">{propKey}</div>
            <p className="w-full min-h-[100px] overflow-auto rounded-md border border-gray-200 p-2 text-sm bg-gray-100 whitespace-pre-wrap">
              {JSON.stringify(value, null, 2)}
            </p>
          </>
        )}
      </div>
    );
  }

  if (typeof value === 'object' && !Array.isArray(value)) {
    return (
      <div className="space-y-2">
        <NodeObjectText
          title={propKey}
          value={value}
          onChange={(newValue) => onChangeProperty(propKey, newValue)}
          onDelete={onDeleteProperty}
        />
      </div>
    );
  }

  if (typeof value === 'boolean') {
    return <NodeBoolean title={propKey} value={value} onChange={(newValue) => onChangeProperty(propKey, newValue)} />;
  }

  return (
    <div className="space-y-2">
      <div>
        <b>{propKey}</b> : {value}
      </div>
    </div>
  );
}
