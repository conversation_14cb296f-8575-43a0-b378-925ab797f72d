import { Button } from '../ui/button';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '../ui/hover-card';

interface NodeArrayTextProps {
  title: string;
  value: string[];
  hint?: string;
  onChange: (newValue: string[]) => void;
  onDelete?: () => void;
}

export default function NodeArrayText({ title, value, hint, onChange, onDelete }: NodeArrayTextProps) {
  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        {onDelete && (
          <button type="button" className="text-red-500 hover:text-red-600 p-1" onClick={onDelete}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-4 w-4">
              <path d="M3 6h18" />
              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
            </svg>
          </button>
        )}
        <div className="flex items-center gap-2 flex-1">
          <span className="font-semibold">{title}:</span>
          {hint && (
            <HoverCard>
              <HoverCardTrigger>
                <Button variant="ghost" size="icon" className="h-4 w-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4">
                    <circle cx="12" cy="12" r="10" />
                    <path d="M12 16v-4" />
                    <path d="M12 8h.01" />
                  </svg>
                </Button>
              </HoverCardTrigger>
              <HoverCardContent className="w-80">
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">{hint}</p>
                </div>
              </HoverCardContent>
            </HoverCard>
          )}
        </div>
        <Button variant="ghost" size="sm" onClick={() => onChange([...value, ''])}>
          Add
        </Button>
      </div>
      <div className="space-y-2">
        {value.map((item, index) => (
          <div key={index} className="flex items-center gap-2">
            <input
              type="text"
              value={item}
              onChange={(e) => {
                const newArray = [...value];
                newArray[index] = e.target.value;
                onChange(newArray);
              }}
              className="flex-1 rounded-md border border-gray-200 p-1 text-sm"
            />
            <button
              type="button"
              className="text-red-500 hover:text-red-600 p-1"
              onClick={() => {
                const newArray = value.filter((_, i) => i !== index);
                onChange(newArray);
              }}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4">
                <path d="M3 6h18" />
                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
              </svg>
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
