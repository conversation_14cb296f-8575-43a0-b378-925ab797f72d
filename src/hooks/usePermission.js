import {useSelector} from "react-redux";
import {useEffect, useState} from "react";

/**
 * Create function to check user permission
 * Ex: const hasPermission = usePermission(undefined, 1); // Only check min role
 * Ex: const hasPermission = usePermission('#view.config:view'); // Check permission include '#view.config:view'
 * @param {string | undefined} permission 
 * @param {number | undefined} minRole 
 * @returns boolean
 */
const usePermission = (permission, minRole = undefined) => {
  const [hasPermission, setHasPermission] = useState(true);
  const user = useSelector((state) => state.auth.user);
  useEffect(() => {
    let perm = true;
    if (minRole) {
      perm = user?.role >= minRole;
    }
    if (perm && permission) {
      perm = user?.permissions?.includes(permission);
    }
    setHasPermission(perm);
  }, [permission, minRole, user, setHasPermission]);
  return hasPermission;
};

export default usePermission;
