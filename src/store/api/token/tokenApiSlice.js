import { apiSlice } from "../apiSlice";

export const botSettingApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getMe: builder.query({
      query: (data) => ({
        url: "tokenUsage/getMe",
        method: "POST",
        body: {
          // month format 202412
          month: data.month,
        },
      }),
    }),
    reportUsage: builder.query({
      query: (data) => ({
        url: "tokenUsageDaily/reportUsage",
        method: "POST",
        body: {
          // start_date, end_date format 20241101, 20241201
          bot_uuid: data.bot_uuid,
          start_date: data.start_date,
          end_date: data.end_date,
        },
      }),
    }),
  }),
});
export const {
  useLazyGetMeQuery,
  useLazyReportUsageQuery,
} = botSettingApi;
