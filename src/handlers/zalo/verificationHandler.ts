'use strict';
import * as http from 'http';

/**
 * Handles Zalo OA verification
 * Zalo requires a specific HTML file with a meta tag for verification
 */
export function handleZaloVerification(
  req: http.IncomingMessage,
  res: http.ServerResponse,
): void {
  // The verification code should be configured based on your Zalo OA settings
  const verificationCode = 'RVQ2A8Jo119_q98uYUKU9rdIXHkrf5H7DpWm';
  
  res.writeHead(200, { 'Content-Type': 'text/html' });
  res.end(`<!DOCTYPE html>
<html lang="en">
<head>
    <meta property="zalo-platform-site-verification" content="${verificationCode}" />
</head>
<body>
There Is No Limit To What You Can Accomplish Using Zalo!
</body>
</html>`);
}
