import { createSlice } from "@reduxjs/toolkit";
import { v4 as uuidv4 } from "uuid";

import { toast } from "react-toastify";
import avatar1 from "@/assets/images/avatar/av-1.svg";
import avatar2 from "@/assets/images/avatar/av-2.svg";
import avatar3 from "@/assets/images/avatar/av-3.svg";
import avatar4 from "@/assets/images/avatar/av-4.svg";

export const appProjectSlice = createSlice({
  name: "approject",
  initialState: {
    openProjectModal: false,
    isLoading: null,
    editItem: {},
    editModal: false,
    projects: [],
    refetch: 0,
    bot_type: "chatbot",
  },
  reducers: {
    toggleAddModal: (state, action) => {
      state.openProjectModal = action.payload.open;
      if(action.payload.open) {
        state.bot_type = action.payload.bot_type;
      }
    },
    toggleEditModal: (state, action) => {
      state.editModal = action.payload;
    },
    pushProject: (state, action) => {
      state.projects = [...[action.payload],...state.projects]
    },
    setProject: (state, action) => {
      state.projects = action.payload;
    },
    removeProject: (state, action) => {
      state.projects = state.projects.filter(
        (item) => item.id !== action.payload
      );
    },
    updateProject: (state, action) => {
      // update project and  store it into editItem when click edit button

      state.editItem = {...action.payload, config: JSON.stringify(action.payload.config)};

      // toggle edit modal
      state.editModal = !state.editModal;
      // find index
      let index = state.projects.findIndex(
        (item) => item._id === action.payload._id
      );
      // update project
      state.projects.splice(index, 1, {
        _id: action.payload._id,
        name: action.payload.name,
        desc: action.payload.desc,
        copyright: action.payload.copyright,
      });
    },
    setEditItem: (state, action) => {
      state.editItem = action.payload;
    },
    setRefetch: (state) => {
      state.refetch += 1;
    },
    setBotType: (state, action) => {
      state.bot_type = action.payload;
    }
  },
});

export const {
  openModal,
  pushProject,
  toggleAddModal,
  removeProject,
  toggleEditModal,
  updateProject,
  setProject,
  setRefetch,
  setBotType,
} = appProjectSlice.actions;
export default appProjectSlice.reducer;
