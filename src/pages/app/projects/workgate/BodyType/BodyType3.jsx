import React from 'react';
import Icon from "@/components/ui/Icon";

const BodyType3 = () => {
    return (
        <div className="flex flex-col items-center justify-center min-h-[400px] h-full p-8 bg-slate-50 dark:bg-slate-800 rounded-lg">
            <div className="relative">
                <div className="animate-spin">
                    <Icon
                        icon="heroicons-outline:cog-6-tooth"
                        className="w-16 h-16 text-primary-500"
                    />
                </div>
                <div className="absolute -top-2 -right-2">
                    <div className="animate-spin-slow">
                        <Icon
                            icon="heroicons-outline:cog"
                            className="w-7 h-7 text-success-500"
                        />
                    </div>
                </div>
            </div>

            <h2 className="text-2xl font-bold text-slate-900 dark:text-white mt-6 text-center">
                Chức năng đang phát triển
            </h2>

            <div className="flex items-center gap-2 mt-6 text-primary-500 animate-pulse">
                <Icon icon="heroicons-outline:clock" className="w-5 h-5" />
                <span className="text-sm font-medium">Coming Soon</span>
            </div>
        </div>
    );
};

export default BodyType3;
