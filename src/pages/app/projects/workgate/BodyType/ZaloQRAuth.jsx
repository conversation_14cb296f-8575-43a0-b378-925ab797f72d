import React, { useState, useEffect, useRef } from 'react';
import { Icon } from '@iconify/react';
import { useTranslation } from "react-i18next";
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { ROOT_URL } from '@/store/api/apiSlice';
import zalo from "@/assets/images/platform/zalo.png";

const ZaloQRAuth = ({ onAuthSuccess, onAuthError, workgateId = null, isRelink = false }) => {
    const { t } = useTranslation();
    const { token } = useSelector((state) => state.auth);
    const [qrCodeUrl, setQrCodeUrl] = useState(null);
    const [authStatus, setAuthStatus] = useState('idle'); // idle, connecting, waiting, scanned, success, error
    const [statusMessage, setStatusMessage] = useState('');
    const [userInfo, setUserInfo] = useState(null);
    const eventSourceRef = useRef(null);

    const statusColors = {
        idle: 'text-gray-600',
        connecting: 'text-blue-600',
        waiting: 'text-orange-600',
        scanned: 'text-purple-600',
        success: 'text-green-600',
        error: 'text-red-600'
    };

    const statusIcons = {
        idle: 'material-symbols:qr-code-2',
        connecting: 'line-md:loading-loop',
        waiting: 'material-symbols:qr-code-scanner',
        scanned: 'material-symbols:phone-android',
        success: 'material-symbols:check-circle',
        error: 'material-symbols:error'
    };

    const initiateQRLogin = () => {
        if (!token) {
            setAuthStatus('error');
            setStatusMessage(t('workgate.auth_token_not_found'));
            toast.error(t('workgate.auth_token_not_found'));
            return;
        }

        setAuthStatus('connecting');
        setStatusMessage(t('workgate.connecting_to_zalo_service'));
        setQrCodeUrl(null);
        setUserInfo(null);

        // Close existing EventSource if any
        if (eventSourceRef.current) {
            eventSourceRef.current.close();
        }

        // Use the user's JWT token as the session token
        const sessionToken = token;

        // Construct the full URL using ROOT_URL from apiSlice
        let sseUrl = `${ROOT_URL}zalo/auth/loginQr?token=${sessionToken}`;

        // Add workgate_id parameter if provided (for re-link functionality)
        if (workgateId) {
            sseUrl += `&workgate_id=${workgateId}`;
        }

        // Start SSE connection
        eventSourceRef.current = new EventSource(sseUrl);

        eventSourceRef.current.addEventListener('connected', function(event) {
            console.log('Connected to Zalo login service');
            setAuthStatus('waiting');
            setStatusMessage(t('workgate.generating_qr_code'));
        });

        eventSourceRef.current.addEventListener('qr', function(event) {
            try {
                const data = JSON.parse(event.data);
                console.log('QR code received');

                // Handle both base64 and data URL formats
                const qrUrl = data.data.startsWith('data:image')
                    ? data.data
                    : `data:image/png;base64,${data.data}`;

                setQrCodeUrl(qrUrl);
                setAuthStatus('waiting');
                setStatusMessage(t('workgate.scan_qr_with_zalo'));
            } catch (error) {
                console.error('Error parsing QR data:', error);
                setAuthStatus('error');
                setStatusMessage(t('workgate.error_generating_qr'));
            }
        });

        eventSourceRef.current.addEventListener('qr_scanned', function(event) {
            try {
                const data = JSON.parse(event.data);
                console.log('QR code scanned');
                setAuthStatus('scanned');
                setStatusMessage(`${t('workgate.qr_scanned_by')}: ${data.displayName || t('workgate.user')}`);
                setUserInfo({ displayName: data.displayName });
            } catch (error) {
                console.error('Error parsing scan data:', error);
            }
        });

        eventSourceRef.current.addEventListener('auth_success', function(event) {
            try {
                const data = JSON.parse(event.data);
                console.log('Zalo login successful', data);
                setAuthStatus('success');
                setStatusMessage(t('workgate.login_successful'));

                // Close the connection
                if (eventSourceRef.current) {
                    eventSourceRef.current.close();
                    eventSourceRef.current = null;
                }

                // Show appropriate success message based on mode
                const successMessage = data.isRelink
                    ? t('workgate.zalo_relink_successful')
                    : t('workgate.zalo_auth_successful');
                toast.success(successMessage);

                // Call success callback if provided
                if (onAuthSuccess) {
                    onAuthSuccess(data);
                }
            } catch (error) {
                console.error('Error parsing success data:', error);
                setAuthStatus('error');
                setStatusMessage(t('workgate.error_processing_auth'));
            }
        });

        eventSourceRef.current.addEventListener('error', function(event) {
            try {
                const data = JSON.parse(event.data);
                console.error('Zalo login error:', data);
                setAuthStatus('error');
                setStatusMessage(`${t('workgate.auth_error')}: ${data.message || t('workgate.unknown_error')}`);

                toast.error(data.message || t('workgate.auth_failed'));

                if (onAuthError) {
                    onAuthError(data);
                }
            } catch (error) {
                console.error('Error parsing error data:', error);
                setAuthStatus('error');
                setStatusMessage(t('workgate.auth_failed'));
            }
        });

        eventSourceRef.current.onerror = function(error) {
            console.error('SSE connection error:', error);
            setAuthStatus('error');
            setStatusMessage(t('workgate.connection_error_retry'));

            if (eventSourceRef.current) {
                eventSourceRef.current.close();
                eventSourceRef.current = null;
            }

            toast.error(t('workgate.connection_error'));
        };
    };

    const resetAuth = () => {
        if (eventSourceRef.current) {
            eventSourceRef.current.close();
            eventSourceRef.current = null;
        }

        setAuthStatus('idle');
        setStatusMessage('');
        setQrCodeUrl(null);
        setUserInfo(null);
    };

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (eventSourceRef.current) {
                eventSourceRef.current.close();
            }
        };
    }, []);

    const renderQRContent = () => {
        if (authStatus === 'success') {
            return (
                <div className="text-center py-8">
                    <div className="mb-4">
                        <Icon
                            icon="material-symbols:check-circle"
                            className="text-6xl text-green-500 mx-auto"
                        />
                    </div>
                    <h3 className="text-xl font-semibold text-green-600 mb-2">
                        {t('workgate.auth_successful')}
                    </h3>
                    {userInfo && (
                        <p className="text-gray-600">
                            {t('workgate.welcome')}, {userInfo.displayName}
                        </p>
                    )}
                </div>
            );
        }

        if (qrCodeUrl) {
            return (
                <div className="text-center">
                    <img
                        src={qrCodeUrl}
                        alt={t('workgate.zalo_login_qr_code')}
                        className="max-w-[250px] max-h-[250px] mx-auto mb-4 border rounded-lg shadow-sm"
                    />
                    <p className="text-sm text-gray-600">
                        {t('workgate.open_zalo_scan_qr')}
                    </p>
                </div>
            );
        }

        return (
            <div className="text-center py-8">
                <div className="mb-4">
                    <Icon
                        icon={statusIcons[authStatus]}
                        className={`text-4xl mx-auto ${
                            authStatus === 'connecting' ? 'animate-spin' : ''
                        }`}
                    />
                </div>
                <p className="text-gray-600">
                    {authStatus === 'idle'
                        ? t('workgate.click_generate_qr_start')
                        : statusMessage
                    }
                </p>
            </div>
        );
    };

    return (
        <div className="w-full max-w-md mx-auto">
            <div className="bg-white rounded-lg border shadow-sm p-6">
                {/* Header */}
                <div className="flex items-center justify-center mb-6">
                    <img src={zalo} alt="Zalo" className="w-8 h-8 mr-3" />
                    <h2 className="text-lg font-semibold text-gray-800">
                        {isRelink ? t('workgate.zalo_relink') : t('workgate.zalo_authentication')}
                    </h2>
                </div>

                {/* Status indicator */}
                <div className="text-center mb-4">
                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                        authStatus === 'success' ? 'bg-green-100 text-green-800' :
                        authStatus === 'error' ? 'bg-red-100 text-red-800' :
                        authStatus === 'scanned' ? 'bg-purple-100 text-purple-800' :
                        authStatus === 'waiting' ? 'bg-orange-100 text-orange-800' :
                        authStatus === 'connecting' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                    }`}>
                        <Icon
                            icon={statusIcons[authStatus]}
                            className={`w-4 h-4 mr-2 ${
                                authStatus === 'connecting' ? 'animate-spin' : ''
                            }`}
                        />
                        {authStatus === 'idle' ? t('workgate.ready') :
                         authStatus === 'connecting' ? t('workgate.connecting') :
                         authStatus === 'waiting' ? t('workgate.waiting_for_scan') :
                         authStatus === 'scanned' ? t('workgate.qr_scanned') :
                         authStatus === 'success' ? t('workgate.authenticated') :
                         t('workgate.error')
                        }
                    </div>
                </div>

                {/* QR Code display area */}
                <div className="border-2 border-dashed border-gray-200 rounded-lg p-6 mb-6 min-h-[200px] flex items-center justify-center">
                    {renderQRContent()}
                </div>

                {/* Status message */}
                {statusMessage && (
                    <div className={`text-center text-sm mb-4 ${statusColors[authStatus]}`}>
                        {statusMessage}
                    </div>
                )}

                {/* Action buttons */}
                <div className="flex gap-3">
                    {authStatus === 'idle' || authStatus === 'error' ? (
                        <button
                            onClick={initiateQRLogin}
                            className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md font-medium transition-colors duration-200 flex items-center justify-center"
                        >
                            <Icon icon="material-symbols:qr-code-2" className="w-5 h-5 mr-2" />
                            {t('workgate.generate_qr_code')}
                        </button>
                    ) : authStatus !== 'success' ? (
                        <button
                            onClick={resetAuth}
                            className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md font-medium transition-colors duration-200 flex items-center justify-center"
                        >
                            <Icon icon="material-symbols:refresh" className="w-5 h-5 mr-2" />
                            {t('workgate.reset')}
                        </button>
                    ) : (
                        <button
                            onClick={resetAuth}
                            className="flex-1 bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-md font-medium transition-colors duration-200 flex items-center justify-center"
                        >
                            <Icon icon="material-symbols:add" className="w-5 h-5 mr-2" />
                            {t('workgate.add_another_account')}
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ZaloQRAuth;