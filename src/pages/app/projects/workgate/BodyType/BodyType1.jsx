import React, { useState, useEffect } from 'react';
import { useForm, useField<PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import FormGroup from "@/components/ui/FormGroup";
import FacebookPageLoading from '@/components/partials/widget/PageLoading';
import {
    getUserLongLivedAccessToken,
    getUserId,
    getPageAccessTokenLongLived,
    getPageDetail,
    getTokenPermissions
} from '@/store/api/facebook/facebookApi';
import { useGetBotSettingQuery } from '@/store/api/project/botApiSlice';
import FacebookLoginButton from "@/components/partials/widget/FacebookLoginButton";
import AutocompleteBotFilter from './../AutocompleteBotFilter';
import FacebookPageMiniView from '@/components/partials/widget/FacebookPageMiniView';
import { useTranslation } from "react-i18next";
import facebook from "@/assets/images/platform/facebook.png";
import bizino_blue from "@/assets/images/platform/bizino_blue.png";
import { Icon } from '@iconify/react';
import { useCreateIntegrationMutation } from "@/store/api/project/integrationSlice";
import { toast } from 'react-toastify';

const schema = yup.object().shape({
    pages: yup.array().of(
        yup.object().shape({
            id: yup.string().required(),
            selectedBot: yup.object().nullable(),
            description: yup.string().required("Mô tả không được để trống"),
            access_token: yup.string().required(),
            category: yup.string(),
            category_list: yup.array().of(yup.object().shape({
                id: yup.string(),
                name: yup.string(),
            })),
            name: yup.string(),
            tasks: yup.array().of(yup.string()),
            picture: yup.object().shape({
                data: yup.object().shape({
                    height: yup.number(),
                    is_silhouette: yup.boolean(),
                    url: yup.string(),
                    width: yup.number(),
                }),
            }),
            emails: yup.array().of(yup.string()),
            website: yup.string(),
            location: yup.object().shape({
                street: yup.string(),
                zip: yup.string(),
                city: yup.string(),
                country: yup.string(),
                latitude: yup.number(),
                longitude: yup.number(),
            }),
        })
    ),
});

const BodyType1 = ({ botList, bot_uuid, closeModal, type }) => {
    const { t } = useTranslation();
    const [profile, setProfile] = useState(null);
    const [accessToken, setAccessToken] = useState(null);
    const [pages, setPages] = useState([]);
    const [userId, setUserId] = useState(null);
    const [loading, setLoading] = useState(false);
    const { data: botData, error: botError } = useGetBotSettingQuery(
        { _id: bot_uuid },
        { skip: !bot_uuid }
    );
    const [createIntegration, { isLoading }] = useCreateIntegrationMutation();

    const { control, handleSubmit, formState: { errors }, setValue, register } = useForm({
        resolver: yupResolver(schema, { context: { bot_uuid } }),
        defaultValues: { pages: [] },
    });

    const { fields, append, remove } = useFieldArray({
        control,
        name: "pages",
    });

    // Đồng bộ pages từ API với form
    useEffect(() => {
        if (pages.length > 0) {
            if (fields.length > pages.length) {
                remove(pages.length, fields.length - 1);
            }
            pages.forEach((page, index) => {
                if (index >= fields.length) {
                    append({
                        id: page.id,
                        selectedBot: bot_uuid ? { _id: bot_uuid, name: botData?.name } : null,
                        description: page.description,
                        access_token: page.access_token,
                        category: page.category,
                        category_list: page.category_list,
                        name: page.name,
                        tasks: page.tasks,
                        picture: page.picture,
                        emails: page.emails,
                        website: page.website,
                        location: page.location,
                    });
                } else {
                    setValue(`pages.${index}.id`, page.id);
                    setValue(`pages.${index}.selectedBot`, bot_uuid ? { _id: bot_uuid, name: botData?.name } : null);
                    setValue(`pages.${index}.description`, page.description);
                    setValue(`pages.${index}.access_token`, page.access_token);
                    setValue(`pages.${index}.category`, page.category);
                    setValue(`pages.${index}.category_list`, page.category_list);
                    setValue(`pages.${index}.name`, page.name);
                    setValue(`pages.${index}.tasks`, page.tasks);
                    setValue(`pages.${index}.picture`, page.picture);
                    setValue(`pages.${index}.emails`, page.emails);
                    setValue(`pages.${index}.website`, page.website);
                    setValue(`pages.${index}.location`, page.location);
                }
            });
        } else if (fields.length > 0) {
            remove(0, fields.length - 1);
        }
    }, [pages, fields, setValue, append, remove]);

    const getPageDetailInfo = async (pageList) => {
        try {
            setLoading(true);
            const updatedPages = await Promise.all(
                pageList.map(async (page) => {
                    const pageDetail = await getPageDetail(page.id, page.access_token);
                    return { ...page, ...pageDetail, selectedBot: null, description: `Fb Page - ${page.name}` };
                })
            );
            setPages(updatedPages);
            console.log("Final pages:", updatedPages);
        } catch (error) {
            console.error("Lỗi khi lấy chi tiết các Page:", error);
        } finally {
            setLoading(false);
        }
    };

    const getFacebookPages = async (shortLivedToken) => {
        try {
            setLoading(true);
            const longLivedTokenData = await getUserLongLivedAccessToken(shortLivedToken);
            if (!longLivedTokenData || !longLivedTokenData.access_token) {
                throw new Error("Không lấy được long-lived access token");
            }
            const userData = await getUserId(longLivedTokenData.access_token);
            if (!userData || !userData.id) {
                throw new Error("Không lấy được user ID");
            }
            setUserId(userData.id);
            const pagesData = await getPageAccessTokenLongLived(userData.id, longLivedTokenData.access_token);
            if (!pagesData || !pagesData.data) {
                throw new Error("Không lấy được danh sách Pages");
            }
            getPageDetailInfo(pagesData.data);
        } catch (error) {
            console.error("Error in getFacebookPages:", error);
            setLoading(false);
        }
    };

    const handleFacebookLogin = () => {
        window.FB.login((response) => {
            if (response.authResponse) {
                setProfile(response.authResponse);
                const token = response.authResponse.accessToken;
                setAccessToken(token);
                getFacebookPages(token);
            } else {
                console.error("Facebook login rejected >>>>", response);
            }
        }, { scope: "email,public_profile,pages_show_list,pages_read_engagement,pages_manage_posts,pages_messaging,pages_manage_ads,pages_manage_engagement,pages_manage_metadata" });
    };

    const handleSelectBotForPage = (pageId, bot, index) => {
        setValue(`pages.${index}.selectedBot`, bot);
    };

    const getFormattedData = async (data, page, index) => {
        const pageData = data.pages[index];
        // Get permissions for this specific page
        const permissions = await getTokenPermissions(pageData.access_token);
        console.log(">>>>>>>>>>>>>> Permissions for this page:", permissions);
        return {
            description: pageData.description,
            type: 1, // Messenger
            status: 1,
            config: JSON.stringify({
                on_chat_bot_uuid: bot_uuid || pageData.selectedBot?._id,
                page_access_token: pageData.access_token,
                facebook_page_id: pageData.id,
                facebook_page_name: pageData.name,
                facebook_page_website: pageData.website,
                facebook_page_emails: pageData.emails,
                facebook_user_id: userId,
                other_setting: pageData.other_setting || '',
                category: pageData.category,
                category_list: pageData.category_list,
                tasks: pageData.tasks,
                picture: pageData.picture,
                location: pageData.location,
                permissions: permissions,
            }),
        };
    };

    const onSubmit = async (data) => {
        if (Object.keys(errors).length > 0) {
            toast.error("Vui lòng kiểm tra lại các trường bắt buộc!");
            return;
        }

        // const hasBotUuid = data.pages.every((page, index) => bot_uuid || page.selectedBot?._id);
        // if (!hasBotUuid) {
        //     toast.error("Vui lòng chọn bot cho tất cả các trang!");
        //     return;
        // }

        try {
            const results = await Promise.all(
                data.pages.map(async (page, index) => {
                    try {
                        const formattedData = await getFormattedData(data, page, index);
                        const response = await createIntegration(formattedData).unwrap();
                        toast.success(response.message || `Created integration for ${page.name}`);
                        return response;
                    } catch (error) {
                        toast.error(`Failed to create integration for ${page.name}: ${error?.data?.message || 'Unknown error'}`);
                        throw error;
                    }
                })
            );

            if (results.every(result => result)) {
                closeModal(true);
            }
        } catch (err) {
            console.error("Lỗi khi tạo integration cho pages:", err);
        }
    };

    return (
        <div className="w-full h-[54vh] pb-5">
            <form className="space-y-2 h-full" onSubmit={handleSubmit(onSubmit)}>
                <span className="w-full border-b pb-1 d-flex text-black-600">{t("workgate.add_faceBook_workgate")}</span>
                {pages.length === 0 && !loading && (
                    <div className="w-full h-full flex flex-col justify-center items-center">
                        <div className="flex flex-row items-center justify-center mb-5">
                            <img className="flatform-icon p-1" src={bizino_blue} alt="bizino logo" />
                            <Icon icon="heroicons-outline:arrows-right-left" className="text-2xl mx-4 text-black-600" />
                            <img className="flatform-icon" src={facebook} alt="facebook logo" />
                        </div>
                        <div className="facebook-description my-4">
                            <p className="text-center text-xl mb-2 text-black-600">{t("workgate.add_facebook_workgate_title")}</p>
                            <p className="text-center text-sm mb-4 text-gray-500 font-normal max-w-md">
                                {t("workgate.add_facebook_workgate_description")}
                            </p>
                        </div>
                        <button type="button" onClick={handleFacebookLogin}>
                            <FacebookLoginButton />
                        </button>
                    </div>
                )}
                {loading && <FacebookPageLoading />}
                {pages.length > 0 && (
                    <div className="h-full overflow-y-auto">
                        <div className="space-y-3">
                            {pages.map((page, index) => (
                                <div key={page.id} className="facebook-page flex w-full rounded-lg p-2 mb-1">
                                    <div className="flex h-fit w-1/2 pr-4">
                                        <FacebookPageMiniView page={page} />
                                    </div>
                                    <div className="w-1/2 flex justify-end pl-4 border-l border-gray-200">
                                        {!bot_uuid ? (
                                            <div className="w-full">
                                                <Controller
                                                    name={`pages.${index}.selectedBot`}
                                                    control={control}
                                                    render={({ field }) => (
                                                        <AutocompleteBotFilter
                                                            botList={botList}
                                                            onSelectBot={(bot) => handleSelectBotForPage(page.id, bot, index)}
                                                            error={errors.pages?.[index]?.selectedBot?.message}
                                                        />
                                                    )}
                                                />
                                                <FormGroup error={errors.pages?.[index]?.description?.message}>
                                                    <input
                                                        type="text"
                                                        className="form-control py-2 mt-1"
                                                        {...register(`pages.${index}.description`)}
                                                        defaultValue={page.description}
                                                    />
                                                </FormGroup>
                                            </div>
                                        ) : (
                                            <div className="w-full">
                                                <span className="mb-1">Bot: {botData?.name}</span>
                                                <FormGroup error={errors.pages?.[index]?.description?.message}>
                                                    <input
                                                        type="text"
                                                        className="form-control py-2"
                                                        {...register(`pages.${index}.description`)}
                                                        defaultValue={page.description}
                                                    />
                                                </FormGroup>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ))}
                            <div className="flex justify-end w-full px-2">
                                <button type="submit" disabled={isLoading} className="btn btn-dark text-center mt-3">
                                    Create
                                </button>
                            </div>
                        </div>
                    </div>
                )}
                {Object.keys(errors).length > 0 && (
                    <div className="text-red-500 text-sm mt-2">
                        {errors.pages?.map((pageError, index) => (
                            <div key={index}>
                                {pageError.selectedBot?.message || pageError.description?.message}
                            </div>
                        ))}
                    </div>
                )}
            </form>
        </div>
    );
};

export default BodyType1;