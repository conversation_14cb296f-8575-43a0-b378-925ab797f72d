import React, { useState } from 'react';
import { useTranslation } from "react-i18next";
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import ZaloQRAuth from './ZaloQRAuth';
import WorkgateSelector from '@/components/ui/WorkgateSelector';

const BodyTypeZaloQR = ({ botList, bot_uuid, closeModal }) => {
    const { t, i18n } = useTranslation();
    const navigate = useNavigate();
    const [authData, setAuthData] = useState(null);
    const [step, setStep] = useState('mode_selection'); // mode_selection, authenticate, success
    const [mode, setMode] = useState('new'); // 'new' or 'relink'
    const [selectedWorkgate, setSelectedWorkgate] = useState(null);

    const handleAuthSuccess = async (data) => {
        console.log('Zalo authentication successful:', data);

        // Handle the new data structure from auth_success
        if (data.success && data.account) {
            setAuthData(data);
            setStep('success');
        } else {
            console.error('Invalid auth success data:', data);
            toast.error(t('workgate.zalo_integration_failed'));
        }
    };

    const handleAuthError = (error) => {
        console.error('Zalo authentication failed:', error);
        toast.error(t('workgate.zalo_auth_failed'));
        // Stay on authenticate step to allow retry
    };

    const resetFlow = () => {
        setStep('mode_selection');
        setAuthData(null);
        setMode('new');
        setSelectedWorkgate(null);
    };

    const handleModeSelection = (selectedMode) => {
        setMode(selectedMode);
        setStep('authenticate');
    };

    const handleWorkgateSelect = (workgate) => {
        setSelectedWorkgate(workgate);
    };

    const canProceedToAuth = () => {
        if (mode === 'new') return true;
        if (mode === 'relink') return selectedWorkgate !== null;
        return false;
    };

    const navigateToWorkgate = () => {
        if (authData?.account?.workGateId) {
            // Navigate to integration detail page with the workGateId
            navigate(`/integration/${authData.account.workGateId}`);
            closeModal();
        }
    };

    // Get appropriate success message based on current language
    const getSuccessMessage = () => {
        if (i18n.language === 'Vn' && authData?.account?.name) {
            return `${t('workgate.zalo_auth_successful')} ${authData.account.name}`;
        }
        return authData?.message || t('workgate.zalo_integration_complete_desc');
    };

    const renderModeSelectionStep = () => (
        <div className="w-full min-h-[54vh] pb-5">
            <div className="space-y-6 h-full">
                <div className="border-b pb-2">
                    <span className="text-black-600 text-sm sm:text-base font-medium">
                        {t('workgate.zalo_qr_setup')}
                    </span>
                </div>

                <div className="space-y-4">
                    <h3 className="text-lg font-medium text-gray-800 mb-4">
                        {t('workgate.choose_setup_mode')}
                    </h3>

                    {/* New Workgate Option */}
                    <div
                        className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                            mode === 'new' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setMode('new')}
                    >
                        <div className="flex items-start">
                            <div className="flex-shrink-0 mt-1">
                                <div className={`w-4 h-4 rounded-full border-2 ${
                                    mode === 'new' ? 'border-blue-500 bg-blue-500' : 'border-gray-300'
                                }`}>
                                    {mode === 'new' && (
                                        <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                                    )}
                                </div>
                            </div>
                            <div className="ml-3">
                                <h4 className="text-base font-medium text-gray-800">
                                    {t('workgate.create_new_workgate')}
                                </h4>
                                <p className="text-sm text-gray-600 mt-1">
                                    {t('workgate.create_new_workgate_desc')}
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Re-link Option */}
                    <div
                        className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                            mode === 'relink' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setMode('relink')}
                    >
                        <div className="flex items-start">
                            <div className="flex-shrink-0 mt-1">
                                <div className={`w-4 h-4 rounded-full border-2 ${
                                    mode === 'relink' ? 'border-blue-500 bg-blue-500' : 'border-gray-300'
                                }`}>
                                    {mode === 'relink' && (
                                        <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                                    )}
                                </div>
                            </div>
                            <div className="ml-3">
                                <h4 className="text-base font-medium text-gray-800">
                                    {t('workgate.relink_existing_workgate')}
                                </h4>
                                <p className="text-sm text-gray-600 mt-1">
                                    {t('workgate.relink_existing_workgate_desc')}
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Workgate Selector for Re-link Mode */}
                    {mode === 'relink' && (
                        <div className="mt-4">
                            <WorkgateSelector
                                onSelect={handleWorkgateSelect}
                                selectedWorkgate={selectedWorkgate}
                                workgateType={6} // Zalo QR type
                            />
                        </div>
                    )}

                    {/* Continue Button */}
                    <div className="flex justify-end mt-6">
                        <button
                            onClick={() => handleModeSelection(mode)}
                            disabled={!canProceedToAuth()}
                            className={`px-6 py-2 rounded-md font-medium transition-colors ${
                                canProceedToAuth()
                                    ? 'bg-blue-500 hover:bg-blue-600 text-white'
                                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            }`}
                        >
                            <Icon icon="material-symbols:arrow-forward" className="w-5 h-5 mr-2 inline" />
                            {t('workgate.continue')}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );

    const renderAuthStep = () => (
        <div className="w-full min-h-[54vh] pb-5">
            <div className="space-y-4 h-full">
                <div className="border-b pb-2 flex items-center justify-between">
                    <span className="text-black-600 text-sm sm:text-base font-medium">
                        {mode === 'relink' ? t('workgate.zalo_qr_relink') : t('workgate.zalo_qr_authentication')}
                    </span>
                    <button
                        onClick={resetFlow}
                        className="text-sm text-blue-500 hover:text-blue-600 flex items-center"
                    >
                        <Icon icon="material-symbols:arrow-back" className="w-4 h-4 mr-1" />
                        {t('workgate.back')}
                    </button>
                </div>

                {mode === 'relink' && selectedWorkgate && (
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
                        <div className="flex items-center text-sm text-blue-700">
                            <Icon icon="material-symbols:info" className="w-4 h-4 mr-2" />
                            <span>
                                {t('workgate.relinking_to')}: <strong>{selectedWorkgate.description}</strong>
                            </span>
                        </div>
                    </div>
                )}

                <div className="flex flex-col items-center justify-center h-full">
                    <ZaloQRAuth
                        onAuthSuccess={handleAuthSuccess}
                        onAuthError={handleAuthError}
                        workgateId={mode === 'relink' ? selectedWorkgate?._id : null}
                        isRelink={mode === 'relink'}
                    />
                </div>
            </div>
        </div>
    );

    const renderSuccessStep = () => (
        <div className="w-full min-h-[54vh] pb-5">
            <div className="space-y-4 h-full">
                <span className="w-full border-b pb-1 d-flex text-black-600 text-sm sm:text-base">
                    {t('workgate.integration_created_successfully')}
                </span>

                <div className="flex flex-col items-center justify-center h-full">
                    <div className="text-center max-w-md">
                        <div className="mb-6">
                            <Icon icon="material-symbols:check-circle" className="text-6xl text-green-500 mx-auto mb-4" />
                            <h3 className="text-xl font-semibold text-green-600 mb-2">
                                {t('workgate.zalo_integration_complete')}
                            </h3>
                            <p className="text-gray-600">
                                {getSuccessMessage()}
                            </p>
                        </div>

                        {authData?.account && (
                            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                                <h4 className="font-medium text-green-800 mb-2">{t('workgate.connected_account')}:</h4>
                                <div className="text-green-700 text-sm space-y-1">
                                    <p><span className="font-medium">{t('workgate.name')}:</span> {authData.account.name}</p>
                                    <p><span className="font-medium">{t('workgate.id')}:</span> {authData.account.id}</p>
                                </div>
                            </div>
                        )}

                        {/* Primary action - Navigate to integration detail */}
                        {authData?.account?.workGateId && (
                            <button
                                onClick={navigateToWorkgate}
                                className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-md font-medium transition-colors duration-200 flex items-center justify-center mb-4"
                            >
                                <Icon icon="material-symbols:arrow-forward" className="w-5 h-5 mr-2" />
                                {t('workgate.goto_workgate_detail')}
                            </button>
                        )}

                        {/* Secondary actions */}
                        <div className="flex gap-3">
                            <button
                                onClick={resetFlow}
                                className="flex-1 bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-md font-medium transition-colors duration-200 flex items-center justify-center"
                            >
                                <Icon icon="material-symbols:add" className="w-5 h-5 mr-2" />
                                {t('workgate.add_another')}
                            </button>
                            <button
                                onClick={closeModal}
                                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md font-medium transition-colors duration-200 flex items-center justify-center"
                            >
                                <Icon icon="material-symbols:close" className="w-5 h-5 mr-2" />
                                {t('workgate.close')}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );

    // Render based on current step
    switch (step) {
        case 'mode_selection':
            return renderModeSelectionStep();
        case 'authenticate':
            return renderAuthStep();
        case 'success':
            return renderSuccessStep();
        default:
            return renderModeSelectionStep();
    }
};

export default BodyTypeZaloQR;