import React, { useState, useEffect } from 'react';
import { useGetIntegrationMutation, useUpdateIntegrationMutation, useDeleteIntegrationMutation } from "@/store/api/project/integrationSlice";
import { useParams } from "react-router-dom";
import Card from '@/components/ui/Card';
import Dropdown from "@/components/ui/Dropdown";
import Icon from "@/components/ui/Icon";
import { Menu, Transition } from "@headlessui/react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import UpdateBodyType1 from './updateBodyType/updateBodyType1';
import UpdateBodyType2 from './updateBodyType/updateBodyType2';
import UpdateBodyType3 from './updateBodyType/updateBodyType3';
import UpdateBodyType4 from './updateBodyType/updateBodyType4';
import AutocompleteBotFilter from '../projects/workgate/AutocompleteBotFilter';
import { useLazyGetAllNameQuery } from "@/store/api/project/botApiSlice";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";
import CodeBlock from "@/components/partials/widget/CodeBlock";
import { useNavigate } from "react-router-dom";

// Định nghĩa schema dynamic
const getSchema = (type) => {
    const baseSchema = {
        description: yup.string().required("Integration Name is required"),
        on_chat_bot_uuid: yup.string().required("Bot UUID is required"),
    };
    switch (Number(type)) {
        case 1:
            return yup.object().shape({
                ...baseSchema,
                config: yup.object().shape({
                    other_setting: yup.string(),
                }),
                status: yup.number().oneOf([-1, 1, 2]).required("Status is required")
            });
        case 2:
            return yup.object().shape({
                ...baseSchema,
                extraField: yup.string(),
            });
        case 3:
            return yup.object().shape({
                ...baseSchema,
                settings: yup.string(),
            });
        case 4:
            return yup.object().shape({
                ...baseSchema,
                config: yup.object().shape({
                    other_setting: yup.string(),
                }),
                status: yup.number().oneOf([-1, 1, 2]).required("Status is required")
            });
        default:
            return yup.object().shape(baseSchema);
    }
};

// Ánh xạ type và status
const typeMap = {
    1: "Messenger",
    2: "Facebook",
    3: "Twitter",
    4: "Custom Gate",
};

const statusMap = {
    0: "Inactive",
    1: "Active",
    2: "Pending",
};

const IntegrationDetail = () => {
    const { t } = useTranslation();
    const { id } = useParams();
    const [integration, setIntegration] = useState(null);
    const [getIntegration, { isFetching }] = useGetIntegrationMutation();
    const [getBotList] = useLazyGetAllNameQuery();
    const [botList, setBotList] = useState([]);
    const [selectedBot, setSelectedBot] = useState(null);
    const [errorBotSelect, setErrorBotSelect] = useState("");
    const [updateIntegration] = useUpdateIntegrationMutation();
    const [deleteIntegration] = useDeleteIntegrationMutation();
    const navigate = useNavigate();

    useEffect(() => {
        const fetchIntegration = async () => {
            try {
                const res = await getIntegration({ _id: id }).unwrap();
                setIntegration(res);
            } catch (error) {
                console.error("Error fetching integration:", error);
            }
        };
        const fetchBotList = async () => {
            try {
                const res = await getBotList();
                if (res) {
                    setBotList(res.data);
                }
            } catch (error) {
                console.error("Error fetching bot list:", error);
            }
        };

        fetchBotList();
        fetchIntegration();
    }, []);

    const { register, handleSubmit, formState: { errors }, setValue, reset } = useForm({
        resolver: yupResolver(getSchema(integration?.type)),
        defaultValues: {},
    });

    useEffect(() => {
        if (integration) {
            reset({
                description: integration.description,
                config: {
                    other_setting: integration.config?.other_setting || "", // Nested config
                },
                status: integration.status,
                on_chat_bot_uuid: integration.config?.on_chat_bot_uuid || "",
            });
            const config = integration.config || {};
            const botUuid = config.on_chat_bot_uuid;
            const defaultBot = botList.find(bot => bot._id === botUuid);
            if (defaultBot) setSelectedBot(defaultBot);
        }
    }, [integration, reset, botList]);

    const getFormattedData = (data) => {
        switch (Number(integration.type)) {
            case 1:
                return {
                    _id: integration._id,
                    description: data.description,
                    config: JSON.stringify({
                        on_chat_bot_uuid: selectedBot._id,
                        other_setting: data.config.other_setting,
                        avatar: data.config.avatar,
                        on_feed_events: data.config.on_feed_events,
                    }),
                    status: data.status,
                };
            case 2:
                return {
                    _id: integration._id,
                    description: data.description,
                    config: JSON.stringify({
                        on_chat_bot_uuid: selectedBot._id,
                        other_setting: data.config.other_setting,
                        avatar: data.config.avatar,
                    }),
                    status: data.status,
                };
            case 3:
                return {
                    _id: integration._id,
                    description: data.description,
                    config: JSON.stringify({
                        on_chat_bot_uuid: selectedBot._id,
                        other_setting: data.config.other_setting,
                        avatar: data.config.avatar,
                    }),
                    status: data.status,
                };
            case 4:
                return {
                    _id: integration._id,
                    description: data.description,
                    config: JSON.stringify({
                        on_chat_bot_uuid: selectedBot._id,
                        other_setting: data.config.other_setting,
                        avatar: data.config.avatar,
                    }),
                    status: data.status,
                };
            case 6:
                return {
                    _id: integration._id,
                    description: data.description,
                    config: JSON.stringify({
                        on_chat_bot_uuid: selectedBot._id,
                    }),
                    status: data.status,
                };
            default:
                return {};
        }
    };

    // const onSubmit = async (data) => {
    //     const formattedData = getFormattedData(data);
    //     console.log("/n/n chạy onSubmit");
    //     if (!selectedBot) {
    //         setErrorBotSelect("Chọn 1 bot");
    //         return;
    //     }

    //     setErrorBotSelect("");
    //     try {
    //         await updateIntegration(formattedData).unwrap();
    //         toast.success("Cập nhật thành công!");
    //     } catch (error) {
    //         console.error("❌ Lỗi khi cập nhật:", error);
    //     }
    // };

    const onSubmit = async (data, e) => {
        e.preventDefault();
        console.log("onSubmit tại IntegrationDetail.jsx >>>>>>>>>>>>> ", data);
        try {
            if (!selectedBot) {
                setErrorBotSelect("Chọn 1 bot");
                return;
            }

            // Kiểm tra on_feed_events
            if (data?.config?.on_feed_events) {
                const feedEvents = data.config.on_feed_events;

                // validate từng cái feedEvent một
                const hasInvalidEvent = Object.entries(feedEvents).some(([key, event]) => {
                    // if (key === 'undefined') {
                    //     return true;
                    // }

                    // Kiểm tra các giá trị bắt buộc
                    return !event.bot_uuid || !event.name || event.bot_uuid === 'undefined' ||
                        event.name === 'undefined' || event.disabled === undefined;
                });

                if (hasInvalidEvent) {
                    toast.error("Có feed event không hợp lệ. Vui lòng kiểm tra lại!");
                    return;
                }
            }

            const formattedData = getFormattedData(data);
            if (!formattedData || Object.keys(formattedData).length === 0) {
                throw new Error("Dữ liệu không hợp lệ");
            }

            setErrorBotSelect("");
            await updateIntegration(formattedData).unwrap();
            toast.success("Cập nhật thành công!");
        } catch (error) {
            console.error("❌ Lỗi khi cập nhật:", error);
            toast.error(error.message || "Có lỗi xảy ra khi cập nhật");
        }
    };

    const onDelete = async (id) => {
        try {
            await deleteIntegration({ _id: id }).unwrap();
            toast.success(t("delete_success"));
            navigate("/integration");
        } catch (error) {
            console.error("❌ Lỗi khi xóa:", error);
            toast.error(t("delete_error"));
        }
    };

    const handleSelectBot = (bot) => {
        setErrorBotSelect("");
        setSelectedBot(bot);
        setValue("on_chat_bot_uuid", bot._id);
    };

    const getBodyComponent = () => {
        switch (Number(integration?.type)) {
            case 1:
                return <UpdateBodyType1 register={register} errors={errors} setValue={setValue} integration={integration} botList={botList} />;
            case 2:
                return <UpdateBodyType2 register={register} errors={errors} setValue={setValue} />;
            case 3:
                return <UpdateBodyType3 register={register} errors={errors} setValue={setValue} />;
            case 4:
                return <UpdateBodyType4 register={register} errors={errors} setValue={setValue} integration={integration} />;
            default:
                return null;
        }
    };

    // Lấy tên bot từ UUID
    const getBotName = () => {
        const config = integration?.config || {};
        const botUuid = selectedBot?._id || config.on_chat_bot_uuid;
        const bot = botList.find(b => b._id === botUuid);
        return bot ? bot.name : "Chưa chọn bot";
    };

    const workGateCode = `<script>
	some_config_later = {
		_id: "${integration?._id}",
		workgate_name: "${integration?.description}",
		secret: "${integration?.secret}",
	};
</script>`;

    return (
        <div className="container">
            <Card>
                <div className="flex flex-row justify-between mb-5">
                    <h4 className="">{t("workgate.title")}: {integration?.description}</h4>
                    <Dropdown label={
                        <Icon icon="heroicons-outline:adjustments-horizontal" className="w-6 h-6" />
                    } classMenuItems="w-[180px] top-[28px]">
                        <Menu.Item>
                            <div
                                className={`text-slate-600 dark:text-slate-300 hover:text-red-500 block cursor-pointer overflow-hidden`}
                            >
                                <div className={`block cursor-pointer px-4 py-2`} onClick={() => onDelete(integration?._id)}>
                                    <div className="flex items-center">
                                        <span className="block text-xl ltr:mr-3 rtl:ml-3">
                                            <Icon icon="heroicons-outline:trash" />
                                        </span>
                                        <span className="block text-sm">{t("delete")}</span>
                                    </div>
                                </div>
                            </div>
                        </Menu.Item>
                    </Dropdown>
                </div>
                {isFetching ? (
                    <p>{t("loading")}</p>
                ) : (
                    <form onSubmit={handleSubmit(onSubmit)}>
                        {/* Phần chọn bot */}
                        <div className="mb-5 px-4">
                            <div className="flex flex-col md:flex-row items-start">
                                <label className="block text-sm font-medium text-gray-700 w-full md:w-1/6 mb-1 md:mb-0">
                                    {t("workgate.messaging_event")}
                                </label>
                                <div className="w-full md:w-5/6">
                                    <p className="mb-2 text-base text-blue-700 font-semibold">{getBotName()}</p>
                                    <AutocompleteBotFilter
                                        botList={botList}
                                        onSelectBot={handleSelectBot}
                                        error={errorBotSelect}
                                        defaultBotUuid={integration?.config?.on_chat_bot_uuid} // Truyền bot_uuid mặc định
                                    />
                                    {errorBotSelect && (
                                        <p className="mt-1 text-sm text-red-600">{errorBotSelect}</p>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Component động */}
                        {getBodyComponent()}

                        {/* Nút submit */}
                        <div className="flex justify-end">
                            <button type="submit" className="btn btn-primary mt-4 w-full md:w-1/5">
                                {t("workbot.update")}
                            </button>
                        </div>
                    </form>
                )}
                <div className="mt-4">
                    <CodeBlock
                        codeString={workGateCode}
                        title={t("Config Workgate")}
                    />
                </div>
            </Card>
        </div>
    );
};

export default IntegrationDetail;