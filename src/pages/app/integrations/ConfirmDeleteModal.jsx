import React, { Component } from 'react';
import Modal from '@/components/ui/Modal';
import { useTranslation } from "react-i18next";

const ConfirmDeleteModal = ({
    bot_uuid,
    openDeleteModal,
    closeModal,
    onConfirm
}) => {

    const { t } = useTranslation();
    const handleConfirm = () => {
        onConfirm(bot_uuid);
        closeModal();
    };

    return (
        <Modal
            title={t("workgate.delete_integration")}
            activeModal={openDeleteModal}
            onClose={() => closeModal(true)}
            centered
        >
            <div className="px-4 py-2">
                <p className="text-lg text-black-500 pb-5">{t("workgate.delete_integration_confirm")}</p>
                <div className="flex justify-end mt-4">
                    <button
                        className="btn btn-danger px-5 py-2"
                        onClick={handleConfirm}
                    >
                        {t("workgate.confirm_delete")}
                    </button>
                    <button
                        className="btn btn-secondary px-5 py-2 ml-2"
                        onClick={() => closeModal()}
                    >
                        {t("workgate.cancel")}
                    </button>
                </div>
            </div>
        </Modal>
    );
}

export default ConfirmDeleteModal;