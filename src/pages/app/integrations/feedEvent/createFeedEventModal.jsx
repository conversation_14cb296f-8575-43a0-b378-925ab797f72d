import React, { useEffect, useState } from 'react';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import { useForm } from 'react-hook-form';
import FormGroup from '@/components/ui/FormGroup';
import { useTranslation } from "react-i18next";
import Swicth from '@/components/ui/Switch';
import AutocompleteBotFilter from "@/pages/app/projects/workgate/AutocompleteBotFilter"
import SelectBox from "@/components/ui/SelectBox";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

const CreateFeedEventModal = ({ openModal, closeModal, posts = [], onSubmit, botList, onSuccess }) => {
    const { t } = useTranslation();
    const [handInput, setHandInput] = useState(false);

    const schema = yup.object().shape({
        post_id: yup.string().required("Post ID là bắt buộc"),
        bot_uuid: yup.string().required("Bot UUID là bắt buộc")
    });
    const {
        register,
        handleSubmit,
        formState: { errors, isValid },
        reset,
        watch,
        setValue,
    } = useForm({
        mode: 'onChange',
        resolver: yupResolver(schema)
    });

    // Thêm state để theo dõi bot được chọn
    const [selectedBot, setSelectedBot] = useState(null);
    const postId = watch('post_id');

    useEffect(() => {
        register('post_id', { required: 'Post ID là bắt buộc' });
    }, [register]);

    const handleFormSubmit = (data) => {
        const selectedPost = posts.find(post => post.id === data.post_id);
        if (!selectedPost) {
            toast.error('Vui lòng chọn hoặc nhập một Post ID hợp lệ!');
            return;
        }

        if (selectedPost.isExist) {
            toast.error("Post ID này đã tồn tại!");
            return;
        }

        try {
            onSubmit?.({
                ...data,
                name: selectedPost?.message?.split('.')[0]?.substring(0, 60) || `Post ${data.post_id}`
            });
            onSuccess?.();
            reset();
            closeModal();
        } catch (error) {
            toast.error("Có lỗi xảy ra khi tạo feed event");
        }
    };

    // Kiểm tra form có hợp lệ không
    const isFormValid = postId && selectedBot;

    return (
        <Modal
            title="Tạo Feed Event Mới"
            activeModal={openModal}
            onClose={closeModal}
        >
            <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
                <FormGroup error={errors.post_id}>
                    <div className="flex w-full items-center justify-between mb-1">
                        <label className="block capitalize form-label">Facebook Post</label>
                        <Swicth
                            value={handInput}
                            onChange={(e) => setHandInput(e.target.checked)}
                            badge
                            prevIcon="heroicons-outline:pencil"
                            nextIcon="heroicons-outline:queue-list"
                            switchClass="w-10 h-5"
                        />
                    </div>
                    {handInput ? (
                        <input
                            type="text"
                            // className="form-control px-3 py-2"
                            className={`w-full px-3 text-black-600 dark:text-white py-2 border dark:border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-slate-900`}
                            placeholder="Nhập Post ID"
                            {...register("post_id", {
                                required: "Post ID là bắt buộc"
                            })}
                        />
                    ) : (
                        <SelectBox
                            data={posts}
                            valueKey="id"
                            displayKey="message"
                            searchKeys={["message", "id"]}
                            placeholder={t("workgate.selectPost")}
                            value={watch('post_id')}
                            onChange={(post_id) => {
                                setValue('post_id', post_id, { shouldValidate: true }); // Kích hoạt validation khi setValue
                            }}
                        />
                    )}
                </FormGroup>

                <FormGroup error={errors.bot_uuid}>
                    <label className="block capitalize form-label">{t("workbot.assigned_bot")}</label>
                    {/* <AutocompleteBotFilter
                        botList={botList}
                        onSelectBot={(bot) => {
                            setValue("bot_uuid", bot._id);
                            setSelectedBot(bot);
                        }}
                        errors={errors}
                    /> */}
                    <AutocompleteBotFilter
                        botList={botList}
                        onSelectBot={(bot) => {
                            console.log('Selected bot:', bot); // Debug để kiểm tra bot được chọn
                            setValue("bot_uuid", bot._id, { shouldValidate: true }); // Kích hoạt validation
                            setSelectedBot(bot);
                        }}
                        errors={errors}
                    />
                </FormGroup>

                <FormGroup error={errors.name}>
                    <label className="block capitalize form-label">Tên</label>
                    <input
                        type="text"
                        className="form-control py-2"
                        readOnly
                        value={posts.find(post => post.id === watch('post_id'))?.message?.split('.')[0]?.substring(0, 60) || `Post ${watch('post_id')}`}
                    />
                </FormGroup>

                <div className="ltr:text-right rtl:text-left">
                    <Button
                        type="submit"
                        className="btn btn-dark text-center"
                        disabled={!isValid || !selectedBot}
                    >
                        Tạo mới
                    </Button>
                </div>
            </form>
        </Modal>
    );
};

export default CreateFeedEventModal;