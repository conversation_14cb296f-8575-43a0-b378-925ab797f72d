import React, { useState, useEffect } from 'react';
import FormGroup from "@/components/ui/FormGroup";
import { useTranslation } from "react-i18next";
import bizino_blue from "@/assets/images/platform/bizino_blue.png";
import zalo from "@/assets/images/platform/zalo.png";
import { Icon } from '@iconify/react';

const UpdateBodyType4 = ({ register, errors, setValue, integration }) => {
    const { t } = useTranslation();
    const config = integration?.config || {};
    const [configFile, setConfigFile] = useState(null);

    const typeMap = {
        1: "Messenger",
        2: "Telegram",
        3: "WhatsApp",
        4: "Zalo Personal",
    };
    const statusOptions = [
        { value: -1, label: "Disabled" },
        { value: 1, label: "Active" },
        { value: 2, label: "Inactive" },
    ];

    const createConfigJson = (a, b) => {
        const configData = {
            WORKGATE_ID: a || 'default_a',
            SECRET: b || 'default_b',
        };
        return new Blob([JSON.stringify(configData, null, 2)], { type: 'application/json' });
    };

    const downloadConfigFile = () => {
        if (configFile) {
            const url = window.URL.createObjectURL(configFile);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'bot_setting.json';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        } else {
            console.log('No config file to download');
        }
    };

    useEffect(() => {
        // console.log('integration', integration)
        let a = integration._id;
        let b = integration.secret;
        let file = createConfigJson(a, b);
        setConfigFile(file);
    }, []);

    return (
        <div className="space-y-6">
            {/* Edit Section */}
            <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm">
                <div className="p-6 border-b border-slate-200 dark:border-slate-700">
                    <div className="flex items-center">
                        <div className="h-10 w-1 bg-primary-500 rounded-full mr-3"></div>
                        <h5 className="text-xl font-semibold text-slate-900 dark:text-slate-200">
                            {t("workgate.edit_title")}
                        </h5>
                    </div>
                </div>

                <div className="p-6 space-y-6">
                    <FormGroup error={errors.description}>
                        <div className="grid grid-cols-12 gap-4 items-center">
                            <label className="col-span-12 md:col-span-3 text-sm font-medium text-slate-700 dark:text-slate-200">
                                {t("workgate.name")}
                            </label>
                            <div className="col-span-12 md:col-span-9">
                                <input
                                    type="text"
                                    className="w-full px-4 py-2.5 rounded-lg border border-slate-200 dark:border-slate-700 dark:bg-slate-900 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                    placeholder={t("workgate.name_placeholder")}
                                    {...register("description")}
                                />
                                {errors.description && (
                                    <p className="mt-1.5 text-sm text-red-500">{errors.description.message}</p>
                                )}
                            </div>
                        </div>
                    </FormGroup>

                    <FormGroup error={errors.other_setting}>
                        <div className="grid grid-cols-12 gap-4 items-center">
                            <label className="col-span-12 md:col-span-3 text-sm font-medium text-slate-700 dark:text-slate-200">
                                {t("workgate.other_setting")}
                            </label>
                            <div className="col-span-12 md:col-span-9">
                                <input
                                    type="text"
                                    className="w-full px-4 py-2.5 rounded-lg border border-slate-200 dark:border-slate-700 dark:bg-slate-900 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                    placeholder={t("workgate.other_setting_placeholder")}
                                    {...register("config.other_setting")}
                                />
                                {errors.other_setting && (
                                    <p className="mt-1.5 text-sm text-red-500">{errors.other_setting.message}</p>
                                )}
                            </div>
                        </div>
                    </FormGroup>

                    <FormGroup error={errors.status}>
                        <div className="grid grid-cols-12 gap-4 items-center">
                            <label className="col-span-12 md:col-span-3 text-sm font-medium text-slate-700 dark:text-slate-200">
                                {t("workgate.status")}
                            </label>
                            <div className="col-span-12 md:col-span-9 relative">
                                <select
                                    className="w-full px-4 py-2.5 rounded-lg border border-slate-200 dark:border-slate-700 dark:bg-slate-900 focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none bg-white dark:bg-slate-900 cursor-pointer"
                                    {...register("status")}
                                >
                                    {statusOptions.map(option => (
                                        <option key={option.value} value={option.value}>
                                            {option.label}
                                        </option>
                                    ))}
                                </select>
                                <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                    <Icon icon="heroicons:chevron-down" className="w-5 h-5 text-gray-400" />
                                </div>
                            </div>
                        </div>
                    </FormGroup>
                </div>
            </div>

            {/* Download Section */}
            <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm">
                <div className="p-6 border-b border-slate-200 dark:border-slate-700">
                    <div className="flex items-center">
                        <div className="h-10 w-1 bg-slate-400 rounded-full mr-3"></div>
                        <h5 className="text-xl font-semibold text-slate-900 dark:text-slate-200">
                            {t("workgate.download_files")}
                        </h5>
                    </div>
                </div>

                <div className="p-6">
                    <div className="space-y-4">
                        {/* Config.json Download */}
                        <div className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div className="flex items-center space-x-3">
                                <Icon icon="heroicons:document-text" className="text-2xl text-primary-500" />
                                <span className="font-medium text-slate-900 dark:text-slate-200">bot_setting.json</span>
                            </div>
                            <button
                                onClick={downloadConfigFile}
                                type="button"
                                className="btn btn-primary btn-sm flex items-center space-x-2"
                            >
                                <span>{t("workgate.download_button")}</span>
                                <Icon icon="heroicons:cloud-arrow-down" className="text-lg" />
                            </button>
                        </div>

                        {/* Zalo Setup File Download */}
                        <div className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-900 rounded-lg">
                            <div className="flex items-center space-x-3">
                                <Icon icon="simple-icons:zalo" className="text-2xl text-blue-500" />
                                <span className="font-medium text-slate-900 dark:text-slate-200">
                                    {t("workgate.zalo_setup_file")}
                                </span>
                            </div>
                            <a
                                href="https://bizino.com.vn/connector-zalo-bizino.zip"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="btn btn-primary btn-sm flex items-center space-x-2"
                            >
                                <span>{t("workgate.download_button")}</span>
                                <Icon icon="heroicons:cloud-arrow-down" className="text-lg" />
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default UpdateBodyType4;