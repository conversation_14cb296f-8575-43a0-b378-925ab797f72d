import React from "react";
import { useTranslation } from 'react-i18next';

const TermsAndContinue = ({ onContinue }) => {
  const { t } = useTranslation();
  return (
    <div className="bg-white rounded-xl p-4 sm:p-6 lg:p-8 shadow-sm border border-gray-100 mt-6 mb-6">
      <p className="text-sm text-gray-600 ">
        {t('checkout.terms_prefix')}
        <a
          href="https://bizino.ai/terms_use"
          className="text-blue-500 hover:text-blue-600"
        >
          {t('checkout.terms_of_service')}
        </a>{" "}
        {t('checkout.and')}{" "}
        <a
          href="https://bizino.ai/privacy_policy"
          className="text-blue-500 hover:text-blue-600"
        >
          {t('checkout.privacy_policy')}
        </a>{" "}
        {t('checkout.of_bizino')}
      </p>
      <button
        onClick={onContinue}
        className="w-full bg-blue-500 text-white py-3 px-4 my-4 rounded-lg hover:bg-blue-600 transition-colors font-medium shadow-sm"
      >
        {t('checkout.continue')}
      </button>
    </div>
  );
};

export default TermsAndContinue; 