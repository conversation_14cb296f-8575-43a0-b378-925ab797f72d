import React from "react";
import { useTranslation } from 'react-i18next';

const InvoiceSection = ({
  needInvoice,
  onNeedInvoiceChange,
  invoiceType,
  onInvoiceTypeChange,
  invoiceInfo,
  onInvoiceInfoChange,
  errors,
}) => {
  const { t } = useTranslation();
  return (
    <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
      <h3 className="font-medium mb-4 text-lg">{t('checkout.invoice_request')}</h3>
      <div className="mt-4">
        <label className="flex items-center space-x-2 cursor-pointer">
          <input
            type="checkbox"
            checked={needInvoice}
            onChange={(e) => onNeedInvoiceChange(e.target.checked)}
            className="form-checkbox text-blue-500 rounded"
          />
          <span className="text-gray-700">{t('checkout.export_invoice')}</span>
        </label>
      </div>

      {needInvoice && (
        <div className="mt-4 space-y-4 border-t pt-4">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('checkout.invoice_type')}
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="invoiceType"
                  value="personal"
                  checked={invoiceType === "personal"}
                  onChange={(e) => onInvoiceTypeChange(e.target.value)}
                  className="form-radio text-blue-500"
                />
                <span>{t('checkout.personal')}</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="invoiceType"
                  value="company"
                  checked={invoiceType === "company"}
                  onChange={(e) => onInvoiceTypeChange(e.target.value)}
                  className="form-radio text-blue-500"
                />
                <span>{t('checkout.company')}</span>
              </label>
            </div>
          </div>

          <div>
            {invoiceType === "company" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('checkout.company_name')}
                </label>
                <input
                  type="text"
                  name="companyName"
                  value={invoiceInfo.companyName}
                  onChange={onInvoiceInfoChange}
                  className="w-full border rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder={t('checkout.company_name_placeholder')}
                />
                {errors.companyName && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.companyName}
                  </p>
                )}
              </div>
            )}
            {invoiceType === "personal" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('checkout.personal_name')}
                </label>
                <input
                  type="text"
                  name="personalName"
                  value={invoiceInfo.personalName}
                  onChange={onInvoiceInfoChange}
                  className="w-full border rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder={t('checkout.personal_name_placeholder')}
                />
                {errors.personalName && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.personalName}
                  </p>
                )}
              </div>
            )}
          </div>

          <div>
            {invoiceType === "company" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('checkout.tax_code')}
                </label>
                <input
                  type="text"
                  name="taxCode"
                  value={invoiceInfo.taxCode}
                  onChange={onInvoiceInfoChange}
                  className="w-full border rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder={t('checkout.tax_code_placeholder')}
                />
                {errors.taxCode && (
                  <p className="mt-1 text-sm text-red-500">{errors.taxCode}</p>
                )}
              </div>
            )}
            {invoiceType === "personal" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('checkout.personal_id')}
                </label>
                <input
                  type="text"
                  name="personalId"
                  value={invoiceInfo.personalId}
                  onChange={onInvoiceInfoChange}
                  className="w-full border rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder={t('checkout.personal_id_placeholder')}
                />
                {errors.personalId && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.personalId}
                  </p>
                )}
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('checkout.address')}
            </label>
            <input
              type="text"
              name="address"
              value={invoiceInfo.address}
              onChange={onInvoiceInfoChange}
              className="w-full border rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder={t('checkout.address_placeholder')}
            />
            {errors.address && (
              <p className="mt-1 text-sm text-red-500">{errors.address}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('checkout.email')}
            </label>
            <input
              type="email"
              name="email"
              value={invoiceInfo.email}
              onChange={onInvoiceInfoChange}
              className="w-full border rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder={t('checkout.email_placeholder')}
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-500">{errors.email}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('checkout.phone')}
            </label>
            <input
              type="tel"
              name="phone"
              value={invoiceInfo.phone}
              onChange={onInvoiceInfoChange}
              className="w-full border rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder={t('checkout.phone_placeholder')}
            />
            {errors.phone && (
              <p className="mt-1 text-sm text-red-500">{errors.phone}</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default InvoiceSection; 