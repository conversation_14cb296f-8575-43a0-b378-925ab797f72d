import PackageFeature from './PackageFeature';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from "react-i18next";
import Icon from "@/components/ui/Icon";
import { useDispatch } from "react-redux";
import { setPackageId } from "@/store/api/package/packageSelectionSlice";

const PackageCard = ({
    type,
    price,
    features,
    isActive,
    tag,
    tagColor = "green",
    borderColor = "slate-200",
    sub_tag,
    billingCycle,
    _id,
    currentTeamPackage,
}) => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const handleClick = () => {
      dispatch(setPackageId(_id));
      navigate('/package/checkout');
    };

    const formatPrice = (price) => {
        if (price === t('free')) return price;
        if (price === 'Contact') return t('package.contact_us');

        return `${price.toLocaleString('vi-VN')}`;
    };

    const displayPrice = formatPrice(price);

    // Kiểm tra gói đang sử dụng
    const isCurrent = currentTeamPackage?.packageName === tag;

    return (
        <div className={`bg-white dark:bg-slate-800 rounded-lg border border-${borderColor} relative overflow-hidden flex flex-col h-full`}>
            {tag && (
                <div className={`w-full bg-${tagColor}-500 text-white p-4 text-center`}>
                    <div className="flex items-center justify-center gap-2">
                        {tag}
                        {/* {sub_tag && <span className="text-sm">{sub_tag}</span>} */}
                    </div>
                </div>
            )}
            <div className="p-6 flex flex-col flex-1">
                <div className="mb-6">
                    <span className={`text-sm text-${tagColor}-500 font-medium`}>{type}</span>
                    <div className="mt-4 flex items-baseline">
                        <span className="text-3xl font-bold dark:text-white">{displayPrice}</span>
                        {/* {price !== 'Contact' && price !== t('free') && (
                            <span className="text-slate-500 dark:text-slate-400 ml-1">
                                /{billingCycle === 'monthly' ? t('package.month') : t('package.year')}
                            </span>
                        )} */}
                    </div>
                </div>

                <div className="space-y-4 flex-1">
                    {features.map((feature, index) => (
                        <PackageFeature key={index} text={feature} />
                    ))}
                </div>

                {isCurrent ? (
                  <button
                    className="w-full mt-8 py-2 px-4 border border-blue-500 text-blue-500 bg-blue-50 dark:bg-blue-900 rounded-md font-semibold transition-colors cursor-not-allowed opacity-80"
                    disabled
                  >
                    {t('package.current_package')}
                  </button>
                ) : (
                  <button
                    onClick={handleClick}
                    className={`w-full mt-8 py-2 px-4 ${isActive
                        ? 'border border-blue-500 text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900'
                        : 'bg-blue-500 text-white hover:bg-blue-600'
                        } rounded-md transition-colors`}
                  >
                    {isActive ? t('package.current_plan') : t('package.select_plan')}
                  </button>
                )}
            </div>
        </div>
    );
};

export default PackageCard;