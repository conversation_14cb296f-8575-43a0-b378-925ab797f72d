import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import Dataset from "../knowledge/Dataset";
import { useGetDatasetListQuery } from "@/store/api/knowledge/dataApiSlice";
import { setDatasets } from "./store"
import Button from "@/components/ui/Button";
import AddDatasetModal from "../dataset/AddDataset"
import { useTranslation } from "react-i18next";


const KnowledgePage = () => {
  const { t } = useTranslation();
  const [page, setPage] = useState(1);
  const {data: listDataset, isLoading, isFetching, isError, error} = useGetDatasetListQuery({page, type: 3})
  const { datasets } = useSelector((state) => state.vectordb)
  const dispatch = useDispatch()
  const [hasMore, setHasMore] = useState(true)
  // Set up the SimpleBar scroll event listener
  const containRef = useRef(null);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (listDataset?.rows) {
      let data = [...datasets, ...listDataset.rows];
      if (page >= listDataset.totalPages) {
        setHasMore(false); // No more posts if less than limit returned
      }
      dispatch(setDatasets(data));
    }
  }, [listDataset]);

  useEffect(() => {
    const handleScroll = () => {
      if (window.innerHeight + window.scrollY >= document.documentElement.scrollHeight - 10 && hasMore) {
        setPage((prevPage) => prevPage + 1);
      }
    };
    // Attach the scroll event listener
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [isLoading, isFetching]);

  const closeModal = () => {
    setIsOpen(false)
  }

  const openModal = () => {
    setIsOpen(true)
  }

  return (
    <div>
      <div className="flex flex-wrap justify-between items-center mb-4">
        <h4 className="font-medium lg:text-2xl text-xl capitalize text-slate-900 inline-block ltr:pr-4 rtl:pl-4">
          Dataset VectorStore
        </h4>
        <div
          className="md:flex md:space-x-4 md:justify-end items-center rtl:space-x-reverse"
        >
          <Button
            icon="heroicons-outline:plus"
            text={t("dataset.add")}
            className="btn-dark dark:bg-slate-800  h-min text-sm font-normal"
            iconClass=" text-lg"
            onClick={() => openModal()}
          />
        </div>
      </div>
      <div ref={containRef} className="grid xl:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-5">
        {datasets.map((data, i) => (
          <Dataset data={data} type="vectordb" key={i}/>
        ))}
      </div>
      <AddDatasetModal isOpen={isOpen} closeModal={closeModal} datasetType={3}/>
    </div>
  )
}

export default KnowledgePage;