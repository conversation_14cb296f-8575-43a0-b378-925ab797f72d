import React, { useEffect } from "react";
import Modal from "@/components/ui/Modal";
import FormImportCsv from "./FormImportCsv";
import FormImportUrl from "./FormImportUrl";
import { useTranslation } from "react-i18next";

const ImportModal = ({data_id , isOpen, closeModal, importType= "csv"}) => {
  const { t } = useTranslation();
  return(
    <Modal
      title={t("dataset.edit") + " Knowledge"}
      activeModal={isOpen}
      onClose={closeModal}
    >
      {importType === "csv" && <FormImportCsv data_id={data_id} closeModal={closeModal} />}
      {importType === "url" && <FormImportUrl data_id={data_id} closeModal={closeModal} />}
    </Modal>
  )
}

export default ImportModal