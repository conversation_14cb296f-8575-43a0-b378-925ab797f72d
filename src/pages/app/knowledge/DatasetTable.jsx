import React, { useState, useMemo, useEffect } from "react";
import Card from "@/components/ui/Card";
import Icon from "@/components/ui/Icon";
import Tooltip from "@/components/ui/Tooltip";
import {
  useTable,
  useRowSelect,
  useSortBy,
  useGlobalFilter,
  usePagination,
} from "react-table";

import {
  useLazyGetKnowledgeQuery,
  useLazyDeleteKnowledgeQuery,
  useLazySearchKnowledgeQuery,
} from "@/store/api/knowledge/dataApiSlice";
import { toast } from "react-toastify";
import { useParams } from "react-router-dom";
import DataKnowledgeModal from "./DataKnowledgeModal";
import Loading from "@/components/Loading";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import DataQnaModal from "../qna/DataQnaModal";
import DataVectorModal from "../vectordb/DataVectorModal";



const COLUMNS = [
  {
    Header: "snapshot.action",
    accessor: "action",
    Cell: (props) => {
      return (
        <div className="flex space-x-3 rtl:space-x-reverse">
          <Tooltip content="Edit" placement="top" arrow animation="shift-away">
            <button className="action-btn" type="button" onClick={() => props.onEdit()}>
              <Icon icon="heroicons:pencil-square" />
            </button>
          </Tooltip>
          <Tooltip
            content="Delete"
            placement="top"
            arrow
            animation="shift-away"
            theme="danger"
          >
            <button className="action-btn" type="button" onClick={() => props.onDelete()}>
              <Icon icon="heroicons:trash" />
            </button>
          </Tooltip>
        </div>
      );
    },
  },
];

const DatasetTable = ({ dataType = "knowledge", dataset, customColumns, refresh = 0, onTotalChange }) => {
  const { t } = useTranslation();
  const { id } = useParams()
  const [data, setData] = useState([]);
  const [openModal, setOpenModal] = useState(false);
  const [reload, setReload] = useState(refresh);
  const [getKnowledge, { isLoading }] = useLazyGetKnowledgeQuery();
  const [deleteKnowledge] = useLazyDeleteKnowledgeQuery();
  const [selectKnowledge, setSelectKnowledge] = useState({});
  const [searchQuery] = useLazySearchKnowledgeQuery()
  const [totalPage, setTotalPage] = useState(1);

  const columns = useMemo(() => [...customColumns, ...COLUMNS], []);

  const {
    register,
    control,
    reset,
    formState: { errors },
    handleSubmit,
  } = useForm({
    mode: "all",
  });

  const tableInstance = useTable(
    {
      columns,
      data,
      manualPagination: true,
      pageCount: totalPage,
      autoResetPage: false,
    },

    useGlobalFilter,
    useSortBy,
    usePagination,
    useRowSelect,
  );
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    footerGroups,
    page,
    nextPage,
    previousPage,
    canNextPage,
    canPreviousPage,
    pageOptions,
    state,
    gotoPage,
    pageCount,
    setGlobalFilter,
    setPageSize,
    prepareRow,
  } = tableInstance;

  const { globalFilter, pageIndex, pageSize } = state;
  const toastConfig = {
    position: "top-right",
    autoClose: 1500,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    theme: "dark",
  }

  useEffect(() => {
    const fetchData = async () => {
      if (!!id) {
        const res = await getKnowledge({
          page: pageIndex + 1,
          dataset_id: id,
          page_size: pageSize
        }).unwrap();
        setData(res.data);
        setTotalPage(Math.ceil(res.total / pageSize));
        onTotalChange?.(res.total);
      }
    };
    fetchData();
  }, [id, pageIndex, pageSize, reload, refresh]);

  const handleCloseModal = () => {
    setReload(reload + 1);
    setOpenModal(false);
  };

  const onDelete = async (data) => {
    if (window.confirm("Do you want delete Knowledge?")) {
      const response = await deleteKnowledge({ ids: [data.id], dataset_id: id }).unwrap();

      if (response.error) {
        toast.error(response.error.data.message, toastConfig);
      } else {
        setReload(reload + 1);
        toast.info("Delete Successfully", toastConfig);
      }
    }
  }

  const searchVector = async (form) => {
    const response = await searchQuery({ search: form.query, dataset_id: id, fields: dataset.vectorConfig.fields })

    if (response.error) {
      toast.error(response.error.data.message, toastConfig);
    } else {
      setData(response.data)
      setTotalPage(1)
      toast.info(t("dataset.search_success"), toastConfig);
    }
  }

  return (
    <>
      {isLoading && <Loading />}
      {!isLoading && <Card>
        <div className="md:flex justify-between items-center mb-6">
          <h4 className="card-title">{t(`dataset.title_${dataType}`)}</h4>
          <form onSubmit={handleSubmit(searchVector)} className="max-w-md flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                <svg className="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                </svg>
              </div>
              <input type="search" id="default-search" className="block w-full p-4 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                placeholder="Search for knowledge" {...register('query')}
              />
              <button type="submit" className="text-white absolute end-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Test Question</button>
            </div>
          </form>
          <button className="btn btn-primary" onClick={handleCloseModal}>{t("dataset.clean_search_result")}</button>
        </div>
        <div className="overflow-x-auto -mx-6">
          <div className="inline-block min-w-full align-middle">
            <div className="overflow-hidden ">
              <table
                className="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700"
                {...getTableProps}
              >
                <thead className="bg-slate-200 dark:bg-slate-700">
                  {headerGroups.map((headerGroup) => (
                    <tr {...headerGroup.getHeaderGroupProps()}>
                      {headerGroup.headers.map((column) => (
                        <th
                          {...column.getHeaderProps(
                            column.getSortByToggleProps()
                          )}
                          scope="col"
                          className=" table-th "
                        >
                          {t(column.render("Header"))}
                          <span>
                            {column.isSorted
                              ? column.isSortedDesc
                                ? " 🔽"
                                : " 🔼"
                              : ""}
                          </span>
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody
                  className="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700"
                  {...getTableBodyProps}
                >
                  {page.map((row) => {
                    prepareRow(row);
                    return (
                      <tr {...row.getRowProps()}>
                        {row.cells.map((cell) => {
                          return (
                            <td {...cell.getCellProps()} className="table-td">
                              {cell.render("Cell", {
                                onDelete: () => {
                                  onDelete(cell.row.original)
                                },
                                onEdit: () => {
                                  setSelectKnowledge(cell.row.original)
                                  setOpenModal(true);
                                }
                              })}
                            </td>
                          );
                        })}
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div className="md:flex md:space-y-0 space-y-5 justify-between mt-6 items-center">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            {/* <select
              className="form-control py-2 w-max"
              value={pageSize}
              onChange={(e) => setPageSize(Number(e.target.value))}
            >
              {[10, 25, 50].map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  Show {pageSize}
                </option>
              ))}
            </select> */}
            <span className="text-sm font-medium text-slate-600 dark:text-slate-300">
              Page{" "}
              <span>
                {pageIndex + 1} of {pageOptions.length}
              </span>
            </span>
          </div>
          <ul className="flex items-center space-x-3 rtl:space-x-reverse">
            <li className="text-xl leading-4 text-slate-900 dark:text-white rtl:rotate-180">
              <button
                className={`${!canPreviousPage ? "opacity-50 cursor-not-allowed" : ""}`}
                onClick={() => gotoPage(0)}
                disabled={!canPreviousPage}
              >
                <Icon icon="heroicons:chevron-double-left-solid" />
              </button>
            </li>
            <li className="text-sm leading-4 text-slate-900 dark:text-white rtl:rotate-180">
              <button
                className={`${!canPreviousPage ? "opacity-50 cursor-not-allowed" : ""}`}
                onClick={() => previousPage()}
                disabled={!canPreviousPage}
              >
                Prev
              </button>
            </li>

            {/* First page */}
            {pageIndex > 2 && (
              <li>
                <button
                  className="bg-slate-100 dark:bg-slate-700 dark:text-slate-400 text-slate-900 font-normal text-sm rounded leading-[16px] flex h-6 w-6 items-center justify-center transition-all duration-150"
                  onClick={() => gotoPage(0)}
                >
                  1
                </button>
              </li>
            )}

            {/* Left ellipsis */}
            {pageIndex > 3 && (
              <li className="text-slate-900 dark:text-white">...</li>
            )}

            {/* Page numbers around current page */}
            {pageOptions.map((page, pageIdx) => {
              // Show pages within 2 positions of current page
              if (
                pageIdx === pageIndex ||
                pageIdx === pageIndex - 1 ||
                pageIdx === pageIndex - 2 ||
                pageIdx === pageIndex + 1 ||
                pageIdx === pageIndex + 2
              ) {
                return (
                  <li key={pageIdx}>
                    <button
                      className={`${pageIdx === pageIndex
                        ? "bg-slate-900 dark:bg-slate-600 dark:text-slate-200 text-white font-medium"
                        : "bg-slate-100 dark:bg-slate-700 dark:text-slate-400 text-slate-900 font-normal"
                        } text-sm rounded leading-[16px] flex h-6 w-6 items-center justify-center transition-all duration-150`}
                      onClick={() => gotoPage(pageIdx)}
                    >
                      {page + 1}
                    </button>
                  </li>
                );
              }
              return null;
            })}

            {/* Right ellipsis */}
            {pageIndex < pageOptions.length - 4 && (
              <li className="text-slate-900 dark:text-white">...</li>
            )}

            {/* Last page */}
            {pageIndex < pageOptions.length - 3 && (
              <li>
                <button
                  className="bg-slate-100 dark:bg-slate-700 dark:text-slate-400 text-slate-900 font-normal text-sm rounded leading-[16px] flex h-6 w-6 items-center justify-center transition-all duration-150"
                  onClick={() => gotoPage(pageOptions.length - 1)}
                >
                  {pageOptions.length}
                </button>
              </li>
            )}

            <li className="text-sm leading-4 text-slate-900 dark:text-white rtl:rotate-180">
              <button
                className={`${!canNextPage ? "opacity-50 cursor-not-allowed" : ""}`}
                onClick={() => nextPage()}
                disabled={!canNextPage}
              >
                Next
              </button>
            </li>
            <li className="text-xl leading-4 text-slate-900 dark:text-white rtl:rotate-180">
              <button
                onClick={() => gotoPage(pageCount - 1)}
                disabled={!canNextPage}
                className={`${!canNextPage ? "opacity-50 cursor-not-allowed" : ""}`}
              >
                <Icon icon="heroicons:chevron-double-right-solid" />
              </button>
            </li>
          </ul>
        </div>
        {/*end*/}
      </Card>}
      {dataType == "knowledge" && < DataKnowledgeModal
        knowledge={selectKnowledge} closeModal={handleCloseModal} isOpen={openModal} datasetId={id}
      />}
      {dataType == "qna" && < DataQnaModal
        knowledge={selectKnowledge} closeModal={handleCloseModal} isOpen={openModal} datasetId={id}
      />}
      {dataType == "vector_store" && < DataVectorModal
        knowledge={selectKnowledge} closeModal={handleCloseModal} isOpen={openModal} datasetId={id}
      />}
    </>
  );
};

export default DatasetTable;
