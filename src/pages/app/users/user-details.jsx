import Button from "@/components/ui/Button";
import FormGroup from "@/components/ui/FormGroup";
import Icon from "@/components/ui/Icon";
import Radio from "@/components/ui/Radio";
import Select from "@/components/ui/Select";
import { UserRole } from "@/constant/appContant.js";
import usePermission from "@/hooks/usePermission";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";
import {useLazyGetMeQuery, useLazyUpdateUserStatusQuery} from "@/store/api/auth/authApiSlice";
import Card from "@/components/ui/Card";
import Badge from "@/components/ui/Badge";


const UserDetails = () => {
  const { t } = useTranslation();
  const { id } = useParams()
  const [getUser, { isSuccess, isError }] = useLazyGetMeQuery();
  const navigate = useNavigate();
  const [editItem, setEditItem] = useState({});
  const [updateUserStatus, { isSuccess: updateSuccess, isError: updateError }] = useLazyUpdateUserStatusQuery();
  const isModerator = usePermission(undefined, UserRole.moderator);

  const toastConfig = {
    position: "top-right",
    autoClose: 1500,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    theme: "dark",
  }

  const {
    register,
    control,
    reset,
    formState: { errors },
    handleSubmit,
  } = useForm({
    mode: "all"
  });

  useEffect(() => {
    const fetchData = async () => {
      const res = await getUser(id).unwrap();
      setEditItem(res);
    };
    fetchData();
  }, [id]);

  useEffect(() => {
    if (isError) {
      navigate("/workbots");
    }
  }, [isError]) // redirect to workbot page when not found bot_id

  useEffect(() => {
    reset({...editItem, config: JSON.stringify(editItem.config)});
  }, [editItem]);

  const onSubmit = async (data) => {
    const response = await triggerQuery(data);
    if(response.isSuccess) {
      setEditItem(response.data);
      toast.info("Edit Successfully", toastConfig);
    } else {
      toast.error(response.error.message || response.error.data.message, toastConfig);
    }
  };

  const changeStatus = async (status) => {
    const response = await updateUserStatus({userId: id, status: status});
    if(response.isSuccess) {
      setEditItem(response.data);
      toast.info("Edit Successfully", toastConfig);
    } else {
      toast.error(response.error.message || response.error.data.message, toastConfig);
    }
  }



  return (
    <Card>
      <div className="flex justify-between items-center mb-5 space-x-2">
        <div>
          <span className="text-sm text-slate-600 mr-2">Status:</span>
          <Badge className=" font-normal text-[10px] bg-info-600 text-white ">
            {editItem?.status === 1 ? t("user.active") : t("user.inactive")}
          </Badge>
        </div>
        {/* <div>
          {editItem?.status === 0 && (
            <Button icon="heroicons-outline:lock-open" text={t("user.active")} className="btn-success" onClick={() => changeStatus(1)}></Button>
          )}
          {editItem?.status === 1 && (
            <Button icon="heroicons-outline:lock-closed" text={t("user.inactive")} className="btn-danger" onClick={() => changeStatus(0)}></Button>
          )}
        </div> */}
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 ">
        <FormGroup error={errors.name}>
          <label className="block capitalize form-label">{t("workbot.name")}</label>
          <input
            type="text"
            defaultValue={editItem.name}
            className="form-control py-2"
            {...register("name")}
            disabled
          />
        </FormGroup>
        <FormGroup error={errors.username}>
          <label className="block capitalize form-label">{t("username")}</label>
          <input
            type="text"
            defaultValue={editItem.username}
            className="form-control py-2"
            {...register("username")}
            disabled
          />
        </FormGroup>
        <FormGroup error={errors.role}>
          <label className="block capitalize form-label">{t("role")}</label>
          <Select
            defaultValue={editItem.role}
            options={Object.entries(UserRole).map((item) => ({ value: item[1], label: item[0] }))} // Convert enum values to optionsUserRole}
            register={register("role")}
            disabled
          />
        </FormGroup>

        <div className="ltr:text-right rtl:text-left">
          {/* <button className="btn btn-dark">{t("workbot.update")}</button> */}
        </div>
      </form>
    </Card>
  );
};

export default UserDetails;
