import React from "react";
// import images
import Twitter from "@/assets/images/icon/tw.svg";
import FaceBook from "@/assets/images/icon/fb.svg";
import LinkedIn from "@/assets/images/icon/in.svg";
import Google from "@/assets/images/icon/gp.svg";
import { GoogleAuthProvider, signInWithPopup } from "firebase/auth";
import { auth } from "@/configs/firebaseConfig.js";
import { useFirebaseLoginMutation } from "@/store/api/auth/authApiSlice.js";
import { parseResponseError } from "@/utils/requestUtils.js";
import { setUser } from "@/store/api/auth/authSlice.js";
import { toast } from "react-toastify";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";

const Social = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [firebaseLogin, { isLoading, isError, error, isSuccess }] = useFirebaseLoginMutation();
  const handleGoogleLogin = async () => {
    const provider = new GoogleAuthProvider();
    try {
      const result = await signInWithPopup(auth, provider);
      const user = result.user;

      console.log(`user.accessToken >>> `, user.accessToken);
      const response = await firebaseLogin({
        firebaseToken: user.accessToken,
      });
      console.log("🚀 ~ handleGoogleLogin ~ response:", response)

      const errorData = parseResponseError(response);
      if (errorData) {
        throw new Error(errorData.message);
      }

      if (!response.data.token) {
        toast.error(response.data.message);
        throw new Error("Invalid credentials");
      }

      dispatch(setUser({
        user: response.data.user,
        token: response.data.token,
      }));
      navigate("/dashboard");
      toast.success("Login Successful");
    } catch (error) {
      console.error("Error during login:", error);
      alert("Login failed. Please try again.");
    }
  };

  return (
    <ul className="flex">
      <li className="flex-1">
        <a
          href="#"
          className="inline-flex h-10 w-10 bg-[#1C9CEB] text-white text-2xl flex-col items-center justify-center rounded-full"
        >
          <img src={Twitter} alt="" />
        </a>
      </li>
      <li className="flex-1">
        <a
          href="#"
          className="inline-flex h-10 w-10 bg-[#395599] text-white text-2xl flex-col items-center justify-center rounded-full"
        >
          <img src={FaceBook} alt="" />
        </a>
      </li>
      <li className="flex-1">
        <a
          href="#"
          className="inline-flex h-10 w-10 bg-[#0A63BC] text-white text-2xl flex-col items-center justify-center rounded-full"
        >
          <img src={LinkedIn} alt="" />
        </a>
      </li>
      <li className="flex-1">
        <a
          onClick={handleGoogleLogin}
          className="inline-flex h-10 w-10 bg-[#EA4335] text-white text-2xl flex-col items-center justify-center rounded-full"
        >
          <img src={Google} alt="" />
        </a>
      </li>
    </ul>
  );
};

export default Social;
