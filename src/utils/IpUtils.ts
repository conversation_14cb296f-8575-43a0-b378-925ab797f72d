/**
 * Utility functions for IP address operations and validation
 */

import { IncomingHttpHeaders } from 'http';

export function isWhitelistProxy(ip: string, whitelist: string): boolean {
  if (!ip) return false;

  // Remove IPv6 prefix if present
  if (ip.startsWith('::ffff:')) {
    ip = ip.substring(7);
  }

  // Check for localhost
  if (ip === '127.0.0.1' || ip === 'localhost' || ip === '::1') {
    return true;
  }

  // Check if IP is in the environment variable whitelist
  if (whitelist) {
    const whitelistItems = whitelist.split(',').map((item) => item.trim());

    for (const item of whitelistItems) {
      // Check for CIDR notation (e.g., ***********/24)
      if (item.includes('/')) {
        if (isIpInCidrRange(ip, item)) {
          return true;
        }
      }
      // Check for exact IP match
      else if (ip === item) {
        return true;
      }
    }
  }

  // Fallback to checking private IP ranges
  const ipv4Segments = ip.split('.');
  if (ipv4Segments.length === 4) {
    const firstOctet = parseInt(ipv4Segments[0], 10);
    const secondOctet = parseInt(ipv4Segments[1], 10);

    // Check for 10.0.0.0/8
    if (firstOctet === 10) {
      return true;
    }

    // Check for **********/12
    if (firstOctet === 172 && secondOctet >= 16 && secondOctet <= 31) {
      return true;
    }

    // Check for ***********/16
    if (firstOctet === 192 && secondOctet === 168) {
      return true;
    }

    // Check for **********/10 (Carrier-grade NAT)
    if (firstOctet === 100 && secondOctet >= 64 && secondOctet <= 127) {
      return true;
    }

    // Check for ***********/16 (Link-local)
    if (firstOctet === 169 && secondOctet === 254) {
      return true;
    }
  }

  return false;
}

export function isIpInCidrRange(ip: string, cidr: string): boolean {
  try {
    const [rangeIp, prefixStr] = cidr.split('/');
    const prefix = parseInt(prefixStr, 10);

    if (isNaN(prefix) || prefix < 0 || prefix > 32) {
      return false;
    }

    // Convert IP addresses to numeric values
    const ipNum = ipToNumber(ip);
    const rangeIpNum = ipToNumber(rangeIp);

    if (ipNum === null || rangeIpNum === null) {
      return false;
    }

    // Calculate the bit mask from the prefix
    const mask = ~((1 << (32 - prefix)) - 1);

    // Check if the IP is in the range
    return (ipNum & mask) === (rangeIpNum & mask);
  } catch (error) {
    return false;
  }
}

/**
 * Converts an IP address to a numeric value
 *
 * @param ip The IP address to convert
 * @returns The numeric value of the IP address, or null if invalid
 */
export function ipToNumber(ip: string): number | null {
  try {
    const segments = ip.split('.');

    if (segments.length !== 4) {
      return null;
    }

    return (
      ((parseInt(segments[0], 10) << 24) |
        (parseInt(segments[1], 10) << 16) |
        (parseInt(segments[2], 10) << 8) |
        parseInt(segments[3], 10)) >>>
      0
    ); // Unsigned right shift to handle negative numbers
  } catch (error) {
    return null;
  }
}

export function getRealIp(directIp: string, headers: IncomingHttpHeaders, whitelist: string) {
  // Get client IP with priority for Cloudflare's cf-connecting-ip header
  let ipOverProxy = headers['cf-connecting-ip'] as string;
  let ipSource = 'cf-connecting-ip';

  // Fallback to x-forwarded-for if cf-connecting-ip is not available
  if (!ipOverProxy && headers['x-forwarded-for']) {
    ipOverProxy = headers['x-forwarded-for'] as string;
    ipSource = 'x-forwarded-for';
    // If x-forwarded-for contains multiple IPs (comma-separated), take the first one
    if (ipOverProxy && ipOverProxy.includes(',')) {
      ipOverProxy = ipOverProxy.split(',')[0].trim();
      ipSource = 'x-forwarded-for (first IP)';
    }
  }

  // If the direct IP is whitelisted, trust the proxy headers
  if (ipOverProxy && isWhitelistProxy(directIp, whitelist)) {
    return ipOverProxy;
  } else {
    return directIp;
  }
}
