import fs from 'fs';
import crypto from 'crypto';

function sleep(miliSecond: number): Promise<void> {
  return new Promise((resolve) => {
    setTimeout(() => resolve(), miliSecond);
  });
}

/**
 *
 * @param url
 * @param params {object}
 * @return {string|*}
 */
function serializeUrl(url: string, params: string[][] = null): string {
  if (params) {
    return url + '?' + new URLSearchParams(params).toString();
  }
  return url;
}

function getDataFile(fileName: string): Promise<string> {
  return new Promise((resolve, reject) => {
    fs.readFile(fileName, 'utf8', (err, data) => {
      if (err) {
        reject(err);
      } else {
        resolve(data);
      }
    });
  });
}

function roundDecimal(value: number): number {
  return Math.round(value * 1000) / 1000;
}

/**
 * Save data to file
 * @param fileName {string}
 * @param text {string}
 * @param flags{'w'|'a'}
 * @return {Promise<unknown>}
 */
function saveDataToFile(fileName: string, text: string, flags: 'w' | 'a' = 'w') {
  return new Promise<void>((resolve, reject) => {
    const fileStream = fs.createWriteStream(fileName, {
      flags, // 'a' means appending (old data will be preserved)
    });
    fileStream.write(text, (error) => {
      fileStream.close();
      if (error) {
        reject(error);
      } else {
        resolve();
      }
    });
  });
}

/**
 * Get sub data of object
 * @param data {object}
 * @param subFields {string} subfield separate with dot(.) character. Ex: data.profile
 */
function getSubValue(data: { [key: string]: any }, subFields: string) {
  return subFields.split('.').reduce((previousValue, currentValue) => {
    if (previousValue && currentValue in previousValue) {
      return previousValue[currentValue];
    }
    return null;
  }, data);
}

/**
 * Set value from object by path key
 * path can allow: b.varB[0].varC b.varB.0.varC
 */
function setNestedValue(obj: any, path: string, value: any, merge = false) {
  const keys = path
    .replace(/\[(\d+)\]/g, '.$1') // Replace array indices with dot notation
    .split('.'); // Split the string into keys

  const lastKey = keys.pop(); // Get the last key
  const nestedObj = keys.reduce((acc, key) => {
    // Create nested objects or arrays if they don't exist
    if (!acc[key]) {
      // @ts-ignore
      acc[key] = isNaN(Number(keys[key + 1])) ? {} : [];
    }
    return acc[key];
  }, obj);

  if (merge && typeof nestedObj[lastKey] === 'object' && typeof value === 'object') {
    // If merge option is true and both are objects, merge them
    nestedObj[lastKey] = { ...nestedObj[lastKey], ...value };
  } else {
    // Otherwise, set the value directly
    nestedObj[lastKey] = value;
  }
}

/**
 * Get value from path
 */
function getNestedValue(obj: any, path: string) {
  const keys = path
    .replace(/\[(\d+)\]/g, '.$1') // Replace array indices with dot notation
    .split('.'); // Split the path string into keys

  return keys.reduce((acc, key) => {
    if (acc && typeof acc === 'object') {
      return acc[key]; // Traverse through the object or array
    }
    return undefined; // Return undefined if any part of the path is invalid
  }, obj);
}

/**
 * Extract primary and nested key from path
 * Ex: b.varB[0].varC . // Output: {'b', 'varB[0].varC'}
 */
function extractNestedPath(path: string): { primaryKey: string; nestedPath: string } {
  // Find the index of the first dot character
  const dotIndex = path.indexOf('.');

  // If there's no dot, return the whole path as the primary key and an empty nestedPath
  if (dotIndex === -1) {
    return { primaryKey: path, nestedPath: '' };
  }

  // Extract the primary key and the nested path
  const primaryKey = path.substring(0, dotIndex);
  const nestedPath = path.substring(dotIndex + 1);

  return { primaryKey, nestedPath };
}

function generateCustomHashUUID(data: any) {
  let normalizedHash = '';
  if (typeof data === 'string') {
    normalizedHash = crypto.createHash('md5').update(data).digest('hex');
  } else {
    normalizedHash = crypto.createHash('md5').update(JSON.stringify(data)).digest('hex');
  }
  // Insert the UUID structure
  return [
    normalizedHash.slice(0, 8),
    normalizedHash.slice(8, 12),
    '4' + normalizedHash.slice(13, 16), // UUID version 4
    ((parseInt(normalizedHash[16], 16) & 0x3) | 0x8).toString(16) + normalizedHash.slice(17, 20), // Variant
    normalizedHash.slice(20),
  ].join('-');
}

const CommonUtils = {
  sleep,
  serializeUrl,
  getDataFile,
  roundDecimal,
  saveDataToFile,
  getSubValue,
  setNestedValue,
  getNestedValue,
  extractNestedPath,
  generateCustomHashUUID,
};
export default CommonUtils;
