
function sleep$1(o) {
  return new Promise(e => setTimeout(e, o))
}

async function imageInfoOfBase64(o) {
  const e = await bufferFromBase64(o);
  return imageInfo(e)
}
async function bufferFromBase64(o) {
  const e = o.replace(/^data:image\/\w+;base64,/, "");
  return buffer.Buffer.from(e, "base64")
}
function renameLocator(o) {
  return o.replace("_midscene_retrieve_task_id", "_scrapersensei_retrieve_task_id")
}
function renameBackLocator(o) {
  return o.replace("_scrapersensei_retrieve_task_id", "_midscene_retrieve_task_id")
}
const baseUrl = "https://api.scrapersensei.com";
async function plan(o, e) {
  const n = {
    openaiApiKey: await chrome.storage.local.get("settings").then(c => c.settings.openaiApiKey),
    fastMode: await chrome.storage.local.get("settings").then(c => c.settings.fastMode)
  }
    , a = await fetch(`${baseUrl}/plan`, {
    method: "POST",
    body: JSON.stringify({
      fastMode: n.fastMode,
      prompt: o,
      context: {
        ...e,
        screenshotBase64: n.fastMode ? "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==" : e.screenshotBase64,
        content: e.content.map(c => ({
          id: c.id,
          rect: c.rect,
          locator: renameLocator(c.locator),
          content: c.content,
          attributes: c.attributes,
          indexId: c.indexId
        }))
      },
      settings: n
    })
  });
  if (!a.ok) {
    const {error: c} = await a.json().catch( () => ({
      error: "Unknown error"
    }));
    throw new Error(`Failed to plan: ${c}`)
  }
  return await a.json()
}
async function locate(o, e, n) {
  const a = {
    openaiApiKey: await chrome.storage.local.get("settings").then(u => u.settings.openaiApiKey)
  }
    , c = await fetch(`${baseUrl}/locate`, {
    method: "POST",
    body: JSON.stringify({
      prompt: o,
      settings: a,
      quickAnswer: n,
      context: {
        ...e,
        content: e.content.map(u => ({
          id: u.id,
          rect: u.rect,
          locator: renameLocator(u.locator),
          content: u.content,
          attributes: u.attributes,
          indexId: u.indexId
        }))
      }
    })
  });
  if (!c.ok) {
    const {error: u} = await c.json().catch( () => ({
      error: "Unknown error"
    }));
    throw new Error(`Failed to locate element: ${u}`)
  }
  const l = await c.json();
  return {
    ...l,
    locator: l.locator ? renameBackLocator(l.locator) : void 0
  }
}
async function assertApi(o, e) {
  const n = {
    openaiApiKey: await chrome.storage.local.get("settings").then(c => c.settings.openaiApiKey)
  }
    , a = await fetch(`${baseUrl}/assert`, {
    method: "POST",
    body: JSON.stringify({
      prompt: o,
      settings: n,
      context: {
        ...e,
        content: e.content.map(c => ({
          id: c.id,
          rect: c.rect,
          locator: renameLocator(c.locator),
          content: c.content,
          attributes: c.attributes,
          indexId: c.indexId
        }))
      }
    })
  });
  if (!a.ok)
    throw new Error(`Failed to assert: ${o}`);
  return await a.json()
}
async function extract(o, e) {
  const n = {
    openaiApiKey: await chrome.storage.local.get("settings").then(c => c.settings.openaiApiKey)
  }
    , a = await fetch(`${baseUrl}/query`, {
    method: "POST",
    body: JSON.stringify({
      prompt: o,
      settings: n,
      context: {
        ...e,
        content: e.content.map(c => ({
          id: c.id,
          rect: c.rect,
          locator: renameLocator(c.locator),
          content: c.content,
          attributes: c.attributes,
          indexId: c.indexId
        }))
      }
    })
  });
  if (!a.ok)
    throw new Error(`Failed to extract: ${o}`);
  return await a.json()
}
class Executor {
  constructor(e, n, a) {
    xr(this, "name");
    xr(this, "description");
    xr(this, "tasks");
    xr(this, "status");
    this.status = a && a.length > 0 ? "pending" : "init",
      this.name = e,
      this.description = n,
      this.tasks = (a || []).map(c => this.markTaskAsPending(c))
  }
  markTaskAsPending(e) {
    return {
      status: "pending",
      ...e
    }
  }
  async append(e) {
    invariant(this.status !== "error", "executor is in error state, cannot append task"),
      Array.isArray(e) ? this.tasks.push(...e.map(n => this.markTaskAsPending(n))) : this.tasks.push(this.markTaskAsPending(e)),
    this.status !== "running" && (this.status = "pending")
  }
  async flush() {
    this.status === "init" && this.tasks.length > 0 && console.warn("illegal state for executor, status is init but tasks are not empty"),
      invariant(this.status !== "running", "executor is already running"),
      invariant(this.status !== "completed", "executor is already completed"),
      invariant(this.status !== "error", "executor is in error state");
    const e = this.tasks.findIndex(l => l.status === "pending");
    if (e < 0)
      return;
    this.status = "running";
    let n = e, a = !0, c;
    for (; n < this.tasks.length; ) {
      const l = this.tasks[n];
      invariant(l.status === "pending", `task status should be pending, but got: ${l.status}`),
        l.timing = {
          start: Date.now()
        };
      try {
        l.status = "running",
          invariant(["Insight", "Action", "Planning"].indexOf(l.type) >= 0, `unsupported task type: ${l.type}`);
        const {executor: u, param: h} = l;
        invariant(u, `executor is required for task type: ${l.type}`);
        let p;
        const b = {
          task: l,
          element: c == null ? void 0 : c.element
        };
        l.type === "Insight" ? (invariant(l.subType === "Locate" || l.subType === "Query" || l.subType === "Assert", `unsupported insight subType: ${l.subType}`),
          p = await l.executor(h, b),
        l.subType === "Locate" && (c = p == null ? void 0 : p.output)) : l.type === "Action" || l.type === "Planning" ? p = await l.executor(h, b) : (console.warn(`unsupported task type: ${l.type}, will try to execute it directly`),
          p = await l.executor(h, b)),
          Object.assign(l, p),
          l.status = "finished",
          l.timing.end = Date.now(),
          l.timing.cost = l.timing.end - l.timing.start,
          n++
      } catch (u) {
        a = !1,
          l.error = (u == null ? void 0 : u.message) || "error-without-message",
          l.errorStack = u.stack,
          l.status = "failed",
          l.timing.end = Date.now(),
          l.timing.cost = l.timing.end - l.timing.start;
        break
      }
    }
    for (let l = n + 1; l < this.tasks.length; l++)
      this.tasks[l].status = "cancelled";
    if (a ? this.status = "completed" : this.status = "error",
      this.tasks.length) {
      const l = Math.min(n, this.tasks.length - 1);
      return this.tasks[l].output
    }
  }
  isInErrorState() {
    return this.status === "error"
  }
  latestErrorTask() {
    if (this.status !== "error")
      return null;
    const e = this.tasks.findIndex(n => n.status === "failed");
    return e >= 0 ? this.tasks[e] : null
  }
  dump() {
    return {
      sdkVersion: "-",
      logTime: Date.now(),
      name: this.name,
      description: this.description,
      tasks: this.tasks
    }
  }
}
var dayjs_min = {
  exports: {}
};
(function(o, e) {
    (function(n, a) {
        o.exports = a()
      }
    )(commonjsGlobal$1, function() {
      var n = 1e3
        , a = 6e4
        , c = 36e5
        , l = "millisecond"
        , u = "second"
        , h = "minute"
        , p = "hour"
        , b = "day"
        , w = "week"
        , T = "month"
        , A = "quarter"
        , I = "year"
        , D = "date"
        , N = "Invalid Date"
        , P = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/
        , L = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g
        , q = {
        name: "en",
        weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),
        months: "January_February_March_April_May_June_July_August_September_October_November_December".split("_"),
        ordinal: function(v) {
          var y = ["th", "st", "nd", "rd"]
            , k = v % 100;
          return "[" + v + (y[(k - 20) % 10] || y[k] || y[0]) + "]"
        }
      }
        , Z = function(v, y, k) {
        var $ = String(v);
        return !$ || $.length >= y ? v : "" + Array(y + 1 - $.length).join(k) + v
      }
        , J = {
        s: Z,
        z: function(v) {
          var y = -v.utcOffset()
            , k = Math.abs(y)
            , $ = Math.floor(k / 60)
            , O = k % 60;
          return (y <= 0 ? "+" : "-") + Z($, 2, "0") + ":" + Z(O, 2, "0")
        },
        m: function v(y, k) {
          if (y.date() < k.date())
            return -v(k, y);
          var $ = 12 * (k.year() - y.year()) + (k.month() - y.month())
            , O = y.clone().add($, T)
            , S = k - O < 0
            , m = y.clone().add($ + (S ? -1 : 1), T);
          return +(-($ + (k - O) / (S ? O - m : m - O)) || 0)
        },
        a: function(v) {
          return v < 0 ? Math.ceil(v) || 0 : Math.floor(v)
        },
        p: function(v) {
          return {
            M: T,
            y: I,
            w,
            d: b,
            D,
            h: p,
            m: h,
            s: u,
            ms: l,
            Q: A
          }[v] || String(v || "").toLowerCase().replace(/s$/, "")
        },
        u: function(v) {
          return v === void 0
        }
      }
        , ae = "en"
        , ce = {};
      ce[ae] = q;
      var oe = "$isDayjsObject"
        , Q = function(v) {
        return v instanceof d || !(!v || !v[oe])
      }
        , te = function v(y, k, $) {
        var O;
        if (!y)
          return ae;
        if (typeof y == "string") {
          var S = y.toLowerCase();
          ce[S] && (O = S),
          k && (ce[S] = k,
            O = S);
          var m = y.split("-");
          if (!O && m.length > 1)
            return v(m[0])
        } else {
          var x = y.name;
          ce[x] = y,
            O = x
        }
        return !$ && O && (ae = O),
        O || !$ && ae
      }
        , ve = function(v, y) {
        if (Q(v))
          return v.clone();
        var k = typeof y == "object" ? y : {};
        return k.date = v,
          k.args = arguments,
          new d(k)
      }
        , C = J;
      C.l = te,
        C.i = Q,
        C.w = function(v, y) {
          return ve(v, {
            locale: y.$L,
            utc: y.$u,
            x: y.$x,
            $offset: y.$offset
          })
        }
      ;
      var d = function() {
        function v(k) {
          this.$L = te(k.locale, null, !0),
            this.parse(k),
            this.$x = this.$x || k.x || {},
            this[oe] = !0
        }
        var y = v.prototype;
        return y.parse = function(k) {
          this.$d = function($) {
            var O = $.date
              , S = $.utc;
            if (O === null)
              return new Date(NaN);
            if (C.u(O))
              return new Date;
            if (O instanceof Date)
              return new Date(O);
            if (typeof O == "string" && !/Z$/i.test(O)) {
              var m = O.match(P);
              if (m) {
                var x = m[2] - 1 || 0
                  , F = (m[7] || "0").substring(0, 3);
                return S ? new Date(Date.UTC(m[1], x, m[3] || 1, m[4] || 0, m[5] || 0, m[6] || 0, F)) : new Date(m[1],x,m[3] || 1,m[4] || 0,m[5] || 0,m[6] || 0,F)
              }
            }
            return new Date(O)
          }(k),
            this.init()
        }
          ,
          y.init = function() {
            var k = this.$d;
            this.$y = k.getFullYear(),
              this.$M = k.getMonth(),
              this.$D = k.getDate(),
              this.$W = k.getDay(),
              this.$H = k.getHours(),
              this.$m = k.getMinutes(),
              this.$s = k.getSeconds(),
              this.$ms = k.getMilliseconds()
          }
          ,
          y.$utils = function() {
            return C
          }
          ,
          y.isValid = function() {
            return this.$d.toString() !== N
          }
          ,
          y.isSame = function(k, $) {
            var O = ve(k);
            return this.startOf($) <= O && O <= this.endOf($)
          }
          ,
          y.isAfter = function(k, $) {
            return ve(k) < this.startOf($)
          }
          ,
          y.isBefore = function(k, $) {
            return this.endOf($) < ve(k)
          }
          ,
          y.$g = function(k, $, O) {
            return C.u(k) ? this[$] : this.set(O, k)
          }
          ,
          y.unix = function() {
            return Math.floor(this.valueOf() / 1e3)
          }
          ,
          y.valueOf = function() {
            return this.$d.getTime()
          }
          ,
          y.startOf = function(k, $) {
            var O = this
              , S = !!C.u($) || $
              , m = C.p(k)
              , x = function(ue, U) {
              var M = C.w(O.$u ? Date.UTC(O.$y, U, ue) : new Date(O.$y,U,ue), O);
              return S ? M : M.endOf(b)
            }
              , F = function(ue, U) {
              return C.w(O.toDate()[ue].apply(O.toDate("s"), (S ? [0, 0, 0, 0] : [23, 59, 59, 999]).slice(U)), O)
            }
              , Y = this.$W
              , le = this.$M
              , de = this.$D
              , j = "set" + (this.$u ? "UTC" : "");
            switch (m) {
              case I:
                return S ? x(1, 0) : x(31, 11);
              case T:
                return S ? x(1, le) : x(0, le + 1);
              case w:
                var H = this.$locale().weekStart || 0
                  , ne = (Y < H ? Y + 7 : Y) - H;
                return x(S ? de - ne : de + (6 - ne), le);
              case b:
              case D:
                return F(j + "Hours", 0);
              case p:
                return F(j + "Minutes", 1);
              case h:
                return F(j + "Seconds", 2);
              case u:
                return F(j + "Milliseconds", 3);
              default:
                return this.clone()
            }
          }
          ,
          y.endOf = function(k) {
            return this.startOf(k, !1)
          }
          ,
          y.$set = function(k, $) {
            var O, S = C.p(k), m = "set" + (this.$u ? "UTC" : ""), x = (O = {},
              O[b] = m + "Date",
              O[D] = m + "Date",
              O[T] = m + "Month",
              O[I] = m + "FullYear",
              O[p] = m + "Hours",
              O[h] = m + "Minutes",
              O[u] = m + "Seconds",
              O[l] = m + "Milliseconds",
              O)[S], F = S === b ? this.$D + ($ - this.$W) : $;
            if (S === T || S === I) {
              var Y = this.clone().set(D, 1);
              Y.$d[x](F),
                Y.init(),
                this.$d = Y.set(D, Math.min(this.$D, Y.daysInMonth())).$d
            } else
              x && this.$d[x](F);
            return this.init(),
              this
          }
          ,
          y.set = function(k, $) {
            return this.clone().$set(k, $)
          }
          ,
          y.get = function(k) {
            return this[C.p(k)]()
          }
          ,
          y.add = function(k, $) {
            var O, S = this;
            k = Number(k);
            var m = C.p($)
              , x = function(le) {
              var de = ve(S);
              return C.w(de.date(de.date() + Math.round(le * k)), S)
            };
            if (m === T)
              return this.set(T, this.$M + k);
            if (m === I)
              return this.set(I, this.$y + k);
            if (m === b)
              return x(1);
            if (m === w)
              return x(7);
            var F = (O = {},
              O[h] = a,
              O[p] = c,
              O[u] = n,
              O)[m] || 1
              , Y = this.$d.getTime() + k * F;
            return C.w(Y, this)
          }
          ,
          y.subtract = function(k, $) {
            return this.add(-1 * k, $)
          }
          ,
          y.format = function(k) {
            var $ = this
              , O = this.$locale();
            if (!this.isValid())
              return O.invalidDate || N;
            var S = k || "YYYY-MM-DDTHH:mm:ssZ"
              , m = C.z(this)
              , x = this.$H
              , F = this.$m
              , Y = this.$M
              , le = O.weekdays
              , de = O.months
              , j = O.meridiem
              , H = function(U, M, B, ee) {
                return U && (U[M] || U($, S)) || B[M].slice(0, ee)
              }
              , ne = function(U) {
                return C.s(x % 12 || 12, U, "0")
              }
              , ue = j || function(U, M, B) {
                var ee = U < 12 ? "AM" : "PM";
                return B ? ee.toLowerCase() : ee
              }
            ;
            return S.replace(L, function(U, M) {
              return M || function(B) {
                switch (B) {
                  case "YY":
                    return String($.$y).slice(-2);
                  case "YYYY":
                    return C.s($.$y, 4, "0");
                  case "M":
                    return Y + 1;
                  case "MM":
                    return C.s(Y + 1, 2, "0");
                  case "MMM":
                    return H(O.monthsShort, Y, de, 3);
                  case "MMMM":
                    return H(de, Y);
                  case "D":
                    return $.$D;
                  case "DD":
                    return C.s($.$D, 2, "0");
                  case "d":
                    return String($.$W);
                  case "dd":
                    return H(O.weekdaysMin, $.$W, le, 2);
                  case "ddd":
                    return H(O.weekdaysShort, $.$W, le, 3);
                  case "dddd":
                    return le[$.$W];
                  case "H":
                    return String(x);
                  case "HH":
                    return C.s(x, 2, "0");
                  case "h":
                    return ne(1);
                  case "hh":
                    return ne(2);
                  case "a":
                    return ue(x, F, !0);
                  case "A":
                    return ue(x, F, !1);
                  case "m":
                    return String(F);
                  case "mm":
                    return C.s(F, 2, "0");
                  case "s":
                    return String($.$s);
                  case "ss":
                    return C.s($.$s, 2, "0");
                  case "SSS":
                    return C.s($.$ms, 3, "0");
                  case "Z":
                    return m
                }
                return null
              }(U) || m.replace(":", "")
            })
          }
          ,
          y.utcOffset = function() {
            return 15 * -Math.round(this.$d.getTimezoneOffset() / 15)
          }
          ,
          y.diff = function(k, $, O) {
            var S, m = this, x = C.p($), F = ve(k), Y = (F.utcOffset() - this.utcOffset()) * a, le = this - F, de = function() {
              return C.m(m, F)
            };
            switch (x) {
              case I:
                S = de() / 12;
                break;
              case T:
                S = de();
                break;
              case A:
                S = de() / 3;
                break;
              case w:
                S = (le - Y) / 6048e5;
                break;
              case b:
                S = (le - Y) / 864e5;
                break;
              case p:
                S = le / c;
                break;
              case h:
                S = le / a;
                break;
              case u:
                S = le / n;
                break;
              default:
                S = le
            }
            return O ? S : C.a(S)
          }
          ,
          y.daysInMonth = function() {
            return this.endOf(T).$D
          }
          ,
          y.$locale = function() {
            return ce[this.$L]
          }
          ,
          y.locale = function(k, $) {
            if (!k)
              return this.$L;
            var O = this.clone()
              , S = te(k, $, !0);
            return S && (O.$L = S),
              O
          }
          ,
          y.clone = function() {
            return C.w(this.$d, this)
          }
          ,
          y.toDate = function() {
            return new Date(this.valueOf())
          }
          ,
          y.toJSON = function() {
            return this.isValid() ? this.toISOString() : null
          }
          ,
          y.toISOString = function() {
            return this.$d.toISOString()
          }
          ,
          y.toString = function() {
            return this.$d.toUTCString()
          }
          ,
          v
      }()
        , _ = d.prototype;
      return ve.prototype = _,
        [["$ms", l], ["$s", u], ["$m", h], ["$H", p], ["$W", b], ["$M", T], ["$y", I], ["$D", D]].forEach(function(v) {
          _[v[1]] = function(y) {
            return this.$g(y, v[0], v[1])
          }
        }),
        ve.extend = function(v, y) {
          return v.$i || (v(y, d, ve),
            v.$i = !0),
            ve
        }
        ,
        ve.locale = te,
        ve.isDayjs = Q,
        ve.unix = function(v) {
          return ve(1e3 * v)
        }
        ,
        ve.en = ce[ae],
        ve.Ls = ce,
        ve.p = {},
        ve
    })
  }
)(dayjs_min);
var dayjs_minExports = dayjs_min.exports;
const dayjs = getDefaultExportFromCjs$1(dayjs_minExports);
class WebElementInfo {
  constructor({content: e, rect: n, page: a, locator: c, id: l, attributes: u, indexId: h}) {
    xr(this, "content");
    xr(this, "locator");
    xr(this, "rect");
    xr(this, "center");
    xr(this, "page");
    xr(this, "id");
    xr(this, "indexId");
    xr(this, "attributes");
    this.content = e,
      this.rect = n,
      this.center = [Math.floor(n.left + n.width / 2), Math.floor(n.top + n.height / 2)],
      this.page = a,
      this.locator = c,
      this.id = l,
      this.attributes = u,
      this.indexId = h
  }
}
async function parseContextFromWebPage(o, e) {
  invariant(o, "page is required");
  const n = o.url()
    , c = `data:image/png;base64,${(await o.screenshot()).toString("base64")}`
    , l = await o.getElementInfos()
    , u = await alignElements(l, o);
  u.filter(p => p.attributes.nodeType !== "TEXT Node");
  const h = await imageInfoOfBase64(c);
  return {
    content: u,
    size: h,
    screenshotBase64: c,
    url: n
  }
}
async function getExtraReturnLogic() {
  return `${globalThis.element_inspector}element_inspector.webExtractTextWithPosition()`
}
const sizeThreshold = 3;
async function alignElements(o, e) {
  const n = o.filter(c => c.rect.height >= sizeThreshold && c.rect.width >= sizeThreshold)
    , a = [];
  for (const c of n) {
    const {rect: l, id: u, content: h, attributes: p, locator: b, indexId: w} = c;
    a.push(new WebElementInfo({
      rect: l,
      locator: b,
      id: u,
      content: h,
      attributes: p,
      page: e,
      indexId: w
    }))
  }
  return a
}
function reportFileName(o="web") {
  const e = dayjs().format("YYYY-MM-DD_HH-mm-ss-SSS");
  return `${o}-${e}`
}
function getCurrentExecutionFile(o) {
  const n = new Error().stack
    , a = process.cwd() || "";
  if (n) {
    const c = n.split(`
`);
    for (const l of c)
      if (l.includes(".spec.") || l.includes(".test.") || l.includes(".ts") || l.includes(".js")) {
        const u = l.match(/(?:at\s+)?(.*?\.(?:spec|test)\.[jt]s)/);
        if (u != null && u[1])
          return u[1].replace(a, "").trim().replace("at ", "")
      }
  }
  return !1
}
const testFileIndex = new Map;
function generateCacheId(o) {
  let e = o || getCurrentExecutionFile();
  if (e || (e = crypto.randomUUID(),
    console.warn("Midscene - using random UUID for cache id. Cache may be invalid.")),
    testFileIndex.has(e)) {
    const n = testFileIndex.get(e);
    n !== void 0 && testFileIndex.set(e, n + 1)
  } else
    testFileIndex.set(e, 1);
  return `${e}-${testFileIndex.get(e)}`
}
class TaskCache {
  constructor(e) {
    xr(this, "cache");
    xr(this, "cacheId");
    xr(this, "newCache");
    this.cacheId = generateCacheId(e == null ? void 0 : e.fileName),
      this.cache = this.readCacheFromFile() || {
        aiTasks: []
      },
      this.newCache = {
        aiTasks: []
      }
  }
  getCacheGroupByPrompt(e) {
    const {aiTasks: n=[]} = this.cache || {
      aiTasks: []
    }
      , a = n.findIndex(l => l.prompt === e)
      , c = [];
    return this.newCache.aiTasks.push({
      prompt: e,
      tasks: c
    }),
      {
        readCache: (l, u, h) => a === -1 ? !1 : u === "plan" ? this.readCache(l, u, h, n[a].tasks) : this.readCache(l, u, h, n[a].tasks),
        saveCache: l => {
          c.push(l),
            this.writeCacheToFile()
        }
      }
  }
  readCache(e, n, a, c) {
    var l;
    if (c.length > 0) {
      const u = c.findIndex(p => p.prompt === a);
      if (u === -1)
        return !1;
      const h = c.splice(u, 1)[0];
      if ((h == null ? void 0 : h.type) === "locate" && !((l = h.response) != null && l.elements.every(p => e.content.findIndex(w => w.id === p.id) !== -1)))
        return !1;
      if (h && h.type === n && h.prompt === a && this.pageContextEqual(h.pageContext, e))
        return h.response
    }
    return !1
  }
  pageContextEqual(e, n) {
    return e.size.width === n.size.width && e.size.height === n.size.height
  }
  generateTaskCache() {
    return this.newCache
  }
  readCacheFromFile() {}
  writeCacheToFile() {}
}
class PageTaskExecutor {
  constructor(e, n) {
    xr(this, "page");
    xr(this, "taskCache");
    this.page = e,
      this.taskCache = new TaskCache({
        fileName: n == null ? void 0 : n.cacheId
      })
  }
  async recordScreenshot(e) {
    return {
      type: "screenshot",
      ts: Date.now(),
      timing: e
    }
  }
  wrapExecutorWithScreenshot(e) {
    return {
      ...e,
      executor: async (a, c, ...l) => {
        const u = []
          , {task: h} = c;
        h.recorder = u;
        const p = await this.recordScreenshot(`before ${h.type}`);
        u.push(p);
        const b = await e.executor(a, c, ...l);
        if (e.type === "Action") {
          await sleep$1(1e3);
          const w = await this.recordScreenshot("after Action");
          u.push(w)
        }
        return b
      }
    }
  }
  async convertPlanToExecutable(e, n) {
    return e.map(c => {
        if (c.type === "Locate")
          return {
            type: "Insight",
            subType: "Locate",
            param: c.param,
            executor: async (u, h) => {
              const {task: p} = h;
              let b;
              const w = await parseContextFromWebPage(this.page)
                , T = n == null ? void 0 : n.readCache(w, "locate", u.prompt)
                , A = await locate(u.prompt, w, c.quickAnswer);
              if (!A)
                throw p.log = {
                  dump: b
                },
                  new Error(`Element not found: ${u.prompt}`);
              return {
                output: {
                  element: A
                },
                log: {
                  dump: b
                },
                cache: {
                  hit: !!T
                }
              }
            }
          };
        if (c.type === "Assert" || c.type === "AssertWithoutThrow") {
          const l = c;
          return {
            type: "Insight",
            subType: "Assert",
            param: l.param,
            executor: async (h, p) => {
              const {task: b} = p;
              let w;
              const T = await parseContextFromWebPage(this.page)
                , A = await assertApi(l.param.assertion, T);
              if (!A.pass) {
                if (c.type === "Assert")
                  throw b.output = A,
                    b.log = {
                      dump: w
                    },
                    new Error(A.thought || "Assertion failed without reason");
                b.error = A.thought
              }
              return {
                output: A,
                log: {
                  dump: w
                }
              }
            }
          }
        }
        if (c.type === "Input")
          return {
            type: "Action",
            subType: "Input",
            param: c.param,
            executor: async (u, {element: h}) => {
              if (h) {
                if (await this.page.clearInput(h),
                u.value === "")
                  return;
                await this.page.keyboard.type(u.value)
              }
            }
          };
        if (c.type === "KeyboardPress")
          return {
            type: "Action",
            subType: "KeyboardPress",
            param: c.param,
            executor: async u => {
              invariant(u.value, "No key to press"),
                await this.page.keyboard.press(u.value)
            }
          };
        if (c.type === "Tap")
          return {
            type: "Action",
            subType: "Tap",
            executor: async (u, {element: h}) => {
              invariant(h, "Element not found, cannot tap"),
                await this.page.mouse.click(h.center[0], h.center[1])
            }
          };
        if (c.type === "Hover")
          return {
            type: "Action",
            subType: "Hover",
            executor: async (u, {element: h}) => {
              invariant(h, "Element not found, cannot hover"),
                await this.page.mouse.move(h.center[0], h.center[1])
            }
          };
        if (c.type === "Scroll")
          return {
            type: "Action",
            subType: "Scroll",
            param: c.param,
            executor: async u => {
              const h = u.scrollType;
              switch (h) {
                case "scrollUntilTop":
                  await this.page.scrollUntilTop();
                  break;
                case "scrollUntilBottom":
                  await this.page.scrollUntilBottom();
                  break;
                case "scrollUpOneScreen":
                  await this.page.scrollUpOneScreen();
                  break;
                case "scrollDownOneScreen":
                  await this.page.scrollDownOneScreen();
                  break;
                default:
                  console.error("Unknown scroll event type:", h)
              }
            }
          };
        if (c.type === "Sleep")
          return {
            type: "Action",
            subType: "Sleep",
            param: c.param,
            executor: async u => {
              await sleep$1(u.timeMs || 3e3)
            }
          };
        if (c.type === "Error")
          return {
            type: "Action",
            subType: "Error",
            param: c.param,
            executor: async u => {
              throw invariant(u.thought, "An error occurred, but no thought provided"),
                new Error(u.thought)
            }
          };
        throw new Error(`Unknown or Unsupported task type: ${c.type}`)
      }
    ).map(c => this.wrapExecutorWithScreenshot(c))
  }
  async action(e) {
    const n = new Executor(e)
      , a = this.taskCache.getCacheGroupByPrompt(e);
    let c = [];
    const l = {
      type: "Planning",
      param: {
        userPrompt: e
      },
      executor: async p => {
        let b;
        const w = await parseContextFromWebPage(this.page)
          , T = a.readCache(w, "plan", e);
        return T ? b = T : b = await plan(p.userPrompt, w),
          invariant(b.plans.length > 0, "No plans found"),
          c = b.plans,
          a.saveCache({
            type: "plan",
            pageContext: {
              url: w.url,
              size: w.size
            },
            prompt: e,
            response: b
          }),
          {
            output: b,
            cache: {
              hit: !!T
            }
          }
      }
    };
    await n.append(this.wrapExecutorWithScreenshot(l));
    let u = await n.flush();
    if (n.isInErrorState())
      return {
        output: u,
        executor: n
      };
    const h = await this.convertPlanToExecutable(c, a);
    return await n.append(h),
      u = await n.flush(),
      {
        output: u,
        executor: n
      }
  }
  async query(e) {
    const n = typeof e == "string" ? e : JSON.stringify(e)
      , a = new Executor(n)
      , c = {
      type: "Insight",
      subType: "Query",
      param: {
        dataDemand: e
      },
      executor: async u => {
        let h;
        const p = await parseContextFromWebPage(this.page);
        return {
          output: await extract(u.dataDemand, p),
          log: {
            dump: h
          }
        }
      }
    };
    return await a.append(this.wrapExecutorWithScreenshot(c)),
      {
        output: await a.flush(),
        executor: a
      }
  }
  async assert(e) {
    const n = `assert: ${e}`
      , a = new Executor(n)
      , c = {
      type: "Assert",
      param: {
        assertion: e
      }
    }
      , l = await this.convertPlanToExecutable([c]);
    return await a.append(this.wrapExecutorWithScreenshot(l[0])),
      {
        output: await a.flush(),
        executor: a
      }
  }
  async waitFor(e, n) {
    const a = `waitFor: ${e}`
      , c = new Executor(a)
      , {timeoutMs: l, checkIntervalMs: u} = n;
    invariant(e, "No assertion for waitFor"),
      invariant(l, "No timeoutMs for waitFor"),
      invariant(u, "No checkIntervalMs for waitFor");
    const h = Date.now();
    let p = Date.now()
      , b = "";
    for (; Date.now() - h < l; ) {
      p = Date.now();
      const A = {
        type: "AssertWithoutThrow",
        param: {
          assertion: e
        }
      }
        , I = await this.convertPlanToExecutable([A]);
      await c.append(this.wrapExecutorWithScreenshot(I[0]));
      const D = await c.flush();
      if (D != null && D.pass)
        return {
          output: void 0,
          executor: c
        };
      b = (D == null ? void 0 : D.thought) || "unknown error";
      const N = Date.now();
      if (N - p < u) {
        const L = {
          type: "Sleep",
          param: {
            timeMs: u - (N - p)
          }
        }
          , q = await this.convertPlanToExecutable([L]);
        await c.append(this.wrapExecutorWithScreenshot(q[0])),
          await c.flush()
      }
    }
    const w = {
      type: "Error",
      param: {
        thought: `waitFor timeout: ${b}`
      }
    }
      , T = await this.convertPlanToExecutable([w]);
    return await c.append(T[0]),
      await c.flush(),
      {
        output: void 0,
        executor: c
      }
  }
}
class PageAgent {
  constructor(e, n) {
    xr(this, "page");
    xr(this, "dump");
    xr(this, "reportFile");
    xr(this, "reportFileName");
    xr(this, "taskExecutor");
    xr(this, "opts");
    this.page = e,
      this.opts = Object.assign({
        generateReport: !0,
        autoPrintReportMsg: !0,
        groupName: "Midscene Report",
        groupDescription: ""
      }, n || {}),
      this.dump = {
        groupName: this.opts.groupName,
        groupDescription: this.opts.groupDescription,
        executions: []
      },
      this.taskExecutor = new PageTaskExecutor(this.page,{
        cacheId: n == null ? void 0 : n.cacheId
      }),
      this.reportFileName = reportFileName((n == null ? void 0 : n.testId) || "web")
  }
  appendExecutionDump(e) {
    this.dump.executions.push(e)
  }
  dumpDataString() {
    return this.dump.groupName = this.opts.groupName,
      this.dump.groupDescription = this.opts.groupDescription,
      ""
  }
  writeOutActionDumps() {}
  async aiAction(e) {
    const {executor: n} = await this.taskExecutor.action(e);
    if (this.appendExecutionDump(n.dump()),
      this.writeOutActionDumps(),
      n.isInErrorState()) {
      const a = n.latestErrorTask();
      throw new Error(`${a == null ? void 0 : a.error}`)
    }
  }
  async aiQuery(e) {
    const {output: n, executor: a} = await this.taskExecutor.query(e);
    if (this.appendExecutionDump(a.dump()),
      this.writeOutActionDumps(),
      a.isInErrorState()) {
      const c = a.latestErrorTask();
      throw new Error(`${c == null ? void 0 : c.error}`)
    }
    return n
  }
  async aiAssert(e, n) {
    const {output: a, executor: c} = await this.taskExecutor.assert(e);
    if (this.appendExecutionDump(c.dump()),
      this.writeOutActionDumps(),
      !(a != null && a.pass)) {
      const l = n || `Assertion failed: ${e}`
        , u = `Reason: ${(a == null ? void 0 : a.thought) || "(no_reason)"}`;
      throw new Error(`${l}
${u}`)
    }
  }
  async aiWaitFor(e, n) {
    const {executor: a} = await this.taskExecutor.waitFor(e, {
      timeoutMs: (n == null ? void 0 : n.timeoutMs) || 15e3,
      checkIntervalMs: (n == null ? void 0 : n.checkIntervalMs) || 3e3,
      assertion: e
    });
    if (this.appendExecutionDump(a.dump()),
      this.writeOutActionDumps(),
      a.isInErrorState()) {
      const c = a.latestErrorTask();
      throw new Error(`${c == null ? void 0 : c.error}`)
    }
  }
  async ai(e, n="action") {
    if (n === "action")
      return this.aiAction(e);
    if (n === "query")
      return this.aiQuery(e);
    if (n === "assert")
      return this.aiAssert(e);
    throw new Error(`Unknown type: ${n}, only support 'action', 'query', 'assert'`)
  }
}
async function resizeImg(o, e) {
  const n = typeof o == "string"
    , a = n ? buffer.Buffer.from(o.split(";base64,").pop() || o, "base64") : o
    , c = await Jimp.read(a)
    , {width: l, height: u} = c.bitmap;
  if (!l || !u)
    throw Error("Undefined width or height from the input image.");
  const h = e || calculateNewDimensions(l, u);
  c.resize(h.width, h.height);
  const p = await c.getBufferAsync(Jimp.MIME_PNG);
  return n ? p.toString("base64") : p
}
function calculateNewDimensions(o, e) {
  let c = o
    , l = e;
  const u = o / e;
  return o > 2048 && (c = 2048,
    l = c / u),
  l > 768 && (l = 768,
    c = l * u),
    {
      width: Math.round(c),
      height: Math.round(l)
    }
}
class Page {
  constructor(e, n) {
    xr(this, "page");
    xr(this, "pageType");
    this.page = e,
      this.pageType = n
  }
  evaluate(e, n) {
    return this.page.evaluate(e, n)
  }
  async getElementInfos() {
    const e = await getExtraReturnLogic();
    return await this.evaluate(e)
  }
  async screenshot() {
    const e = await this.evaluate( () => ({
      width: document.documentElement.clientWidth,
      height: document.documentElement.clientHeight,
      deviceScaleFactor: window.devicePixelRatio
    }));
    let a = await this.page.screenshot({
      path: 'getTmpFile("jpeg")',
      type: "png"
    });
    return e.deviceScaleFactor > 1 && (a = await resizeImg(a, {
      width: e.width,
      height: e.height
    })),
      a
  }
  url() {
    return this.page.url()
  }
  get mouse() {
    return {
      click: async (e, n, a) => this.page.mouse.click(e, n, {
        button: (a == null ? void 0 : a.button) || "left"
      }),
      wheel: async (e, n) => {
        await this.page.mouse.wheel(e, n)
      }
      ,
      move: async (e, n) => this.page.mouse.move(e, n)
    }
  }
  get keyboard() {
    return {
      type: async e => this.page.keyboard.type(e),
      press: async e => this.page.keyboard.press(e),
      down: async e => this.page.keyboard.down(e),
      up: async e => this.page.keyboard.up(e)
    }
  }
  async clearInput(e) {
    if (!e)
      return;
    await this.mouse.click(e.center[0], e.center[1]),
      navigator.userAgent.includes("Macintosh") ? (await this.page.keyboard.down("Meta"),
        await this.page.keyboard.press("a"),
        await this.page.keyboard.up("Meta")) : (await this.page.keyboard.down("Control"),
        await this.page.keyboard.press("a"),
        await this.page.keyboard.up("Control")),
      await this.keyboard.press("Backspace")
  }
  scrollUntilTop() {
    return this.mouse.wheel(0, -9999999)
  }
  scrollUntilBottom() {
    return this.mouse.wheel(0, 9999999)
  }
  async scrollUpOneScreen() {
    const n = await this.evaluate( () => window.innerHeight) * .7;
    await this.mouse.wheel(0, -n)
  }
  async scrollDownOneScreen() {
    const n = await this.evaluate( () => window.innerHeight) * .7;
    await this.mouse.wheel(0, n)
  }
}
class WebPage extends Page {
  constructor(e) {
    super(e, "playwright")
  }
}
let abortController, currentStepPromise, crxApp;
function runStep(o) {
  return currentStepPromise = o,
    currentStepPromise
}
const flowsRouter = router$3({
  cancelFlow: publicProcedure.mutation(async () => (abortController == null || abortController.abort(),
    await currentStepPromise,
    !0)),
  runAI: publicProcedure.input(FlowFormSchema).subscription( ({input: o, signal: e}) => observable$6(n => {
      async function a() {
        var p;
        if (await ((p = closeInspectingCtx()) == null ? void 0 : p.catch( () => {}
        )),
          crxApp)
          try {
            await crxApp.detachAll(),
              await crxApp.close()
          } catch {}
        const [c] = await chrome.tabs.query({
          active: !0
        });
        chrome.sidePanel.setOptions({
          path: "ui.html",
          enabled: !0
        }),
          crxApp = await crx.start();
        const l = crxApp.context();
        await l.tracing.start({
          snapshots: !0,
          screenshots: !0
        });
        const u = await crxApp.attach(c.id).catch( () => crxApp.newPage())
          , h = new PageAgent(new WebPage(u),{
          testId: "playwright-1234",
          cacheId: "playwright-1234",
          groupName: "playwright-1234",
          groupDescription: "playwright-1234",
          generateReport: !1
        });
        globalThis.fs.promises;
        try {
          abortController = new AbortController;
          for (const [b,w] of o.items.entries()) {
            if (abortController.signal.aborted) {
              n.complete();
              break
            }
            if (n.next({
              event: "runAIEvent",
              step: b,
              type: w.type,
              status: "running"
            }),
            w.type === "goto" && await runStep(u.goto(w.gotoUrl)),
            w.type === "AI" && await runStep(h.ai(w.prompt)),
            w.type === "AIExtract") {
              const T = [Object.fromEntries(w.extractFields.map(I => [I.name, I.description]))]
                , A = await runStep(h.aiQuery(T));
              n.next({
                event: "runAIEvent",
                step: b,
                type: w.type,
                data: A,
                status: "completed"
              });
              continue
            }
            if (w.type === "click") {
              const T = u.locator(locatorOrSelectorAsSelector$1("python", w.selector, "data-testid"));
              if (await T.count() === 0) {
                n.next({
                  event: "runAIEvent",
                  step: b,
                  type: w.type,
                  status: "failed",
                  message: `Element not found: ${w.selector}`
                });
                break
              }
              await runStep(T.first().click())
            }
            if (w.type === "type") {
              const T = u.locator(locatorOrSelectorAsSelector$1("python", w.selector, "data-testid"));
              if (await T.count() === 0) {
                n.next({
                  event: "runAIEvent",
                  step: b,
                  type: w.type,
                  status: "failed",
                  message: `Element not found: ${w.selector}`
                });
                break
              }
              await runStep(T.first().fill(w.text))
            }
            if (w.type === "scroll")
              if (w.scrollToSelector) {
                const T = u.locator(locatorOrSelectorAsSelector$1("python", w.selector, "data-testid"));
                if (await T.count() === 0) {
                  n.next({
                    event: "runAIEvent",
                    step: b,
                    type: w.type,
                    status: "failed",
                    message: `Element not found: ${w.selector}`
                  });
                  break
                }
                await runStep(T.first().scrollIntoViewIfNeeded())
              } else
                await runStep(u.mouse.wheel(0, w.scroll ?? 0));
            w.type === "waitForNavigation" && await runStep(u.waitForLoadState("networkidle")),
            w.type === "waitForUrl" && await runStep(u.waitForURL(w.waitForUrl)),
              n.next({
                event: "runAIEvent",
                step: b,
                type: w.type,
                status: "completed"
              })
          }
        } catch (b) {
          console.log(123, b, b.message),
            b.name === "TimeoutError" || b.message.includes("Failed to plan") || b.message.includes("Failed to locate") ? n.error(new TRPCError$6({
              code: "BAD_REQUEST",
              message: b.message
            })) : n.error(b)
        } finally {
          await l.tracing.stop({
            path: "/crx/trace.zip"
          }),
            await (crxApp == null ? void 0 : crxApp.detachAll()),
            await (crxApp == null ? void 0 : crxApp.close()),
            n.complete()
        }
      }
      return a(),
        async () => {}
    }
  ))
});
var adapter = {}, dist = {}, createProxy$2 = {}, _memo, _cacheKey;
const noop$1 = () => {}
  , freezeIfAvailable = o => {
    Object.freeze && Object.freeze(o)
  }
;
function createInnerProxy(o, e, n) {
  const a = e.join(".");
  return (_memo = n)[_cacheKey = a] ?? (_memo[_cacheKey] = new Proxy(noop$1,{
    get(c, l) {
      if (!(typeof l != "string" || l === "then"))
        return createInnerProxy(o, [...e, l], n)
    },
    apply(c, l, u) {
      const h = e[e.length - 1];
      let p = {
        args: u,
        path: e
      };
      return h === "call" ? p = {
        args: u.length >= 2 ? [u[1]] : [],
        path: e.slice(0, -1)
      } : h === "apply" && (p = {
        args: u.length >= 2 ? u[1] : [],
        path: e.slice(0, -1)
      }),
        freezeIfAvailable(p.args),
        freezeIfAvailable(p.path),
        o(p)
    }
  })),
    n[a]
}
const createRecursiveProxy = o => createInnerProxy(o, [], Object.create(null))
  , createFlatProxy = o => new Proxy(noop$1,{
  get(e, n) {
    if (!(typeof n != "string" || n === "then"))
      return o(n)
  }
});
createProxy$2.createFlatProxy = createFlatProxy;
createProxy$2.createRecursiveProxy = createRecursiveProxy;
var utils$6 = {};
const unsetMarker = Symbol();
function mergeWithoutOverrides(o, ...e) {
  const n = Object.assign(Object.create(null), o);
  for (const a of e)
    for (const c in a) {
      if (c in n && n[c] !== a[c])
        throw new Error(`Duplicate key ${c}`);
      n[c] = a[c]
    }
  return n
}
function isObject(o) {
  return !!o && !Array.isArray(o) && typeof o == "object"
}
function isFunction(o) {
  return typeof o == "function"
}
function omitPrototype(o) {
  return Object.assign(Object.create(null), o)
}
const asyncIteratorsSupported = typeof Symbol == "function" && !!Symbol.asyncIterator;
function isAsyncIterable(o) {
  return asyncIteratorsSupported && isObject(o) && Symbol.asyncIterator in o
}
const run = o => o();
function noop() {}
function identity(o) {
  return o
}
function assert(o, e="no additional info") {
  if (!o)
    throw new Error(`AssertionError: ${e}`)
}
function sleep(o=0) {
  return new Promise(e => setTimeout(e, o))
}
function abortSignalsAnyPonyfill(o) {
  if (typeof AbortSignal.any == "function")
    return AbortSignal.any(o);
  const e = new AbortController;
  for (const a of o) {
    if (a.aborted) {
      n();
      break
    }
    a.addEventListener("abort", n, {
      once: !0
    })
  }
  return e.signal;
  function n() {
    e.abort();
    for (const a of o)
      a.removeEventListener("abort", n)
  }
}
utils$6.abortSignalsAnyPonyfill = abortSignalsAnyPonyfill;
utils$6.assert = assert;
utils$6.identity = identity;
utils$6.isAsyncIterable = isAsyncIterable;
utils$6.isFunction = isFunction;
utils$6.isObject = isObject;
utils$6.mergeWithoutOverrides = mergeWithoutOverrides;
utils$6.noop = noop;
utils$6.omitPrototype = omitPrototype;
utils$6.run = run;
utils$6.sleep = sleep;
utils$6.unsetMarker = unsetMarker;
var codes$1 = codes$2
  , utils$5 = utils$6;
const JSONRPC2_TO_HTTP_CODE = {
  PARSE_ERROR: 400,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_SUPPORTED: 405,
  TIMEOUT: 408,
  CONFLICT: 409,
  PRECONDITION_FAILED: 412,
  PAYLOAD_TOO_LARGE: 413,
  UNSUPPORTED_MEDIA_TYPE: 415,
  UNPROCESSABLE_CONTENT: 422,
  TOO_MANY_REQUESTS: 429,
  CLIENT_CLOSED_REQUEST: 499,
  INTERNAL_SERVER_ERROR: 500,
  NOT_IMPLEMENTED: 501,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504
}
  , HTTP_CODE_TO_JSONRPC2 = {
  400: "BAD_REQUEST",
  401: "UNAUTHORIZED",
  403: "FORBIDDEN",
  404: "NOT_FOUND",
  405: "METHOD_NOT_SUPPORTED",
  408: "TIMEOUT",
  409: "CONFLICT",
  412: "PRECONDITION_FAILED",
  413: "PAYLOAD_TOO_LARGE",
  415: "UNSUPPORTED_MEDIA_TYPE",
  422: "UNPROCESSABLE_CONTENT",
  429: "TOO_MANY_REQUESTS",
  499: "CLIENT_CLOSED_REQUEST",
  500: "INTERNAL_SERVER_ERROR",
  501: "NOT_IMPLEMENTED",
  502: "BAD_GATEWAY",
  503: "SERVICE_UNAVAILABLE",
  504: "GATEWAY_TIMEOUT"
};
function getStatusCodeFromKey(o) {
  return JSONRPC2_TO_HTTP_CODE[o] ?? 500
}
function getStatusKeyFromCode(o) {
  return HTTP_CODE_TO_JSONRPC2[o] ?? "INTERNAL_SERVER_ERROR"
}
function getHTTPStatusCode$1(o) {
  const e = Array.isArray(o) ? o : [o]
    , n = new Set(e.map(c => {
      var l;
      if ("error"in c && utils$5.isObject(c.error.data)) {
        if (typeof ((l = c.error.data) == null ? void 0 : l.httpStatus) == "number")
          return c.error.data.httpStatus;
        const u = codes$1.TRPC_ERROR_CODES_BY_NUMBER[c.error.code];
        return getStatusCodeFromKey(u)
      }
      return 200
    }
  ));
  return n.size !== 1 ? 207 : n.values().next().value
}
function getHTTPStatusCodeFromError(o) {
  return getStatusCodeFromKey(o.code)
}
getHTTPStatusCode$2.HTTP_CODE_TO_JSONRPC2 = HTTP_CODE_TO_JSONRPC2;
getHTTPStatusCode$2.JSONRPC2_TO_HTTP_CODE = JSONRPC2_TO_HTTP_CODE;
getHTTPStatusCode$2.getHTTPStatusCode = getHTTPStatusCode$1;
getHTTPStatusCode$2.getHTTPStatusCodeFromError = getHTTPStatusCodeFromError;
getHTTPStatusCode$2.getStatusCodeFromKey = getStatusCodeFromKey;
getHTTPStatusCode$2.getStatusKeyFromCode = getStatusKeyFromCode;
var getHTTPStatusCode = getHTTPStatusCode$2
  , codes = codes$2;
function getErrorShape$1(o) {
  const {path: e, error: n, config: a} = o
    , {code: c} = o.error
    , l = {
    message: n.message,
    code: codes.TRPC_ERROR_CODES_BY_KEY[c],
    data: {
      code: c,
      httpStatus: getHTTPStatusCode.getHTTPStatusCodeFromError(n)
    }
  };
  return a.isDev && typeof o.error.stack == "string" && (l.data.stack = o.error.stack),
  typeof e == "string" && (l.data.path = e),
    a.errorFormatter({
      ...o,
      shape: l
    })
}
getErrorShape$2.getErrorShape = getErrorShape$1;
var TRPCError$5 = {}
  , utils$4 = utils$6;
class UnknownCauseError extends Error {
}
function getCauseFromUnknown(o) {
  if (o instanceof Error)
    return o;
  const e = typeof o;
  if (!(e === "undefined" || e === "function" || o === null)) {
    if (e !== "object")
      return new Error(String(o));
    if (utils$4.isObject(o)) {
      const n = new UnknownCauseError;
      for (const a in o)
        n[a] = o[a];
      return n
    }
  }
}
function getTRPCErrorFromUnknown(o) {
  if (o instanceof TRPCError$4 || o instanceof Error && o.name === "TRPCError")
    return o;
  const e = new TRPCError$4({
    code: "INTERNAL_SERVER_ERROR",
    cause: o
  });
  return o instanceof Error && o.stack && (e.stack = o.stack),
    e
}
let TRPCError$4 = class extends Error {
    constructor(e) {
      const n = getCauseFromUnknown(e.cause)
        , a = e.message ?? (n == null ? void 0 : n.message) ?? e.code;
      super(a, {
        cause: n
      }),
        this.code = e.code,
        this.name = "TRPCError",
      this.cause || (this.cause = n)
    }
  }
;
TRPCError$5.TRPCError = TRPCError$4;
TRPCError$5.getCauseFromUnknown = getCauseFromUnknown;
TRPCError$5.getTRPCErrorFromUnknown = getTRPCErrorFromUnknown;
var tracked$2 = {};
const trackedSymbol = Symbol();
function sse(o) {
  return tracked$1(o.id, o.data)
}
function isTrackedEnvelope(o) {
  return Array.isArray(o) && o[2] === trackedSymbol
}
function tracked$1(o, e) {
  if (o === "")
    throw new Error("`id` must not be an empty string as empty string is the same as not setting the id at all");
  return [o, e, trackedSymbol]
}
tracked$2.isTrackedEnvelope = isTrackedEnvelope;
tracked$2.sse = sse;
tracked$2.tracked = tracked$1;
var transformer$3 = {}
  , utils$3 = utils$6;
function getDataTransformer(o) {
  return "input"in o ? o : {
    input: o,
    output: o
  }
}
const defaultTransformer = {
  input: {
    serialize: o => o,
    deserialize: o => o
  },
  output: {
    serialize: o => o,
    deserialize: o => o
  }
};
function transformTRPCResponseItem(o, e) {
  return "error"in e ? {
    ...e,
    error: o.transformer.output.serialize(e.error)
  } : "data"in e.result ? {
    ...e,
    result: {
      ...e.result,
      data: o.transformer.output.serialize(e.result.data)
    }
  } : e
}
function transformTRPCResponse(o, e) {
  return Array.isArray(e) ? e.map(n => transformTRPCResponseItem(o, n)) : transformTRPCResponseItem(o, e)
}
function transformResultInner(o, e) {
  if ("error"in o) {
    const a = e.deserialize(o.error);
    return {
      ok: !1,
      error: {
        ...o,
        error: a
      }
    }
  }
  return {
    ok: !0,
    result: {
      ...o.result,
      ...(!o.result.type || o.result.type === "data") && {
        type: "data",
        data: e.deserialize(o.result.data)
      }
    }
  }
}
class TransformResultError extends Error {
  constructor() {
    super("Unable to transform response from server")
  }
}
function transformResult(o, e) {
  let n;
  try {
    n = transformResultInner(o, e)
  } catch {
    throw new TransformResultError
  }
  if (!n.ok && (!utils$3.isObject(n.error.error) || typeof n.error.error.code != "number"))
    throw new TransformResultError;
  if (n.ok && !utils$3.isObject(n.result))
    throw new TransformResultError;
  return n
}
transformer$3.defaultTransformer = defaultTransformer;
transformer$3.getDataTransformer = getDataTransformer;
transformer$3.transformResult = transformResult;
transformer$3.transformTRPCResponse = transformTRPCResponse;
var initTRPC$2 = {}
  , formatter$2 = {};
const defaultFormatter = ({shape: o}) => o;
formatter$2.defaultFormatter = defaultFormatter;
var middleware$3 = {}
  , TRPCError$3 = TRPCError$5
  , utils$2 = utils$6;
const middlewareMarker = "middlewareMarker";
function createMiddlewareFactory() {
  function o(n) {
    return {
      _middlewares: n,
      unstable_pipe(a) {
        const c = "_middlewares"in a ? a._middlewares : [a];
        return o([...n, ...c])
      }
    }
  }
  function e(n) {
    return o([n])
  }
  return e
}
const experimental_standaloneMiddleware = () => ({
  create: createMiddlewareFactory()
});
function createInputMiddleware(o) {
  const e = async function(a) {
    let c;
    const l = await a.getRawInput();
    try {
      c = await o(l)
    } catch (h) {
      throw new TRPCError$3.TRPCError({
        code: "BAD_REQUEST",
        cause: h
      })
    }
    const u = utils$2.isObject(a.input) && utils$2.isObject(c) ? {
      ...a.input,
      ...c
    } : c;
    return a.next({
      input: u
    })
  };
  return e._type = "input",
    e
}
function createOutputMiddleware(o) {
  const e = async function({next: a}) {
    const c = await a();
    if (!c.ok)
      return c;
    try {
      const l = await o(c.data);
      return {
        ...c,
        data: l
      }
    } catch (l) {
      throw new TRPCError$3.TRPCError({
        message: "Output validation failed",
        code: "INTERNAL_SERVER_ERROR",
        cause: l
      })
    }
  };
  return e._type = "output",
    e
}
middleware$3.createInputMiddleware = createInputMiddleware;
middleware$3.createMiddlewareFactory = createMiddlewareFactory;
middleware$3.createOutputMiddleware = createOutputMiddleware;
middleware$3.experimental_standaloneMiddleware = experimental_standaloneMiddleware;
middleware$3.middlewareMarker = middlewareMarker;
var procedureBuilder$1 = {}
  , parser$1 = {};
function getParseFn(o) {
  const e = o;
  if (typeof e == "function" && typeof e.assert == "function")
    return e.assert.bind(e);
  if (typeof e == "function")
    return e;
  if (typeof e.parseAsync == "function")
    return e.parseAsync.bind(e);
  if (typeof e.parse == "function")
    return e.parse.bind(e);
  if (typeof e.validateSync == "function")
    return e.validateSync.bind(e);
  if (typeof e.create == "function")
    return e.create.bind(e);
  if (typeof e.assert == "function")
    return n => (e.assert(n),
      n);
  throw new Error("Could not find a validator fn")
}
parser$1.getParseFn = getParseFn;
var TRPCError$2 = TRPCError$5
  , middleware$2 = middleware$3
  , parser = parser$1
  , utils$1 = utils$6;
function createNewBuilder(o, e) {
  const {middlewares: n=[], inputs: a, meta: c, ...l} = e;
  return createBuilder({
    ...utils$1.mergeWithoutOverrides(o, l),
    inputs: [...o.inputs, ...a ?? []],
    middlewares: [...o.middlewares, ...n],
    meta: o.meta && c ? {
      ...o.meta,
      ...c
    } : c ?? o.meta
  })
}
function createBuilder(o={}) {
  const e = {
    procedure: !0,
    inputs: [],
    middlewares: [],
    ...o
  };
  return {
    _def: e,
    input(a) {
      const c = parser.getParseFn(a);
      return createNewBuilder(e, {
        inputs: [a],
        middlewares: [middleware$2.createInputMiddleware(c)]
      })
    },
    output(a) {
      const c = parser.getParseFn(a);
      return createNewBuilder(e, {
        output: a,
        middlewares: [middleware$2.createOutputMiddleware(c)]
      })
    },
    meta(a) {
      return createNewBuilder(e, {
        meta: a
      })
    },
    use(a) {
      const c = "_middlewares"in a ? a._middlewares : [a];
      return createNewBuilder(e, {
        middlewares: c
      })
    },
    unstable_concat(a) {
      return createNewBuilder(e, a._def)
    },
    query(a) {
      return createResolver({
        ...e,
        type: "query"
      }, a)
    },
    mutation(a) {
      return createResolver({
        ...e,
        type: "mutation"
      }, a)
    },
    subscription(a) {
      return createResolver({
        ...e,
        type: "subscription"
      }, a)
    },
    experimental_caller(a) {
      return createNewBuilder(e, {
        caller: a
      })
    }
  }
}
function createResolver(o, e) {
  const n = createNewBuilder(o, {
    resolver: e,
    middlewares: [async function(p) {
      const b = await e(p);
      return {
        marker: middleware$2.middlewareMarker,
        ok: !0,
        data: b,
        ctx: p.ctx
      }
    }
    ]
  })
    , a = {
    ...n._def,
    type: o.type,
    experimental_caller: !!n._def.caller,
    meta: n._def.meta,
    $types: null
  }
    , c = createProcedureCaller(n._def)
    , l = n._def.caller;
  if (!l)
    return c;
  const u = async (...h) => await l({
    args: h,
    invoke: c,
    _def: a
  });
  return u._def = a,
    u
}
const codeblock = `
This is a client-only function.
If you want to call this function on the server, see https://trpc.io/docs/v11/server/server-side-calls
`.trim();
async function callRecursive(o, e, n) {
  try {
    const a = e.middlewares[o];
    return await a({
      ...n,
      meta: e.meta,
      input: n.input,
      next(l) {
        const u = l;
        return callRecursive(o + 1, e, {
          ...n,
          ctx: u != null && u.ctx ? {
            ...n.ctx,
            ...u.ctx
          } : n.ctx,
          input: u && "input"in u ? u.input : n.input,
          getRawInput: (u == null ? void 0 : u.getRawInput) ?? n.getRawInput
        })
      }
    })
  } catch (a) {
    return {
      ok: !1,
      error: TRPCError$2.getTRPCErrorFromUnknown(a),
      marker: middleware$2.middlewareMarker
    }
  }
}
function createProcedureCaller(o) {
  async function e(n) {
    if (!n || !("getRawInput"in n))
      throw new Error(codeblock);
    const a = await callRecursive(0, o, n);
    if (!a)
      throw new TRPCError$2.TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "No result from middlewares - did you forget to `return next()`?"
      });
    if (!a.ok)
      throw a.error;
    return a.data
  }
  return e._def = o,
    e
}
procedureBuilder$1.createBuilder = createBuilder;
var rootConfig$1 = {}, sn, on, an, ln, cn, un;
const isServerDefault = typeof window > "u" || "Deno"in window || ((on = (sn = globalThis.process) == null ? void 0 : sn.env) == null ? void 0 : on.NODE_ENV) === "test" || !!((ln = (an = globalThis.process) == null ? void 0 : an.env) != null && ln.JEST_WORKER_ID) || !!((un = (cn = globalThis.process) == null ? void 0 : cn.env) != null && un.VITEST_WORKER_ID);
rootConfig$1.isServerDefault = isServerDefault;
var router$2 = {}
  , createProxy$1 = createProxy$2
  , formatter$1 = formatter$2
  , TRPCError$1 = TRPCError$5
  , transformer$2 = transformer$3
  , utils = utils$6;
function isRouter(o) {
  return o._def && "router"in o._def
}
const emptyRouter = {
  _ctx: null,
  _errorShape: null,
  _meta: null,
  queries: {},
  mutations: {},
  subscriptions: {},
  errorFormatter: formatter$1.defaultFormatter,
  transformer: transformer$2.defaultTransformer
}
  , reservedWords = ["then", "call", "apply"];
function createRouterFactory(o) {
  function e(n) {
    const a = new Set(Object.keys(n).filter(p => reservedWords.includes(p)));
    if (a.size > 0)
      throw new Error("Reserved words used in `router({})` call: " + Array.from(a).join(", "));
    const c = utils.omitPrototype({});
    function l(p, b=[]) {
      const w = utils.omitPrototype({});
      for (const [T,A] of Object.entries(p ?? {})) {
        if (isRouter(A)) {
          w[T] = l(A._def.record, [...b, T]);
          continue
        }
        if (!isProcedure(A)) {
          w[T] = l(A, [...b, T]);
          continue
        }
        const I = [...b, T].join(".");
        if (c[I])
          throw new Error(`Duplicate key: ${I}`);
        c[I] = A,
          w[T] = A
      }
      return w
    }
    const u = l(n)
      , h = {
      _config: o,
      router: !0,
      procedures: c,
      ...emptyRouter,
      record: u
    };
    return {
      ...u,
      _def: h,
      createCaller: createCallerFactory()({
        _def: h
      })
    }
  }
  return e
}
function isProcedure(o) {
  return typeof o == "function"
}
function callProcedure(o) {
  const {type: e, path: n} = o
    , a = o.procedures[n];
  if (!a || !isProcedure(a) || a._def.type !== e && !o.allowMethodOverride)
    throw new TRPCError$1.TRPCError({
      code: "NOT_FOUND",
      message: `No "${e}"-procedure on path "${n}"`
    });
  /* istanbul ignore if -- @preserve */
  if (a._def.type !== e && o.allowMethodOverride && a._def.type === "subscription")
    throw new TRPCError$1.TRPCError({
      code: "METHOD_NOT_SUPPORTED",
      message: "Method override is not supported for subscriptions"
    });
  return a(o)
}
function createCallerFactory() {
  return function(e) {
    const n = e._def;
    return function(c, l) {
      return createProxy$1.createRecursiveProxy(async ({path: u, args: h}) => {
          var T;
          const p = u.join(".");
          if (u.length === 1 && u[0] === "_def")
            return n;
          const b = n.procedures[p];
          let w;
          try {
            return w = utils.isFunction(c) ? await Promise.resolve(c()) : c,
              await b({
                path: p,
                getRawInput: async () => h[0],
                ctx: w,
                type: b._def.type,
                signal: l == null ? void 0 : l.signal
              })
          } catch (A) {
            throw (T = l == null ? void 0 : l.onError) == null || T.call(l, {
              ctx: w,
              error: TRPCError$1.getTRPCErrorFromUnknown(A),
              input: h[0],
              path: p,
              type: b._def.type
            }),
              A
          }
        }
      )
    }
  }
}
function mergeRouters(...o) {
  var l;
  const e = utils.mergeWithoutOverrides({}, ...o.map(u => u._def.record))
    , n = o.reduce( (u, h) => {
      if (h._def._config.errorFormatter && h._def._config.errorFormatter !== formatter$1.defaultFormatter) {
        if (u !== formatter$1.defaultFormatter && u !== h._def._config.errorFormatter)
          throw new Error("You seem to have several error formatters");
        return h._def._config.errorFormatter
      }
      return u
    }
    , formatter$1.defaultFormatter)
    , a = o.reduce( (u, h) => {
      if (h._def._config.transformer && h._def._config.transformer !== transformer$2.defaultTransformer) {
        if (u !== transformer$2.defaultTransformer && u !== h._def._config.transformer)
          throw new Error("You seem to have several transformers");
        return h._def._config.transformer
      }
      return u
    }
    , transformer$2.defaultTransformer);
  return createRouterFactory({
    errorFormatter: n,
    transformer: a,
    isDev: o.every(u => u._def._config.isDev),
    allowOutsideOfServer: o.every(u => u._def._config.allowOutsideOfServer),
    isServer: o.every(u => u._def._config.isServer),
    $types: (l = o[0]) == null ? void 0 : l._def._config.$types
  })(e)
}
router$2.callProcedure = callProcedure;
router$2.createCallerFactory = createCallerFactory;
router$2.createRouterFactory = createRouterFactory;
router$2.mergeRouters = mergeRouters;
var formatter = formatter$2
  , middleware$1 = middleware$3
  , procedureBuilder = procedureBuilder$1
  , rootConfig = rootConfig$1
  , router$1 = router$2
  , transformer$1 = transformer$3;
class TRPCBuilder {
  context() {
    return new TRPCBuilder
  }
  meta() {
    return new TRPCBuilder
  }
  create(e) {
    var a;
    const n = {
      transformer: transformer$1.getDataTransformer((e == null ? void 0 : e.transformer) ?? transformer$1.defaultTransformer),
      isDev: (e == null ? void 0 : e.isDev) ?? ((a = globalThis.process) == null ? void 0 : a.env.NODE_ENV) !== "production",
      allowOutsideOfServer: (e == null ? void 0 : e.allowOutsideOfServer) ?? !1,
      errorFormatter: (e == null ? void 0 : e.errorFormatter) ?? formatter.defaultFormatter,
      isServer: (e == null ? void 0 : e.isServer) ?? rootConfig.isServerDefault,
      $types: null,
      experimental: (e == null ? void 0 : e.experimental) ?? {}
    };
    if (!((e == null ? void 0 : e.isServer) ?? rootConfig.isServerDefault) && (e == null ? void 0 : e.allowOutsideOfServer) !== !0)
      throw new Error("You're trying to use @trpc/server in a non-server environment. This is not supported by default.");
    return {
      _config: n,
      procedure: procedureBuilder.createBuilder({
        meta: e == null ? void 0 : e.defaultMeta
      }),
      middleware: middleware$1.createMiddlewareFactory(),
      router: router$1.createRouterFactory(n),
      mergeRouters: router$1.mergeRouters,
      createCallerFactory: router$1.createCallerFactory()
    }
  }
}
const initTRPC$1 = new TRPCBuilder;
initTRPC$2.initTRPC = initTRPC$1;
var createProxy = createProxy$2
  , getErrorShape = getErrorShape$2
  , TRPCError = TRPCError$5
  , tracked = tracked$2
  , transformer = transformer$3
  , initTRPC = initTRPC$2
  , middleware = middleware$3
  , router = router$2;
dist.createTRPCFlatProxy = createProxy.createFlatProxy;
dist.getErrorShape = getErrorShape.getErrorShape;
dist.TRPCError = TRPCError.TRPCError;
dist.getTRPCErrorFromUnknown = TRPCError.getTRPCErrorFromUnknown;
dist.isTrackedEnvelope = tracked.isTrackedEnvelope;
dist.sse = tracked.sse;
dist.tracked = tracked.tracked;
dist.transformTRPCResponse = transformer.transformTRPCResponse;
dist.initTRPC = initTRPC.initTRPC;
dist.experimental_standaloneMiddleware = middleware.experimental_standaloneMiddleware;
dist.experimental_trpcMiddleware = middleware.experimental_standaloneMiddleware;
dist.callProcedure = router.callProcedure;
dist.callTRPCProcedure = router.callProcedure;
var observable$5 = {}
  , observable$4 = {};
function isObservable(o) {
  return typeof o == "object" && o !== null && "subscribe"in o
}
function observable$3(o) {
  const e = {
    subscribe(n) {
      let a = null
        , c = !1
        , l = !1
        , u = !1;
      function h() {
        if (a === null) {
          u = !0;
          return
        }
        l || (l = !0,
          typeof a == "function" ? a() : a && a.unsubscribe())
      }
      return a = o({
        next(p) {
          var b;
          c || (b = n.next) == null || b.call(n, p)
        },
        error(p) {
          var b;
          c || (c = !0,
          (b = n.error) == null || b.call(n, p),
            h())
        },
        complete() {
          var p;
          c || (c = !0,
          (p = n.complete) == null || p.call(n),
            h())
        }
      }),
      u && h(),
        {
          unsubscribe: h
        }
    },
    pipe(...n) {
      return n.reduce(pipeReducer, e)
    }
  };
  return e
}
function pipeReducer(o, e) {
  return e(o)
}
function observableToPromise(o) {
  const e = new AbortController;
  return new Promise( (a, c) => {
      let l = !1;
      function u() {
        l || (l = !0,
          h.unsubscribe())
      }
      e.signal.addEventListener("abort", () => {
          c(e.signal.reason)
        }
      );
      const h = o.subscribe({
        next(p) {
          l = !0,
            a(p),
            u()
        },
        error(p) {
          c(p)
        },
        complete() {
          e.abort(),
            u()
        }
      })
    }
  )
}
function observableToReadableStream(o) {
  let e = null;
  return new ReadableStream({
    start(n) {
      e = o.subscribe({
        next(a) {
          n.enqueue({
            ok: !0,
            value: a
          })
        },
        error(a) {
          n.enqueue({
            ok: !1,
            error: a
          }),
            n.close()
        },
        complete() {
          n.close()
        }
      })
    },
    cancel() {
      e == null || e.unsubscribe()
    }
  })
}
function observableToAsyncIterable(o) {
  const n = observableToReadableStream(o).getReader()
    , a = {
    async next() {
      const c = await n.read();
      if (c.done)
        return {
          value: void 0,
          done: !0
        };
      const {value: l} = c;
      if (!l.ok)
        throw l.error;
      return {
        value: l.value,
        done: !1
      }
    },
    async return() {
      return await n.cancel(),
        {
          value: void 0,
          done: !0
        }
    }
  };
  return {
    [Symbol.asyncIterator]() {
      return a
    }
  }
}
observable$4.isObservable = isObservable;
observable$4.observable = observable$3;
observable$4.observableToAsyncIterable = observableToAsyncIterable;
observable$4.observableToPromise = observableToPromise;
var operators$1 = {}
  , observable$2 = observable$4;
function map(o) {
  return e => observable$2.observable(n => {
      let a = 0;
      return e.subscribe({
        next(l) {
          n.next(o(l, a++))
        },
        error(l) {
          n.error(l)
        },
        complete() {
          n.complete()
        }
      })
    }
  )
}
function share(o) {
  return e => {
    let n = 0
      , a = null;
    const c = [];
    function l() {
      a || (a = e.subscribe({
        next(h) {
          var p;
          for (const b of c)
            (p = b.next) == null || p.call(b, h)
        },
        error(h) {
          var p;
          for (const b of c)
            (p = b.error) == null || p.call(b, h)
        },
        complete() {
          var h;
          for (const p of c)
            (h = p.complete) == null || h.call(p)
        }
      }))
    }
    function u() {
      if (n === 0 && a) {
        const h = a;
        a = null,
          h.unsubscribe()
      }
    }
    return observable$2.observable(h => (n++,
      c.push(h),
      l(),
      {
        unsubscribe() {
          n--,
            u();
          const p = c.findIndex(b => b === h);
          p > -1 && c.splice(p, 1)
        }
      }))
  }
}
function tap(o) {
  return e => observable$2.observable(n => e.subscribe({
    next(a) {
      var c;
      (c = o.next) == null || c.call(o, a),
        n.next(a)
    },
    error(a) {
      var c;
      (c = o.error) == null || c.call(o, a),
        n.error(a)
    },
    complete() {
      var a;
      (a = o.complete) == null || a.call(o),
        n.complete()
    }
  }))
}
const distinctUnsetMarker = Symbol();
function distinctUntilChanged(o= (e, n) => e === n) {
  return e => observable$2.observable(n => {
      let a = distinctUnsetMarker;
      return e.subscribe({
        next(c) {
          a !== distinctUnsetMarker && o(a, c) || (a = c,
            n.next(c))
        },
        error(c) {
          n.error(c)
        },
        complete() {
          n.complete()
        }
      })
    }
  )
}
const isDeepEqual = (o, e) => o === e ? !0 : !!(o && e && typeof o == "object" && typeof e == "object") && Object.keys(o).length === Object.keys(e).length && Object.entries(o).every( ([a,c]) => isDeepEqual(c, e[a]));
function distinctUntilDeepChanged() {
  return distinctUntilChanged(isDeepEqual)
}
operators$1.distinctUntilChanged = distinctUntilChanged;
operators$1.distinctUntilDeepChanged = distinctUntilDeepChanged;
operators$1.map = map;
operators$1.share = share;
operators$1.tap = tap;
var behaviorSubject$2 = {}
  , observable$1 = observable$4;
function behaviorSubject$1(o) {
  let e = o;
  const n = []
    , a = u => {
    e !== void 0 && u.next(e),
      n.push(u)
  }
    , c = u => {
    n.splice(n.indexOf(u), 1)
  }
    , l = observable$1.observable(u => (a(u),
      () => {
        c(u)
      }
  ));
  return l.next = u => {
    if (e !== u) {
      e = u;
      for (const h of n)
        h.next(u)
    }
  }
    ,
    l.get = () => e,
    l
}
behaviorSubject$2.behaviorSubject = behaviorSubject$1;
var observable = observable$4
  , operators = operators$1
  , behaviorSubject = behaviorSubject$2;
observable$5.isObservable = observable.isObservable;
observable$5.observable = observable.observable;
observable$5.observableToAsyncIterable = observable.observableToAsyncIterable;
observable$5.observableToPromise = observable.observableToPromise;
observable$5.distinctUntilChanged = operators.distinctUntilChanged;
observable$5.distinctUntilDeepChanged = operators.distinctUntilDeepChanged;
observable$5.map = operators.map;
observable$5.share = operators.share;
observable$5.tap = operators.tap;
observable$5.behaviorSubject = behaviorSubject.behaviorSubject;
var errors = {};
Object.defineProperty(errors, "__esModule", {
  value: !0
});
errors.getErrorFromUnknown = void 0;
const server_1$1 = dist;
function getErrorFromUnknown(o) {
  if (o instanceof Error && o.name === "TRPCError")
    return o;
  let e, n;
  o instanceof Error && (e = o,
    n = o.stack);
  const a = new server_1$1.TRPCError({
    message: "Internal server error",
    code: "INTERNAL_SERVER_ERROR",
    cause: e
  });
  return n && (a.stack = n),
    a
}
errors.getErrorFromUnknown = getErrorFromUnknown;
Object.defineProperty(adapter, "__esModule", {
  value: !0
});
var createChromeHandler_1 = adapter.createChromeHandler = void 0;
const server_1 = dist
  , observable_1 = observable$5
  , errors_1 = errors
  , createChromeHandler = o => {
    const {router: e, createContext: n, onError: a} = o
      , {transformer: c} = e._def._config;
    chrome.runtime.onConnect.addListener(l => {
        const u = new Map
          , h = []
          , p = () => {
            h.forEach(w => w())
          }
        ;
        l.onDisconnect.addListener(p),
          h.push( () => l.onDisconnect.removeListener(p));
        const b = async w => {
            if (!("trpc"in w))
              return;
            const {trpc: T} = w;
            if (!("id"in T) || T.id === null || T.id === void 0 || !T)
              return;
            const {id: A, jsonrpc: I, method: D} = T
              , N = Z => {
                l.postMessage({
                  trpc: Object.assign({
                    id: A,
                    jsonrpc: I
                  }, Z)
                })
              }
            ;
            let P, L, q;
            try {
              if (D === "subscription.stop") {
                const Q = u.get(A);
                Q && (Q.unsubscribe(),
                  N({
                    result: {
                      type: "stopped"
                    }
                  })),
                  u.delete(A);
                return
              }
              P = T.params,
                L = c.input.deserialize(P.input),
                q = await (n == null ? void 0 : n({
                  req: l,
                  res: void 0
                }));
              const Z = e.createCaller(q)
                , ce = await P.path.split(".").reduce( (Q, te) => Q[te], Z)(L);
              if (D !== "subscription") {
                const Q = c.output.serialize(ce);
                N({
                  result: {
                    type: "data",
                    data: Q
                  }
                });
                return
              }
              if (!(0,
                observable_1.isObservable)(ce))
                throw new server_1.TRPCError({
                  message: "Subscription ${params.path} did not return an observable",
                  code: "INTERNAL_SERVER_ERROR"
                });
              const oe = ce.subscribe({
                next: Q => {
                  N({
                    result: {
                      type: "data",
                      data: Q
                    }
                  })
                }
                ,
                error: Q => {
                  const te = (0,
                    errors_1.getErrorFromUnknown)(Q);
                  a == null || a({
                    error: te,
                    type: D,
                    path: P == null ? void 0 : P.path,
                    input: L,
                    ctx: q,
                    req: l
                  }),
                    N({
                      error: e.getErrorShape({
                        error: te,
                        type: D,
                        path: P == null ? void 0 : P.path,
                        input: L,
                        ctx: q
                      })
                    })
                }
                ,
                complete: () => {
                  N({
                    result: {
                      type: "stopped"
                    }
                  })
                }
              });
              if (u.has(A))
                throw oe.unsubscribe(),
                  N({
                    result: {
                      type: "stopped"
                    }
                  }),
                  new server_1.TRPCError({
                    message: `Duplicate id ${A}`,
                    code: "BAD_REQUEST"
                  });
              h.push( () => oe.unsubscribe()),
                u.set(A, oe),
                N({
                  result: {
                    type: "started"
                  }
                });
              return
            } catch (Z) {
              const J = (0,
                errors_1.getErrorFromUnknown)(Z);
              a == null || a({
                error: J,
                type: D,
                path: P == null ? void 0 : P.path,
                input: L,
                ctx: q,
                req: l
              }),
                N({
                  error: e.getErrorShape({
                    error: J,
                    type: D,
                    path: P == null ? void 0 : P.path,
                    input: L,
                    ctx: q
                  })
                })
            }
          }
        ;
        l.onMessage.addListener(b),
          h.push( () => l.onMessage.removeListener(b))
      }
    )
  }
;
createChromeHandler_1 = adapter.createChromeHandler = createChromeHandler;
telemetry.init("sk_umw4ieqnAW7VHzoZJDHrXT");
const appRouter = router$3({
  flows: flowsRouter,
  cdp: cdpRouter,
  helloWorld: publicProcedure.query( () => "Hello World"),
  helloMutation: publicProcedure.mutation( () => (console.log("Hello Mutation"),
    "Hello Mutation")),
  testRunAI: publicProcedure.mutation(async () => {
      await chrome.tabs.query({
        active: !0
      });
      const o = await chrome.windows.create({
        width: 1280,
        height: 720
      })
        , e = await crx.start()
        , n = await e.newPage({
        windowId: o.id
      })
        , a = new PageAgent(new WebPage(n),{
        testId: "playwright-1234",
        cacheId: "playwright-1234",
        groupName: "playwright-1234",
        groupDescription: "playwright-1234",
        generateReport: !1
      });
      return await n.goto("https://todomvc.com/examples/react/dist/"),
        await a.ai("Input 'Study JS today' in the task box input and press the Enter key"),
        await e.close(),
        await chrome.windows.remove(o.id),
        "testRunAI"
    }
  ),
  test: publicProcedure.mutation( () => "test"),
  updateStatus: publicProcedure.query(async () => {
      const o = await fetch(`https://api.scrapersensei.com/update-status?currentVersion=${chrome.runtime.getManifest().version}`);
      if (!o.ok)
        throw new TRPCError$6({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch latest version"
        });
      return await o.json()
    }
  )
});
appRouter.getErrorShape = o => getErrorShape$3({
  ...o,
  config: t._config
});
createChromeHandler_1({
  router: appRouter,
  onError(o) {
    var e, n, a;
    console.log("onError", o),
      telemetry.log("scrapersensei_browser_errors", {
        from: "background",
        userAgent: navigator.userAgent,
        extVersion: chrome.runtime.getManifest().version,
        chromeVersion: ((n = (e = navigator.userAgentData) == null ? void 0 : e.brands.find(c => c.brand === "Chromium")) == null ? void 0 : n.version) || "Uknown",
        os: (a = navigator.userAgentData) == null ? void 0 : a.platform,
        error: o.error.message,
        stack: o.error.stack,
        time: new Date().toISOString()
      })
  },
  createContext() {
    return {}
  }
});
const definition = defineBackground({
  type: "module",
  main: () => {
    globalThis.element_inspector = element_inspector,
      chrome.sidePanel.setPanelBehavior({
        openPanelOnActionClick: !0
      }).catch(o => console.error(o))
  }
});
var hn, dn;
((dn = (hn = globalThis.browser) == null ? void 0 : hn.runtime) == null ? void 0 : dn.id) == null ? globalThis.chrome : globalThis.browser;
function print(o, ...e) {}
const logger = {
  debug: (...o) => print(console.debug, ...o),
  log: (...o) => print(console.log, ...o),
  warn: (...o) => print(console.warn, ...o),
  error: (...o) => print(console.error, ...o)
};
let result;
try {
  initPlugins(),
    result = definition.main(),
  result instanceof Promise && console.warn("The background's main() function return a promise, but it must be synchronous")
} catch (o) {
  throw logger.error("The background crashed on startup!"),
    o
}
