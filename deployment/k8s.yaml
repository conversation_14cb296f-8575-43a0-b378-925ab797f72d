#########################################################
# Common Environment variables ConfigMap
#########################################################
apiVersion: v1
kind: ConfigMap
metadata:
  name: common-env
data:
  NAMESPACE: ""
  LOGLEVEL: info
  SERVICEDIR: dist/services
  TRANSPORTER: nats://nats:4222
  CACHER: redis://redis:6379
  MONGO_URI: mongodb://mongo/flowbot

---
#########################################################
# Service for Moleculer API Gateway service
#########################################################
apiVersion: v1
kind: Service
metadata:
  name: api
spec:
  selector:
    app: api
  ports:
    - port: 3000
      targetPort: 3000

---
#########################################################
# Ingress for Moleculer API Gateway
#########################################################
apiVersion: networking.k8s.io/v1beta1
kind: Ingress
metadata:
  name: ingress
spec:
  rules:
    - host: flowbot.************.nip.io # Need change to right domain or right nip.io ip
      http:
        paths:
          - path: /
            backend:
              serviceName: api
              servicePort: 3000

---
#########################################################
# API Gateway service
#########################################################
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
spec:
  selector:
    matchLabels:
      app: api
  replicas: 2
  template:
    metadata:
      labels:
        app: api
    spec:
      containers:
        - name: api
          image: registry.gitlab.com/flowbot1/flowbot
          envFrom:
            - configMapRef:
                name: common-env
          env:
            - name: SERVICES
              value: api
            - name: PORT
              value: "3000"
          ports:
            - containerPort: 3000
      imagePullSecrets:
        - name: regcred

---
#########################################################
# Greeter service
#########################################################
apiVersion: apps/v1
kind: Deployment
metadata:
  name: greeter
spec:
  selector:
    matchLabels:
      app: greeter
  replicas: 2
  template:
    metadata:
      labels:
        app: greeter
    spec:
      containers:
        - name: greeter
          image: registry.gitlab.com/flowbot1/flowbot
          envFrom:
            - configMapRef:
                name: common-env
          env:
            - name: SERVICES
              value: greeter
      imagePullSecrets:
        - name: regcred

---
#########################################################
# Products service
#########################################################
apiVersion: apps/v1
kind: Deployment
metadata:
  name: products
spec:
  selector:
    matchLabels:
      app: products
  replicas: 2
  template:
    metadata:
      labels:
        app: products
    spec:
      containers:
        - name: products
          image: flowbot
          envFrom:
            - configMapRef:
                name: common-env
          env:
            - name: SERVICES
              value: products

---
#########################################################
# MongoDB server
#########################################################
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongo
  labels:
    app: mongo
spec:
  selector:
    matchLabels:
      app: mongo
  replicas: 1
  serviceName: mongo
  template:
    metadata:
      labels:
        app: mongo
    spec:
      containers:
        - image: mongo
          name: mongo
          ports:
            - containerPort: 27017
          resources: { }
          volumeMounts:
            - mountPath: /data/db
              name: mongo-data
      volumes:
        - name: mongo-data
          persistentVolumeClaim:
            claimName: mongo-data

---
#########################################################
# Persistent volume for MongoDB
#########################################################
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mongo-data
  labels:
    name: mongo-data
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Mi

---
#########################################################
# MongoDB service
#########################################################
apiVersion: v1
kind: Service
metadata:
  name: mongo
  labels:
    app: mongo
spec:
  ports:
    - port: 27017
      targetPort: 27017
  selector:
    app: mongo


---
#########################################################
# NATS transporter service
#########################################################
apiVersion: v1
kind: Service
metadata:
  name: nats
spec:
  selector:
    app: nats
  ports:
    - port: 4222
      name: nats
      targetPort: 4222

---
#########################################################
# NATS transporter
#########################################################
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nats
spec:
  selector:
    matchLabels:
      app: nats
  replicas: 1
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: nats
    spec:
      containers:
        - name: nats
          image: nats
          ports:
            - containerPort: 4222
              name: nats

---
#########################################################
# Redis service
#########################################################
apiVersion: v1
kind: Service
metadata:
  name: redis
spec:
  selector:
    app: redis
  ports:
    - port: 6379
      name: redis
      targetPort: 6379

---
#########################################################
# Redis server (transporter/cacher)
#########################################################
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
spec:
  selector:
    matchLabels:
      app: redis
  replicas: 1
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
        - name: redis
          image: redis:alpine
          ports:
            - containerPort: 6379
              name: redis





