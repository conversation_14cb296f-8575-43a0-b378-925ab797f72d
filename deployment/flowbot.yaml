version: '3.9'

x-environment: &global-environment
  NAMESPACE: ${NAMESPACE}
  LOGGER: ${LOGGER}
  LOGLEVEL: ${LOGLEVEL}
  SERVICEDIR: dist/services
  TRANSPORTER: ${TRANSPORTER}
  CACHER: ${CACHER}
  MONGO_URI: ${MONGO_URI}
  WEAVIATE_URL: ${WEAVIATE_URL}
  WEAVIATE_SCHEMA: ${WEAVIATE_SCHEMA}
  WEAVIATE_API_KEY: ${WEAVIATE_API_KEY}
  REST_BASE_URL: ${REST_BASE_URL}

services:
  api:
    image: registry.gitlab.com/flowbot1/flowbot/flowbot:latest
    environment:
      <<: *global-environment
      PROXY_IP_WHITELIST: ${PROXY_IP_WHITELIST}
      SERVICES: api
      PORT: 3000
    depends_on:
      - nats
    volumes:
      - 'prometheus_data:/app/monitoring/prometheus'
    labels:
      - traefik.enable=true
      - traefik.http.routers.api-gw.rule=PathPrefix(`/`)
      - traefik.http.services.api-gw.loadbalancer.server.port=3000
    networks:
      - internal
  openapi:
    image: registry.gitlab.com/flowbot1/flowbot/flowbot:latest
    environment:
      <<: *global-environment
      SERVICES: openapi
    depends_on:
      - api
      - nats
    networks:
      - internal
  config:
    image: registry.gitlab.com/flowbot1/flowbot/flowbot:latest
    environment:
      <<: *global-environment
      SERVICES: config
    depends_on:
      - nats
    networks:
      - internal
  telegram:
    image: registry.gitlab.com/flowbot1/flowbot/flowbot:latest
    environment:
      <<: *global-environment
      SERVICES: telegram
      TELEGRAM_SYSTEM_TOKEN: ${TELEGRAM_SYSTEM_TOKEN}
      TELEGRAM_SYSTEM_CHAT_ID: ${TELEGRAM_SYSTEM_CHAT_ID}
    depends_on:
      - nats
    networks:
      - internal
  user:
    image: registry.gitlab.com/flowbot1/flowbot/flowbot:latest
    environment:
      <<: *global-environment
      SERVICES: 'user,package,teamPackage'
    depends_on:
      - nats
    networks:
      - internal
  botChatAll:
    image: registry.gitlab.com/flowbot1/flowbot/flowbot:latest
    environment:
      <<: *global-environment
      SERVICES: 'botChat,botSetting,chatHistory'
    depends_on:
      - nats
    networks:
      - internal
  customerContact:
    image: registry.gitlab.com/flowbot1/flowbot/flowbot:latest
    environment:
      <<: *global-environment
      SERVICES: 'customerContact'
    depends_on:
      - nats
    networks:
      - internal
  vectorStore:
    image: registry.gitlab.com/flowbot1/flowbot/flowbot:latest
    environment:
      <<: *global-environment
      SERVICES: vectorStore
    volumes:
      - 'dataset_tmp_data:/app/tmp/dataset'
    depends_on:
      - nats
    networks:
      - internal
  model768:
    image: registry.gitlab.com/flowbot1/flowbot/flowbot-onnx:0.0.2
    environment:
      <<: *global-environment
      SERVICES: model768
    depends_on:
      - nats
    networks:
      - internal
  dataset:
    image: registry.gitlab.com/flowbot1/flowbot/flowbot:latest
    environment:
      <<: *global-environment
      SERVICES: dataset
    volumes:
      - 'dataset_tmp_data:/app/tmp/dataset'
    depends_on:
      - nats
    networks:
      - internal
  workGate:
    image: registry.gitlab.com/flowbot1/flowbot/flowbot:latest
    environment:
      <<: *global-environment
      SERVICES: workGate
    depends_on:
      - nats
    networks:
      - internal
  coreAi:
    image: registry.gitlab.com/flowbot1/flowbot/flowbot:latest
    environment:
      <<: *global-environment
      SERVICES: coreAi
    depends_on:
      - nats
    networks:
      - internal
  devTest:
    image: registry.gitlab.com/flowbot1/flowbot/flowbot:latest
    environment:
      <<: *global-environment
      SERVICES: devTest
    depends_on:
      - nats
    networks:
      - internal
  tokenUsage:
    image: registry.gitlab.com/flowbot1/flowbot/flowbot:latest
    environment:
      <<: *global-environment
      SERVICES: tokenUsage,tokenUsageDaily
    depends_on:
      - nats
    networks:
      - internal
  socketConnector:
    image: registry.gitlab.com/flowbot1/flowbot/flow-connector:latest
    environment:
      <<: *global-environment
      SERVICES: socketConnector
    depends_on:
      - nats
    networks:
      - internal
      - default
  zalo:
    image: registry.gitlab.com/flowbot1/flowbot/flow-connector:latest
    environment:
      <<: *global-environment
      SERVICES: zalo
    depends_on:
      - nats
    networks:
      - internal
      - default
  nats:
    image: 'nats:2'
    networks:
      - internal
    command:
      - '-m'
      - '8222'
  nats_exporter:
    image: 'natsio/prometheus-nats-exporter:latest'
    command:
      - '-varz'
      - 'http://nats:8222'
    networks:
      - internal
  traefik:
    image: 'traefik:v2.1'
    command:
      - '--api.insecure=true'
      - '--entryPoints.http.address=:80'
      - '--providers.docker=true'
      - '--providers.docker.exposedbydefault=false'
      - '--metrics.prometheus=true'
      - '--entryPoints.metrics.address=:8082'
      - '--metrics.prometheus.entryPoint=metrics'
    ports:
      - '127.0.0.1:3334:80'
      - '3001:8080'
    volumes:
      - '/var/run/docker.sock:/var/run/docker.sock:ro'
    networks:
      - internal
      - default
  jaeger:
    networks:
      - internal
      - default
    image: 'jaegertracing/all-in-one:latest'
    container_name: jaeger
    ports:
#      - '5775:5775/udp'
#      - '6831:6831/udp'
#      - '6832:6832/udp'
#      - '5778:5778'
      - '16686:16686'
#      - '14268:14268'
#      - '9411:9411'
    depends_on:
      - traefik
    environment:
      - QUERY_BASE_PATH=/jaeger
    labels:
      - traefik.enable=true
      - traefik.http.routers.jaeger.rule=PathPrefix(`/jaeger/`)
      - traefik.http.services.jaeger.loadbalancer.server.port=16686
      - traefik.http.routers.jaeger.middlewares=jaeger-auth
      - 'traefik.http.middlewares.jaeger-auth.basicauth.users=bizino:$apr1$NyrWx1Ol$o0a8r1npOF3pVgjMoBmDG.'
#  prometheus:
#    image: 'prom/prometheus:latest'
#    container_name: prometheus
#    volumes:
#      - './monitoring/prometheus/:/etc/prometheus/'
#      - 'prometheus_data:/prometheus'
#    command:
#      - '--config.file=/etc/prometheus/prometheus.yml'
#      - '--storage.tsdb.path=/prometheus'
#      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
#      - '--web.console.templates=/usr/share/prometheus/consoles'
##    ports:
##      - '9090:9090'
#    links:
#      - 'alertmanager:alertmanager'
#      - api
#    depends_on:
#      - traefik
#    networks:
#      - internal
#      - default
#    restart: always
#    labels:
#      - traefik.enable=true
#      - traefik.http.routers.prometheus.rule=PathPrefix(`/prometheus/`)
#      - traefik.http.services.prometheus.loadbalancer.server.port=9090
#  alertmanager:
#    image: 'prom/alertmanager:v0.15.3'
##    ports:
##      - '9093:9093'
#    volumes:
#      - './monitoring/alertmanager/:/etc/alertmanager/'
#    restart: unless-stopped
#    command:
#      - '--config.file=/etc/alertmanager/config.yml'
#      - '--storage.path=/alertmanager'
#  grafana:
#    image: grafana/grafana
#    container_name: grafana
#    user: '104'
#    depends_on:
#      - prometheus
#      - traefik
##    ports:
##      - '3005:3000'
#    volumes:
#      - 'grafana_data:/var/lib/grafana'
#    links:
#      - 'prometheus:prometheus'
#    networks:
#      - internal
#      - default
#    restart: always
#    environment:
#      - 'GF_SERVER_ROOT_URL=%(protocol)s://%(domain)s'
networks:
  internal: null
volumes:
  prometheus_data: null
  grafana_data: null
  dataset_tmp_data: null
