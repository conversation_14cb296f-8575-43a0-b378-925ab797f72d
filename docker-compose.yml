version: '3.8'

services:
  api:
    build:
      context: .
    image: flowbot
    env_file: docker-compose.env
    environment:
      SERVICES: api
      PORT: 3000
    depends_on:
      - nats
      - redis
    volumes:
      # Only need api service public moleculer node. Because other broadcast info to each
      - prometheus_data:/app/monitoring/prometheus
    labels:
      - 'traefik.enable=true'
      - 'traefik.http.routers.api-gw.rule=PathPrefix(`/`)'
      - 'traefik.http.services.api-gw.loadbalancer.server.port=3000'
    networks:
      - internal

  openapi:
    build:
      context: .
    image: flowbot
    env_file: docker-compose.env
    environment:
      SERVICES: openapi
    depends_on:
      - api
      - nats
      - redis
    networks:
      - internal
        
  config:
    build:
      context: .
    image: flowbot
    env_file: docker-compose.env
    environment:
      SERVICES: config
    depends_on:
      - mongo
      - nats
      - redis
    networks:
      - internal    
      
  telegram:
    build:
      context: .
    image: flowbot
    env_file: docker-compose.env
    environment:
      SERVICES: telegram
    depends_on:
      - mongo
      - nats
      - redis
    networks:
      - internal

  user:
    build:
      context: .
    image: flowbot
    env_file: docker-compose.env
    environment:
      SERVICES: user
    depends_on:
      - mongo
      - nats
      - redis
    networks:
      - internal

  mongo:
    image: mongo:4
    volumes:
      - data:/data/db
    networks:
      - internal
      
  botChatAll:
    build:
      context: .
    image: flowbot
    env_file: docker-compose.env
    environment:
      SERVICES: botChat,botSetting,chatHistory
    depends_on:
      - mongo
      - nats
      - redis
    networks:
      - internal
      
  vectorStore:
    build:
      context: .
    image: flowbot
    env_file: docker-compose.env
    environment:
      SERVICES: vectorStore
    depends_on:
      - nats
      - redis
    networks:
      - internal

  model768:
    image: flowbot-onnx # using special build for onnx
    env_file: docker-compose.env
    environment:
      SERVICES: model768
    depends_on:
      - nats
      - redis
    networks:
      - internal
 
  dataset:
    build:
      context: .
    image: flowbot
    env_file: docker-compose.env
    environment:
      SERVICES: dataset
    depends_on:
      - mongo
      - nats
      - redis
      - weaviate
    networks:
      - internal  

  coreAi:
    build:
      context: .
    image: flowbot
    env_file: docker-compose.env
    environment:
      SERVICES: coreAi
    depends_on:
      - nats
      - redis
    networks:
      - internal

  tokenUsage:
    build:
      context: .
    image: flowbot
    env_file: docker-compose.env
    environment:
      SERVICES: tokenUsage, tokenUsageDaily
    depends_on:
      - mongo
      - nats
      - redis
    networks:
      - internal

  devTest:
    build:
      context: .
    image: flowbot
    env_file: docker-compose.env
    environment:
      SERVICES: devTest
    depends_on:
      - nats
      - redis
    networks:
      - internal
        
  mongo_exporter:
    image: bitnami/mongodb-exporter:latest
    environment:
      MONGODB_URI: mongodb://mongo:27017
    command:
      - '--compatible-mode'
    networks:
      - internal

  nats:
    image: nats:2
    networks:
      - internal
    command:
      - '-m'
      - '8222'

  nats_exporter:
    image: natsio/prometheus-nats-exporter:latest
    command:
      - '-varz'
      - http://nats:8222
    networks:
      - internal

  redis:
    image: redis:alpine
    networks:
      - internal

  redis_exporter:
    image: oliver006/redis_exporter:alpine
    environment:
      REDIS_ADDR: redis://redis:6379
    networks:
      - internal

  traefik:
    image: traefik:v2.1
    command:
      - '--api.insecure=true' # Don't do that in production!
      - '--entryPoints.http.address=:80'
      - '--providers.docker=true'
      - '--providers.docker.exposedbydefault=false'
      - '--metrics.prometheus=true'
      - '--entryPoints.metrics.address=:8082'
      - '--metrics.prometheus.entryPoint=metrics'
    ports:
      - 3334:80
      - 3001:8080
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - internal
      - default

  # tracing container for all tracing reporting
  # includes hydra and microservices
  jaeger:
    networks:
      - internal
      - default
    image: jaegertracing/all-in-one:latest
    container_name: jaeger
    ports:
      - '5775:5775/udp'
      - '6831:6831/udp'
      - '6832:6832/udp'
      - '5778:5778'
      - '16686:16686'
      - '14268:14268'
      - '9411:9411'
    depends_on:
      - traefik
    environment:
      - QUERY_BASE_PATH=/jaeger
    labels:
      - 'traefik.enable=true'
      - 'traefik.http.routers.jaeger.rule=PathPrefix(`/jaeger/`)'
      - 'traefik.http.services.jaeger.loadbalancer.server.port=16686'
      # Add basic authen for protect jaeger service
      # echo $(htpasswd -nb hotdream1990 Admin@@123) | sed -e s/\\$/\\$\\$/g
      - "traefik.http.routers.jaeger.middlewares=jaeger-auth"
      - "traefik.http.middlewares.jaeger-auth.basicauth.users=hotdream1990:$$apr1$$HM88iVRm$$HrdqZcZhzriiJ3d5jFq7P."

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    volumes:
      - ./monitoring/prometheus/:/etc/prometheus/
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    ports:
      - 9090:9090
    links:
      - alertmanager:alertmanager
      - api
    depends_on:
      - traefik
    networks:
      - internal
      - default
    restart: always
    labels:
      - 'traefik.enable=true'
      - 'traefik.http.routers.prometheus.rule=PathPrefix(`/prometheus/`)'
      - 'traefik.http.services.prometheus.loadbalancer.server.port=9090'

  alertmanager:
    image: prom/alertmanager:v0.15.3
    ports:
      - '9093:9093'
    volumes:
      - ./monitoring/alertmanager/:/etc/alertmanager/
    restart: unless-stopped
    command:
      - '--config.file=/etc/alertmanager/config.yml'
      - '--storage.path=/alertmanager'

  weaviate:
    image: cr.weaviate.io/semitechnologies/weaviate:1.26.4
    container_name: weaviate
    environment:
      QUERY_DEFAULTS_LIMIT: 20
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
    volumes:
      - weaviate_data:/var/lib/weaviate
    networks:
      - internal
    healthcheck:
      test:
        - CMD
        - wget
        - '-q'
        - '--spider'
        - 'http://localhost:8080/v1/.well-known/ready'
      interval: 5s
      timeout: 30s
      retries: 10

  grafana:
    image: grafana/grafana
    container_name: grafana
    user: '104'
    depends_on:
      - prometheus
      - traefik
    ports:
      - 3005:3000
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning/:/etc/grafana/provisioning/
      - ./monitoring/grafana/plugins/:/var/lib/grafana/plugins/
    env_file:
      - ./monitoring/grafana/grafana.env
    links:
      - prometheus:prometheus
    networks:
      - internal
      - default
    restart: always
    environment:
      #      - GF_SERVER_ROOT_URL=%(protocol)s://%(domain)s/grafana
      - GF_SERVER_ROOT_URL=%(protocol)s://%(domain)s
  #    labels:
  #      - "traefik.enable=true"
  #      - "traefik.http.routers.grafana.rule=PathPrefix(`/grafana/`)"
  #      - "traefik.http.services.grafana.loadbalancer.server.port=3000"

networks:
  internal:

volumes:
  data:
  prometheus_data:
  grafana_data:
  weaviate_data:
