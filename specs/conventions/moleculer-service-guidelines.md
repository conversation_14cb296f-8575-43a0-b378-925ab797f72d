# Moleculer Service Implementation Guidelines

## Service Structure
Each service should follow this structure:
1. `services/[service-name]/[service-name].service.ts` - Main service implementation
2. `services/[service-name]/[service-name].service.types.ts` - Type definitions for actions and events
3. `models/[service-name].ts` - Mongoose model definition
4. `entities/[service-name].entity.ts` - Entity interface and constants

## Service Implementation Pattern
Follow this pattern based on existing services:

```typescript
@DService({
  name: '[service-name]',
  mixins: [
    ConfigMixin(['[service-name].**']),
    dbBaseMixin.getMixin()
  ],
  settings: {
    // Service-specific settings
  }
})
class [ServiceName]Service extends MoleculerDBService<SettingsType, ModelType>
  implements Omit<MoleculerTs.GetServiceOwnActions<OwnActions>, DbBaseMixinActionTypes> {

  // Service implementation
}
```

## Key Components

### 1. Actions

#### Implementation Pattern
Follow this pattern for action implementation:

```typescript
@Action({
  rest: 'POST /create',
  params: {
    // Parameter definitions
  },
  cache: {
    keys: ['#user.teamId', 'name'],
    ttl: 60 * 30
  },
  openapi: {
    description: 'Create new resource'
  }
})
async actionCreate(
  ctx: Context<ServiceTypes.ActionParams<'actionCreate'>, ApiGatewayMeta>,
): Promise<ServiceTypes.ActionReturn<'actionCreate'>> {
  // Implementation
}
```

#### Key Guidelines
- Use `@Action` decorator for REST endpoints
- Define action parameters and return types in service.types.ts
- Use strict parameter validation with `$$strict: true`
- Implement proper error handling using CommonErrors
- Use caching where appropriate with proper keys and TTL
- Document endpoints using OpenAPI annotations
- Use proper typing for Context and return values
- Follow RESTful principles for endpoint design
- Implement proper input validation
- Use async/await for asynchronous operations
- Handle errors gracefully and provide meaningful error messages
- Use proper logging for debugging and monitoring
- Implement proper security checks (authentication, authorization)
- Use consistent naming conventions for actions

Example:
```typescript
@Action({
  rest: 'POST /create',
  params: {
    name: 'string',
    type: 'number'
  },
  cache: {
    keys: ['#user.teamId', 'name'],
    ttl: 60 * 30
  },
  openapi: {
    description: 'Create new resource'
  }
})
async createResource(ctx: Context<CreateParams>) {
  // Implementation
}
```

### 2. Events
- Use `@Event` decorator for event handlers
- Define event types in service.types.ts
- Implement cache cleaning patterns
- Use `eventName()` helper for event naming
- For multiple events that share the same handler, use multiple @Event decorators on a single method
- Common use case is to handle cache cleaning for create/update/remove events on the same method

Example:
```typescript
@Event({
  name: eventName('resource.created')
})
async handleResourceCreated(payload: ResourceEvent) {
  // Handle event
}
```

### 3. Mixins
- Use ConfigMixin for configuration
- Use DbBaseMixin for database operations
- Implement custom mixins if needed

### 4. Caching
- Use Moleculer caching with proper keys
- Implement cache cleaning on data changes
- Set appropriate TTL values
- Clean cache on create/update/delete operations

### 5. Security
- Use `@RequireRoles()` decorator for role-based access control
- Validate all input parameters
- Sanitize output data
- Use proper error handling
- Encrypt sensitive data

## Model Implementation Guidelines

### 1. Base Model Structure
All models should extend BaseModel and follow this pattern:

```typescript
import { plugin, pre, prop } from '@typegoose/typegoose';
import { Field, getSchema, Number, Schema, String as IsString } from 'fastest-validator-decorators';
import { BaseModel, basePreSave } from './base.model';
import * as mongoose from 'mongoose';
import mongooseLeanId from '../utils/mongoPlugins/mongoLeanId';
import paginate from '../utils/mongoPlugins/paginate';

@Schema()
@pre<ModelName>('save', function (): Promise<void> {
  return basePreSave(this);
})
@plugin(paginate, { sort: '-created' })
@plugin(mongooseLeanId)
export class ModelName extends BaseModel implements IEntity {
  @prop({ default: uuidv4, unique: true, required: true })
  @IsString()
  public _id: string = null;

  // Add model fields here following the pattern:
  @prop({ default: '' })
  @IsString()
  public fieldName: string;

  // Timestamps are handled by BaseModel
}
```

### 2. Required Plugins
- Use `@plugin(paginate)` for pagination support
- Use `@plugin(mongooseLeanId)` for lean query support
- Use `@pre('save')` hook for pre-save operations

### 3. Field Decorators
- Use `@prop()` for Mongoose schema definitions
- Use `@Field()` from fastest-validator-decorators for validation
- Use appropriate type decorators:
  - `@IsString()` for string fields
  - `@Number()` for number fields
  - `@Boolean()` for boolean fields

### 4. Schema Export
Export the schema and type definitions:

```typescript
export const ModelNameSchema = getSchema(ModelName);
export type ModelNameType = ModelName & mongoose.Document;
```

### 5. Entity Implementation
- Create corresponding entity interface in entities folder
- Define all required constants and types
- Use consistent naming conventions

### Database Operations
- Use Mongoose for database interactions
- Implement proper schema validation
- Use pagination for list operations
- Implement soft delete pattern where appropriate
- Use transactions for complex operations

## Error Handling
- Use CommonErrors for consistent error responses
- Implement proper error logging
- Handle database errors gracefully
- Validate all inputs before processing

## Testing Guidelines
- Write unit tests for all actions
- Test error scenarios
- Verify cache behavior
- Test event handling
- Test security constraints
- Test database operations

## Documentation
- Document all actions with OpenAPI annotations
- Include parameter descriptions
- Document return types
- Document error responses
- Include examples where applicable

## Performance Considerations
- Implement proper indexing
- Use caching where appropriate
- Optimize database queries
- Implement pagination for large datasets
- Use async/await properly

## Service Lifecycle
- Implement `created()` for initial setup
- Use `started()` for service initialization
- Implement proper cleanup in `stopped()`
- Use `waitForServices()` for service dependencies

## Best Practices
- Keep actions focused and single-purpose
- Use proper typing throughout
- Implement proper validation
- Use consistent naming conventions
- Follow RESTful principles
- Implement proper error handling
- Use caching appropriately
- Document all public interfaces
